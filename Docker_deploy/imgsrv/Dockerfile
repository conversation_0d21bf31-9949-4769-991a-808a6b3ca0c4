#1、zpass要求 /home/<USER>
#2、主站在基础镜像（alios）中指定了entrypoint，要求后续镜像不能使用CMD或者entrypoint，需要将相关命令从dockerfile中删除。
#3、docker在镜像化的过程中，需要关闭puppet服务，要不然就会同步到主站的nagnagiosbin/asa的目录。需要再dockerfile里面添加一行：
#RUN chkconfig --level 2345 puppet off
#4、主站要求有如下四个脚本：
#/home/<USER>/nagiosbin/asa/pubcore.sh 程序启动，docker第一次运行时会从这里拉任务
#/home/<USER>/nagiosbin/asa/checkservice.sh 运行状态检查，程序没有运行需要exit非0
#/home/<USER>/nagiosbin/asa/off_service.sh 程序关闭
#/home/<USER>/deploy.sh 程序重启

# Use an official Python runtime as a parent image
FROM reg.docker.alibaba-inc.com/alipay/alios7u2-min:1.0-8eab639-20171023

MAINTAINER chunhuan.wch <EMAIL>

# Copy the current directory contents into the container at /appinstall
ADD ./packages/imgsrv.tar.gz /home/<USER>/
ADD --chown=admin:admin ./base_shell/pubcore.sh /home/<USER>/nagiosbin/asa/
ADD --chown=admin:admin ./base_shell/checkservice.sh /home/<USER>/nagiosbin/asa/
ADD --chown=admin:admin ./base_shell/off_service.sh /home/<USER>/nagiosbin/asa/
ADD --chown=admin:admin ./base_shell/deploy.sh /home/<USER>/

RUN yum install java-1.8.0-openjdk.x86_64 -y; yum clean all
# 升级gcc
RUN yum install alios7u-2_24-gcc-8-repo.noarch -y; yum clean all
RUN yum install gcc-8.3.1-1.alios7.x86_64 -y; yum clean all
RUN yum -y update libstdc++
# 安装python3和相关环境
RUN rpm --rebuilddb && yum install python3 -y
RUN rpm --rebuilddb && yum install python3-devel -y
RUN rpm --rebuilddb && yum install libjpeg-devel -y
RUN pip3 install pillow -i https://mirrors.aliyun.com/pypi/simple/
RUN pip3 install numpy -i https://mirrors.aliyun.com/pypi/simple/

# 自研avif安装
RUN chmod -R +x /home/<USER>/imgsrv/third/self_avif

RUN chown admin:admin -R /home/<USER>/imgsrv
RUN chown admin:admin -R /home/<USER>/nagiosbin
RUN chown admin:admin -R /home/<USER>/logs
RUN chmod -R +x /home/<USER>/imgsrv/bin
RUN chmod +x /home/<USER>/imgsrv/imgsrv_control.sh
RUN chmod -R +x /home/<USER>/imgsrv/shell
RUN chmod -R +x /home/<USER>/imgsrv/third/bin
RUN chkconfig --level 2345 puppet off