#!/bin/sh
#Author: wen<PERSON> ch<PERSON><EMAIL>

imgsrv="imgsrv"

sudo sh -c "echo 8192 > /proc/sys/net/core/somaxconn"
sudo sh -c "chown -R admin:admin /home/<USER>/logs"

rm_puppet_certs() {
    puppet_cert_path="/var/lib/puppet/ssl/certs/ca.pem"
    if [ -f ${puppet_cert_path} ]
    then
        sudo mv ${puppet_cert_path} /tmp/ca.pem.bak
    fi
}

antvip_conf() {
    file=/home/<USER>/conf/server.conf
    if [ -d "/home/<USER>/conf" ]
    then
        sudo chown admin:admin -R /home/<USER>/conf
        if [ ! -f $file ]
        then
            echo "zone="$ALIPAY_ZONE > $file
        fi
    else
        mkdir /home/<USER>/conf
        echo "zone="$ALIPAY_ZONE > $file
    fi

    soft_link=/home/<USER>/server.conf

    if [ -L $soft_link ]
    then
        target=`ls -l $soft_link | awk '{print $NF}'`
        if [ "$taget" != "file" ]
        then
            rm -rf $soft_link
            ln -s $file $soft_link
        fi
    else
        ln -s $file $soft_link
    fi
}

rm_puppet_certs
antvip_conf

cd /home/<USER>/imgsrv/third/self_avif/
sudo sh ./download.sh

sleep 1
cd /home/<USER>/imgsrv
./imgsrv_control.sh start
sleep 1
ret=`./imgsrv_control.sh status | grep -c running`
if [ ${ret} -eq 2 ]
then
    echo "${imgsrv} started successfully!"
else
    echo "${imgsrv} started failed!"
    exit 1
fi

exit 0
