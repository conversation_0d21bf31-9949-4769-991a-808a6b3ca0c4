#!/bin/bash
#Author: wen<PERSON> <EMAIL>

bin_name="imgsrv"

if [ $# -ne 2 ]; then
    echo "Usage: $0 [LINKB_WORKSPACE imageTag]"
    exit 1
fi

LINKB_WORKSPACE=$1
TAG=$2

cd $LINKB_WORKSPACE
mkdir build && cd build
cmake ..
JOBS=`grep -c ^processor /proc/cpuinfo 2>/dev/null`
make -j${JOBS}

if [ ! -f ${bin_name} ]; then
    echo "build failed!"
    exit 1
fi

mkdir -p $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/bin
\cp -fr ${bin_name} $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/bin/

mkdir -p $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/conf
cp $LINKB_WORKSPACE/conf/imgsrv.conf $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/conf/
cp $LINKB_WORKSPACE/shell/imgsrv_control.sh $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/

mkdir -p $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/shell
cp $LINKB_WORKSPACE/shell/imgsrv_log_recycle.sh $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/shell/
cp $LINKB_WORKSPACE/shell/imgsrv_monitor.sh $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/shell/
cp $LINKB_WORKSPACE/shell/imgsrvgm_log_recycle.sh $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/shell/
cp -r $LINKB_WORKSPACE/third $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages/imgsrv/

cd $LINKB_WORKSPACE/Docker_deploy/imgsrv/packages

package_name=${bin_name}.tar.gz
tar -czvf ${package_name} imgsrv

