environments:
  imageTag: 1.2.0-beta.202104141438

version: "1.5"

stages:
  - 镜像构建
  - 推进预发
  - 预发部署
  - 推进生产
  - 生产部署

镜像构建:
  stage: 镜像构建
  plugin: ANT-BUILD
  passEnv: true
  pluginConfig:
    image: reg.docker.alibaba-inc.com/antmm/mmtc_builder:0.0.4
    environments:
      tag: ${ACI_COMMIT_SHA}
    beforeScript:
      - pwd
      - whoami
    script:
      - ls -al
      - cd $LINKB_WORKSPACE/Docker_deploy/imgsrv
      - sh ./build.sh ${LINKB_WORKSPACE} ${ACI_COMMIT_SHA}
    inputs:
      params:
        - name: DIRECTORY
          value: ./Docker_deploy/imgsrv
        - name: DOCKERFILE
          value: ./Docker_deploy/imgsrv/Dockerfile
    outputs:
      - name: imgsrv
        type: image
        repository: reg.docker.alibaba-inc.com
        namespace: antmm
        tag: ${ACI_COMMIT_SHA}
        desc: "this is the image for imgsrv"
  except:
    triggerType:
      - push
      - pullRequest
      - tagPush


推进预发:
  stage: 推进预发
  plugin: MANUAL-APPROVAL
  pluginConfig:
    env: 'PRE'
    linkallStage: 'PRE'

预发部署:
  stage: 预发部署
  component: jiuzhou-deploy
  inputs:
    # 租户, 默认为 alipay 租户, 可不填
    tenantName: "alipay"
    # 应用名称
    appName: "mmtcimgsvr"
    # 部署环境，枚举值：pre（预发）,sim（仿真）,gray（灰度）,prod（生产）
    env: "pre"
    # 自定义镜像 tag
    image: ${{parameters.imgsrv}}
    config:
      beforeExecute:
        confirm:
          buttonName: 确认预发部署

推进生产:
  stage: 推进生产
  plugin: MANUAL-APPROVAL
  pluginConfig:
    env: 'PROD'
    linkallStage: 'PROD'

生产部署:
  stage: 生产部署
  component: jiuzhou-deploy
  inputs:
    # 租户, 默认为 alipay 租户, 可不填
    tenantName: "alipay"
    # 应用名称
    appName: "mmtcimgsvr"
    # 部署环境，枚举值：pre（预发）,sim（仿真）,gray（灰度）,prod（生产）
    env: "prod"
    # 自定义镜像
    image: ${{parameters.imgsrv}}