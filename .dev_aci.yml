environments:
  imageTag: 1.2.0-beta.202104141438

version: "1.5"

stages:
  - 镜像构建
  - 线下部署

镜像构建:
  stage: 镜像构建
  plugin: ANT-BUILD
  passEnv: true
  pluginConfig:
    image: reg.docker.alibaba-inc.com/antmm/mmtc_builder:0.0.3
    environments:
      tag: ${ACI_COMMIT_SHA}
    beforeScript:
      - pwd
      - whoami
    script:
      - ls -al
      - cd $LINKB_WORKSPACE/Docker_deploy/imgsrv
      - sh ./build.sh ${LINKB_WORKSPACE} ${ACI_COMMIT_SHA}
    inputs:
      params:
        - name: DIRECTORY
          value: ./Docker_deploy/imgsrv
        - name: DOCKERFILE
          value: ./Docker_deploy/imgsrv/Dockerfile
    outputs:
      - name: imgsrv
        type: image
        repository: reg.docker.alibaba-inc.com
        namespace: antmm
        tag: ${ACI_COMMIT_SHA}
        desc: "this is the image for imgsrv"
  except:
    triggerType:
      - push
      - pullRequest
      - tagPush


线下部署:
  stage: 线下部署
  component: happydeploy
  # confirm:
  #   type: manul
  #   buttonName: 开始部署
  #   approvers:
  #     - albert.zmz
  inputs:
    appName: mmtcimgsvr
    envName: STABLE 
    image: ${{parameters.imgsrv}}
    extraRequest:
      readinessProbe:
        exec:
          command:
            - /bin/bash
            - '-c'
            - echo "health" >> /tmp/health
        initialDelaySeconds: 3
        periodSeconds: 10
        failureThreshold: 3
        timeoutSeconds: 10
      lifecycle:
        postStart:
          exec:
            command:
              - /bin/bash
              - '-c'
              - echo "123" >> /tmp/log