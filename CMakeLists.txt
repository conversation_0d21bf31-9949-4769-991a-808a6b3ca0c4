cmake_minimum_required(VERSION 3.13)
set(CMAKE_VERBOSE_MAKEFILE ON)
project(imgsrv)

set(CMAKE_CXX_STANDARD 11)
set(TARGET_EXE imgsrv)

include_directories(/usr/local/include
#        /usr/local/include/opencv
        third/lib/opencv/include
        third/lib/opencv/include/opencv
        /usr/local/ssl/include
        src/util
        third/lib/sal)
file(GLOB_RECURSE source_files "src/*.cpp")
link_directories(/usr/local/lib
        /usr/local/ssl/lib/
        /usr/lib64/
        third/lib/sal
        third/lib/opencv/lib
        third/lib/gm/lib
        third/lib/common)
add_compile_options(-g -Wall)

add_executable(imgsrv ${source_files})
#target_compile_definitions(${TARGET_EXE} PRIVATE BOOST_ASIO_ENABLE_HANDLER_TRACKING)
target_link_libraries(${TARGET_EXE}
        liblogger.a
        libantvip_client.a
        libboost_program_options.a
        libboost_filesystem.a
        libprotobuf.a
        libconfig++.a
        libcurl.a
        libboost_thread.a
        libboost_regex.a
        regionBySaliency
        libface_detect_track.a
        ssl
        crypto
        pthread
        idn
        ldap
        rt
        z
)

set(CMAKE_INSTALL_PREFIX ./output)
install (TARGETS ${TARGET_EXE} DESTINATION ${TARGET_EXE}/bin)
install (DIRECTORY conf DESTINATION ${TARGET_EXE})
install (PROGRAMS shell/imgsrv_control.sh DESTINATION ${TARGET_EXE})
install (PROGRAMS shell/imgsrv_log_recycle.sh DESTINATION ${TARGET_EXE}/shell)
install (PROGRAMS shell/imgsrvgm_log_recycle.sh DESTINATION ${TARGET_EXE}/shell)
#install (PROGRAMS shell/imgsrv_stat_recycle.sh DESTINATION ${TARGET_EXE}/shell)
install (PROGRAMS shell/imgsrv_monitor.sh DESTINATION ${TARGET_EXE}/shell)
install (DIRECTORY third DESTINATION ${TARGET_EXE}
        PATTERN "third/bin/*"
        PERMISSIONS OWNER_EXECUTE OWNER_WRITE OWNER_READ
        GROUP_EXECUTE GROUP_READ WORLD_EXECUTE WORLD_READ)
