#Configuration file for imgsrv
version = "1.0";

imgsrv:
{
    server: {
        thread_num = 32;
        backlog = 4096;
        port = 7999;
        //ms
        default_time_out = 15000;

    }

    out_file_path = "/home/<USER>/imgsrv/tmp";
    third_path = "/home/<USER>/imgsrv/third"

    worker:
    {
        thread_num = 64;
        process_num = 16;
    }
    logger:
    {
        #1:DEBUG; 2:INFO; 3:WARN; 4:ERROR; 5:FATAL
        log_level = 1;
        log_path = "/home/<USER>/logs/imgsrv/log/imgsrv.log";
        stat_path = "/home/<USER>/logs/imgsrv/stat/imgsrv_stat.log"
        ce_log_path = "/home/<USER>/logs/imgsrv/log/ce.log"
        gmstat_path = "/home/<USER>/logs/imgsrv/stat/gm_stat.log"
    }
    afts:
    {
        # host_names = ["mmtcaftsdw-pool.gz00c.alipay.com", "mmtcaftsdw-pool.gz00d.alipay.com"];
        host_names = ["mmtcaftsdw-pool.stable.alipay.net", "mmtcaftsdw-pool.stable.alipay.net"];

        antvip:
        {
            //线上：antvip-pool
            //预发：antvip-pool.cz99s.alipay.com
            //stable: antvip-pool
            //sit: antvip-pool
            antvip_cloud_inc = "cloudinc-pool";
            antvip_log = "/home/<USER>/logs/imgsrv/log/antvip.log";
            //1:DEBUG; 2:INFO; 3:WARN; 4:ERROR; 5:FATAL
            antvip_log_level = 1;
            # antvip_afts_domain_name = "mmtcaftsdw-pool.global.alipay.com";
            antvip_afts_domain_name = "mmtcaftsdw-pool.stable.alipay.net";
        }
        //seconds
        dns_refresh_interval = 5;
        resolve_retry = 3;
    }
    processer:
    {
        //0,1,2
        gm_zoom_type = 0;
        hevc_thread_num = 2;
    }

    debug:
    {
        rm_tmp_dir = true;
    }
};
