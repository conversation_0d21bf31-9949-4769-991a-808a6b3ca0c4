Changes to imgsrv 1.2.0-beta.202104141438
    - Feature: support audio format conversion

Changes to imgsrv 1.1.1-beta.202009281127
    - Bug fix: 修复图片水印底图和logo图相同时导致处理失败的情况
    - Bug fix: 部分图片[点击下载]智能裁剪crash，因为解码失败没有catch。
    - Bug fix: 部分图片[相关图片下载]解码是gm crash。
    - Bug fix: 修复 gm stat日志无法通过xflush系统清洗，因为日志不是以时间开头的。
    - 算法调整：QP调整，在主观变化不大的情况下，降低压缩质量，提高压缩比例。
    - Bug fix：修复老机型<6v输出<128x128的AHP图，导致IOS概率性卡死的问题。

Changes to imgsrv 1.1.0-beta.202008181717
    - Feature: Add Antvip
    - Bug fix: 修复带角度的图片缩放时，是先缩放再旋转，导致尺寸不对。改成先旋转再缩放。

Changes to imgsrv *******-beta.202006301604
    - Bug fix: gm 二进制修复内容：1.ahp3 出现的mismatch降级问题修复。2. hevc 编码算法升级，提高压缩率

Changes to imgsrv 1.0.8-beta.202006301604
    - Bug fix: 智能裁剪失败，降级zoom2的管道逻辑不对的问题修复。
    - Bug fix: 兼容.jpeg/300w_300h这种参数，当前这种参数失败的case 达上百qpm。
    - Bug fix: 修复uritype=url这种请求的bug

Changes to imgsrv 1.0.7-beta.202005111812
    - Bug fix: Add state machine of http reply phases to avoid multiple
    replies on one session in some rare cases
    - Bug fix: change sub imgproc request to sub file request when
    "zoom=original" param is set in water mark http request
    GM:
    1.[功能]动图全面支持，首页引入口碑(apng)和手淘(awebp)的动图后，因端上不支持解码，需要服务端转成可支持格式ahp3/gif。
    当前ahp3编解码/gif编解码/awebp解码已有，新增 apng编解码，awebp编码，实现四种格式的任意互转。
    2. 增加状态埋点日志。stat/gm_stat.log
    3. AHP头部增加对比文件大小埋点。
    ref:
    https://aone.alipay.com/req/27454442

Changes to imgsrv 1.0.6-beta.202004211706
    - [功能]zoom=[n]v 透传到gm，v范围支持[0-1000]，对于<6v的，输出确保安卓老版本兼容(无奇数，>128x128)。【与DW联调】
    - [BUG]兼容tfs缩放参数 + 客户端前后缀组合失败的情况：6v_80q_150x150xz.JPEG_df.ahp2
    - [BUG]修复智能裁剪人脸depth=16位png图core。
    - [BUG]修复智能裁剪 + 动态格式时，动态格式无效果问题。
    6068f40de9e80e878148f6cd681e643b  bin/gm --> alipay.1.0.1.20200421
    dba3afc1995ed088c09049af3ed88312   bin/FalconIntelligentCut.bin
    cc5d1110ad8cb56842ff833d0c8e0716     lib/libregionBySaliency.so

Changes to imgsrv 1.0.5-beta.202004021419
    - Bug fix: Fix ".webp" param in zoom2 parsing error
    - Bug fix: Check emptiness for downgrade zoom2 param
    - Optimization: move opencv dependency to third directory

Changes to imgsrv 1.0.4-beta.202001021121
    - Bug fix: Fix conditional variable problem
    - Bug fix: Downgrade zoom param bug fix
    - Bug fix: Fix params-in-pipe problem
    - Optimization: gm Performance improvement

Changes to imgsrv 1.0.3-beta.************
    - Modify: Add bizType when requests to origin server(AFTS)

Changes to imgsrv 1.0.2-beta.************
    - Modify: Change server port to 7999
    - Modify: Change dns cache to 5 seconds

Changes to imgsrv 1.0.1-beta.************
    - Bug fix: Add library path to LD_LIBRARY_PATH for command executing

Changes to imgsrv 1.0.0-beta.************
    - Feature: image process(zoom, crop, convert...), ref:https://yuque.antfin-inc.com/django/user_guide/wlf6bd
    - Feature: image water mark, image text mark
