/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : url.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_URL_H
#define IMGSRV_URL_H

#include <string>
#include <map>

namespace imgsrv {

class Url {
public:
    Url();
    explicit Url(const std::string& url);

    void Parse(const std::string& url);

    void ParseQuery(const std::string& query, std::map<std::string, std::string>& params);

    void ParseUri(const std::string& uri);

    void Reset();

    Url& operator= (const Url& other);

public:
    const std::string& GetOriginalUrl() const;

    const std::string& GetDecodedUrl() const;

    const std::string& GetProtocol() const;

    const std::string& GetHost() const;

    const std::string& GetPort() const;

    const std::string& GetPath() const;

    const std::string& GetQuery() const;

    const std::string& GetFragment() const;

    const std::map<std::string, std::string>& GetParams() const;


    static bool UrlDecode(const std::string& in, std::string& out);
    static bool UrlEncode(const std::string& in, std::string& out);

private:
    static unsigned char ToHex(unsigned char x);
    static bool FromHex(unsigned char in, unsigned char * out);
    std::string original_url_;
    std::string decoded_url_;
    std::string protocol_;
    std::string host_;
    std::string port_;
    std::string path_;
    std::string query_;
    std::string fragment_;
    std::map<std::string, std::string> params_;
};
}


#endif //IMGSRV_URL_H
