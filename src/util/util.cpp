/*===============================================================
* Copyright (C) 2019 All rights reserved.
*   
* @file     : util.cpp
* <AUTHOR> wen<PERSON>(<EMAIL>)
* @date     : 2019-07-31
* @version  : 
* @brief    : 
*
* @details  : 
================================================================*/
#include <ftw.h>
#include <sys/resource.h>
#include <iostream>
#include <cmath>
#include "util.h"
#include <string.h>
#include <boost/date_time/microsec_time_clock.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <openssl/evp.h>
#include <fstream>
#include <sys/stat.h>
#include <dirent.h>

void imgsrv::Util::EnableLimit() {
    struct rlimit climit;
    climit.rlim_cur = RLIM_INFINITY;
    climit.rlim_max = RLIM_INFINITY;
    if (setrlimit(RLIMIT_CORE, &climit) < 0) {
        std::cout << "setrlimit: " << strerror(errno);
    }
}

int64_t imgsrv::Util::GetTimeStamp() {
    boost::posix_time::ptime epoch(boost::gregorian::date(1970, boost::gregorian::Jan, 1));
    boost::posix_time::time_duration time_from_epoch =
            boost::posix_time::microsec_clock::universal_time() - epoch;
    return time_from_epoch.total_milliseconds();
}

bool imgsrv::Util::Aes256CbcDecrypt(
        const std::string& key,
        const std::string& iv,
        const std::vector<unsigned char>& ctext,
        std::vector<char>& rtext) {
    using EVP_CIPHER_CTX_free_ptr = std::unique_ptr<EVP_CIPHER_CTX, decltype(&::EVP_CIPHER_CTX_free)>;
    if (key.size() != 32) {
//        LOG(ERROR) << "aes_256_cbc key size = " << key.size();
        return false;
    }
    EVP_CIPHER_CTX_free_ptr ctx(EVP_CIPHER_CTX_new(), ::EVP_CIPHER_CTX_free);
    int rc = EVP_DecryptInit_ex(ctx.get(), EVP_aes_256_cbc(), NULL,
                                (const unsigned char*)key.c_str(),
                                (const unsigned char*)iv.c_str());
    if (rc != 1) {
//        LOG(ERROR) << "EVP_DecryptInit_ex failed";
        return false;
    }

    // Recovered text contracts upto BLOCK_SIZE
    rtext.resize(ctext.size());
    int out_len1 = (int)rtext.size();

    rc = EVP_DecryptUpdate(ctx.get(), (unsigned char*)&rtext[0], &out_len1,
                           &ctext[0], (int)ctext.size());
    if (rc != 1) {
//        LOG(ERROR) << "EVP_DecryptUpdate failed";
        return false;
    }

    int out_len2 = (int)rtext.size() - out_len1;
    rc = EVP_DecryptFinal_ex(ctx.get(), (unsigned char*)(&rtext[0]+out_len1), &out_len2);
    if (rc != 1) {
//        LOG(ERROR) << "EVP_DecryptFinal_ex failed";
        return false;
    }

    // Set recovered text size now that we know it
    rtext.resize(out_len1 + out_len2);
    return true;
}

bool imgsrv::Util::Aes256CbcDecryptFile(
        const std::string& key,
        const std::string& in_file,
        const std::string& out_file) {
    std::ifstream is(in_file, std::ios::in | std::ios::binary);
    if (!is) {
        return false;
    }
    is.seekg (0, is.end);
    int length = is.tellg();
    is.seekg (0, is.beg);
    std::vector<unsigned char> in;
    std::vector<char> out;
    in.resize(length);
    is.read((char *)&in[0], length);
    std::string iv(16, 0);
    bool rst = Aes256CbcDecrypt(key, iv, in, out);
    is.close();
    if (!rst) {
        return false;
    }

    if (in_file == out_file) {
        unlink(in_file.c_str());
    }
    std::ofstream ofs(out_file, std::ios::out | std::ios::binary);
    if(!ofs) {
        return false;
    }

    ofs.write(out.data(), out.size());
    ofs.close();
    return true;
}

bool imgsrv::Util::Aes256CbcEncrypt(
        const std::string& key,
        const std::string& iv,
        const std::vector<unsigned char>& ptext,
        std::vector<char>& ctext) {
    using EVP_CIPHER_CTX_free_ptr = std::unique_ptr<EVP_CIPHER_CTX, decltype(&::EVP_CIPHER_CTX_free)>;
    if (key.size() != 32) {
//        LOG(ERROR) << "aes_256_cbc key size = " << key.size();
        return false;
    }
    EVP_CIPHER_CTX_free_ptr ctx(EVP_CIPHER_CTX_new(), ::EVP_CIPHER_CTX_free);
    int rc = EVP_EncryptInit_ex(ctx.get(), EVP_aes_256_cbc(), NULL,
                                (const unsigned char*)key.c_str(),
                                (const unsigned char*)iv.c_str());
    if (rc != 1) {
//        LOG(ERROR) << "EVP_EncryptInit_ex failed";
        return false;
    }

    // Recovered text expands upto BLOCK_SIZE
    ctext.resize(ptext.size() + iv.size());
    int out_len1 = (int)ctext.size();

    rc = EVP_EncryptUpdate(ctx.get(), (unsigned char*)&ctext[0], &out_len1,
                           &ptext[0], (int)ptext.size());
    if (rc != 1) {
//        LOG(ERROR) << "EVP_EncryptUpdate failed";
        return false;
    }

    int out_len2 = (int)ctext.size() - out_len1;
    rc = EVP_EncryptFinal_ex(ctx.get(), (unsigned char*)(&ctext[0]+out_len1), &out_len2);
    if (rc != 1) {
//        LOG(ERROR) << "EVP_EncryptFinal_ex failed";
        return false;
    }
    // Set cipher text size now that we know it
    ctext.resize(out_len1 + out_len2);
    return true;
}

bool
imgsrv::Util::Aes256CbcEncryptFile(
        const std::string& key,
        const std::string& in_file,
        const std::string& out_file) {
    std::ifstream is(in_file, std::ios::in | std::ios::binary);
    if (!is) {
        return false;
    }
    is.seekg (0, is.end);
    int length = is.tellg();
    is.seekg (0, is.beg);
    std::vector<unsigned char> in;
    std::vector<char> out;
    in.resize(length);
    is.read((char *)&in[0], length);
    std::string iv(16, 0);
    bool rst = Aes256CbcEncrypt(key, iv, in, out);
    is.close();
    if (!rst) {
        return false;
    }
    if (in_file == out_file) {
        unlink(in_file.c_str());
    }
    std::ofstream ofs(out_file, std::ios::out | std::ios::binary);
    if(!ofs) {
        return false;
    }

    ofs.write(out.data(), out.size());
    ofs.close();
    return true;
}

void imgsrv::Util::RemoveUnsafeChar(std::string& text) {
    StringReplace(text, "`", "");
    StringReplace(text, "'", "");
    StringReplace(text, "\"", "");
    StringReplace(text, ")", "");
    StringReplace(text, "(", "");
    StringReplace(text, ">", "");
    StringReplace(text, "<", "");
    StringReplace(text, ";", "");
    StringReplace(text, ".", "");
    StringReplace(text, "&", "");
    StringReplace(text, "|", "");
    StringReplace(text, ",", "");
    StringReplace(text, "$", "");
}

void imgsrv::Util::StringReplace(std::string& s1, const std::string s2, const std::string s3) {
    std::string::size_type pos=0;
    std::string::size_type a=s2.size();
    std::string::size_type b=s3.size();
    while((pos=s1.find(s2,pos)) != std::string::npos) {
        s1.replace(pos,a,s3);
        pos += b;
    }
}

void imgsrv::Util::Split(const std::string& str, char sep,
                         std::vector<std::string>* pieces) {
    pieces->clear();
    if (str.empty()) {
        return;
    }
    size_t pos = 0;
    size_t next = str.find(sep, pos);
    while (next != std::string::npos) {
        pieces->push_back(str.substr(pos, next - pos));
        pos = next + 1;
        next = str.find(sep, pos);
    }
    if (!str.substr(pos).empty()) {
        pieces->push_back(str.substr(pos));
    }
}

void imgsrv::Util::Split(const std::string& str, const std::string seps, std::vector<std::string>* pieces) {
    pieces->clear();
    if (str.empty()) {
        return;
    }
    size_t pos = 0;

    size_t next = std::string::npos;
    for (char sep : seps)
        next = std::min(str.find(sep, pos), next);

    while (next != std::string::npos) {
        pieces->push_back(str.substr(pos, next - pos));
        pos = next + 1;
        next = std::string::npos;
        for (char sep : seps)
            next = std::min(str.find(sep, pos), next);
    }
    if (!str.substr(pos).empty()) {
        pieces->push_back(str.substr(pos));
    }
}

bool imgsrv::Util::GetNumFront(const std::string& str,
                             int& result, int& pos) {
    std::string::const_iterator i = str.begin();

    if (i == str.end())
        return false;

    bool negative = false;

    while ((*i < '0' || *i > '9') && *i != '-') {
        i++;
        if (i == str.end()) return false;
    }

    if (*i == '-') {
        negative = true;
        ++i;

        if (i == str.end())
            return false;
    }

    result = 0;

    for (; i != str.end(); ++i) {
        if (*i < '0' || *i > '9') {
            pos = i - str.begin();
            break;
        }
        result *= 10;
        result += *i - '0';
    }

    if (negative) {
        result = -result;
    }

    return true;
}

bool imgsrv::Util::GetStringBack(const std::string& str, std::string& s, int& pos) {
    s.clear();
    std::string::const_iterator i = str.end();
    if (i == str.begin()) return false;
    i--;
    while (*i >= '0' && *i <= '9') {
        i--;
        if (i < str.begin()) return false;
    }

    int n = 1;
    pos = 0;
    for (; i >= str.begin(); --i) {
        if (*i >= '0' && *i <= '9') {
            pos = i - str.begin() + 1;
            break;
        }
        n++;
    }
    s = str.substr(pos, n);
    return true;
}

bool imgsrv::Util::CpFile(const std::string& src, const std::string& dest) {
    struct stat src_st;
    int n = stat(src.c_str(), &src_st);
    if (0 != n){
        return false;
    }

    if (!S_ISREG(src_st.st_mode)) {
        return false;
    }

    std::ifstream infile(src, std::ios::in | std::ios::binary);
    std::ofstream outfile(dest, std::ios::out | std::ios::binary | std::ios::app);
    if ( !infile || !outfile ) {
        return false;
    }
    outfile << infile.rdbuf();

    infile.close();
    outfile.flush();
    outfile.close();

    struct stat des_st;
    n = stat(dest.c_str(), &des_st);
    if (0 != n){
//        std::cout << "cp " << dest << ", file failed!" << std::endl;
        return false;
    }
//    std::cout << "cp " << dest << ", file size=" << des_st.st_size << std::endl;

    return true;
}

static std::string GetName(const char* full_name)
{
    std::string file_name = full_name;
    const char*  mn_first = full_name;
    const char*  mn_last  = full_name + strlen( full_name );
    if ( strchr( full_name, '\\' ) != NULL )
        mn_first = strchr( full_name, '\\' ) + 1;
    else if ( strchr( full_name, '/' ) != NULL )
        mn_first = strchr( full_name, '/' ) + 1;
    if ( strchr( full_name, '.' ) != NULL )
        mn_last = strchr( full_name, '.' );
    if ( mn_last < mn_first )
        mn_last = full_name + strlen( full_name );

    file_name.assign( mn_first, mn_last );

    return file_name;
}

std::string imgsrv::Util::GetSuffix(const std::string &dir_name, const std::string &name) {
    struct dirent *ptr;
    DIR* dir = opendir(dir_name.c_str());
    std::vector<std::string> files;
    if (dir == NULL) {
        return "";
    }

    while((ptr=readdir(dir))!=NULL) {
        if(ptr->d_name[0] == '.')
            continue;
        files.push_back(ptr->d_name);
    }

    for (unsigned int i = 0; i < files.size(); ++i) {
        if(GetName(files[i].c_str()).compare(name) == 0) {
            std::string suffix_str = files[i].substr(files[i].find_first_of('.') + 1);
            closedir(dir);
            return suffix_str;
        }
    }

    closedir(dir);
    return "";
}

bool imgsrv::Util::MkDir(const std::string& path) {
    char dir[ PATH_MAX ] = {0};
    int pos = 0;
    int cnt = 0;
    const char *ptr;

    const char * full_name = path.c_str();
    while ( true ){
        if ( ( ptr = index( full_name + pos, '/' ) ) == 0 )
            break;
        cnt = ptr - full_name + 1;
        pos = cnt;
        bzero( dir, sizeof( dir ) );
        strncpy( dir, full_name, cnt );
        if ( mkdir( dir, 0777 ) < 0 ){
            if ( errno != EEXIST )
                return false;
        }
    }
    return true;
}

static int UnlinkCb(const char* fpath, const struct stat* sb, int typeflag, struct FTW* ftwbuf) {
    int rv = remove(fpath);
    if (rv)
        perror(fpath);
    return rv;
}

int imgsrv::Util::Remove(const std::string& path) {
    return nftw(path.c_str(), UnlinkCb, 64, FTW_DEPTH | FTW_PHYS);
}
