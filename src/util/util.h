/*===============================================================
* Copyright (C) 2019 All rights reserved.
*   
* @file     : util.h
* <AUTHOR> wen<PERSON>(<EMAIL>)
* @date     : 2019-07-31
* @version  : 
* @brief    : 
*
* @details  : 
================================================================*/

#ifndef IMGSRV_UTIL_H
#define IMGSRV_UTIL_H
#include <boost/thread/shared_mutex.hpp>

namespace imgsrv {

typedef boost::shared_mutex WrMutex;
typedef boost::unique_lock<WrMutex> WLock;
typedef boost::shared_lock<WrMutex> RLock;

class Util {
public:
    static void EnableLimit();
    static int64_t GetTimeStamp();
    static bool Aes256CbcDecrypt(
            const std::string& key,
            const std::string& iv,
            const std::vector<unsigned char>& ctext,
            std::vector<char>& rtext);
    static bool Aes256CbcDecryptFile(
            const std::string& key,
            const std::string& in_file,
            const std::string& out_file);
    static bool Aes256CbcEncrypt(
            const std::string& key,
            const std::string& iv,
            const std::vector<unsigned char>& ptext,
            std::vector<char>& ctext);
    static bool Aes256CbcEncryptFile(
            const std::string& key,
            const std::string& in_file,
            const std::string& out_file
            );
    static void RemoveUnsafeChar(std::string& text);
    static void StringReplace(std::string&s1, const std::string s2, const std::string s3);
    static void Split(const std::string& str, char sep, std::vector<std::string>* pieces);
    static void Split(const std::string& str, const std::string seps, std::vector<std::string>* pieces);
    static bool GetNumFront(const std::string& str, int& result, int& pos);
    static bool GetStringBack(const std::string& str, std::string& s, int& pos);
    static bool CpFile(const std::string &src, const std::string &dest);
    static std::string GetSuffix(const std::string &dir, const std::string &name);
    static bool MkDir(const std::string& path);
    static int Remove(const std::string& path);
};
}


#endif //IMGSRV_UTIL_H
