/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : url.cpp
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include <boost/regex.hpp>
#include <boost/algorithm/string.hpp>
#include <boost/algorithm/string/split.hpp>
#include "url.h"

imgsrv::Url::Url()
        : original_url_(""),
          decoded_url_(""),
          protocol_(""),
          host_(""),
          port_(""),
          path_(""),
          query_(""),
          fragment_("") {

}

imgsrv::Url::Url(const std::string& url) {
    Parse(url);
}

void imgsrv::Url::Parse(const std::string& url) {
    original_url_ = url;
    UrlDecode(url, decoded_url_);
    boost::regex ex("((http|https)://)?([^/ :]+):?([^/ ]*)(/?[^ #?]*)\\x3f?([^ #]*)#?([^ ]*)");
    boost::cmatch what;
    if(regex_match(url.c_str(), what, ex))
    {
        protocol_ = std::string(what[2].first, what[2].second);
        host_ = std::string(what[3].first, what[3].second);
        port_ = std::string(what[4].first, what[4].second);
        path_ = std::string(what[5].first, what[5].second);
        query_ = std::string(what[6].first, what[6].second);
        fragment_ = std::string(what[7].first, what[7].second);

    }
    ParseQuery(query_, params_);
}

void imgsrv::Url::ParseQuery(const std::string& query, std::map<std::string, std::string>& params) {
    std::vector<std::string> paramsv;
    boost::split(paramsv, query, boost::is_any_of("&"), boost::token_compress_on);
    for (auto & it : paramsv) {
        std::string::size_type pos = it.find_first_of("=", 0);
        std::string decoded_value;
        UrlDecode(it.substr(pos + 1), decoded_value);
//        params[it.substr(0, pos)] = it.substr(pos + 1);
        params[it.substr(0, pos)] = decoded_value;
    }
}

void imgsrv::Url::ParseUri(const std::string& uri) {
    boost::regex ex("(/?[^ #?]*)\\x3f?([^ #]*)#?([^ ]*)");
    boost::cmatch what;
    if(regex_match(uri.c_str(), what, ex))
    {
        path_ = std::string(what[1].first, what[1].second);
        query_ = std::string(what[2].first, what[2].second);
        fragment_ = std::string(what[3].first, what[3].second);

    }
    ParseQuery(query_, params_);
}

void imgsrv::Url::Reset() {
    original_url_ = "";
    decoded_url_ = "";
    protocol_ = "";
    host_ = "";
    port_ = "";
    path_ = "";
    query_ = "";
    fragment_ = "";
    params_.clear();
}

imgsrv::Url& imgsrv::Url::operator=(const imgsrv::Url& other) {
    original_url_ = other.GetOriginalUrl();
    decoded_url_ = other.GetDecodedUrl();
    protocol_ = other.GetProtocol();
    host_ = other.GetHost();
    port_ = other.GetPort();
    path_ = other.GetPath();
    query_ = other.GetQuery();
    fragment_ = other.GetFragment();
    params_ = other.GetParams();
    return *this;
}

const std::string& imgsrv::Url::GetOriginalUrl() const {
    return original_url_;
}

const std::string& imgsrv::Url::GetDecodedUrl() const {
    return decoded_url_;
}

const std::string& imgsrv::Url::GetProtocol() const {
    return protocol_;
}

const std::string& imgsrv::Url::GetHost() const {
    return host_;
}

const std::string& imgsrv::Url::GetPort() const {
    return port_;
}

const std::string& imgsrv::Url::GetPath() const {
    return path_;
}

const std::string& imgsrv::Url::GetQuery() const {
    return query_;
}

const std::string& imgsrv::Url::GetFragment() const {
    return fragment_;
}

const std::map<std::string, std::string>& imgsrv::Url::GetParams() const {
    return params_;
}

bool imgsrv::Url::UrlDecode(const std::string& in, std::string& out) {
    out.clear();
    size_t length = in.length();
    for (size_t i = 0; i < length; i++)
    {
        if (in[i] == '+') out += ' ';
        else if (in[i] == '%')
        {
            if (i + 2 < length) {
                unsigned char high;
                unsigned char low;
                if (!FromHex((unsigned char)in[++i], &high)) {
                    return false;
                }
                if (!FromHex((unsigned char)in[++i], &low)) {
                    return false;
                }
                out += high*16 + low;
            } else {
                return false;
            }
        }
        else out += in[i];
    }
    return true;
}

bool imgsrv::Url::UrlEncode(const std::string& in, std::string& out) {
    out.clear();
    size_t length = in.length();
    for (size_t i = 0; i < length; i++)
    {
        if (isalnum((unsigned char)in[i]) ||
            (in[i] == '-') ||
            (in[i] == '_') ||
            (in[i] == '.') ||
            (in[i] == '~'))
            out += in[i];
        else if (in[i] == ' ')
            out += "+";
        else
        {
            out += '%';
            out += ToHex((unsigned char)in[i] >> 4);
            out += ToHex((unsigned char)in[i] % 16);
        }
    }
    return true;
}

unsigned char imgsrv::Url::ToHex(unsigned char x) {
    return  x > 9 ? x + 55 : x + 48;
}

bool imgsrv::Url::FromHex(unsigned char in, unsigned char* out) {
    if (out == nullptr) {
        return false;
    }

    if (in >= 'A' && in <= 'Z') {
        *out = in - 'A' + 10;
    } else if (in >= 'a' && in <= 'z') {
        *out = in - 'a' + 10;
    } else if (in >= '0' && in <= '9') {
        *out = in - '0';
    } else {
        return false;
    }
    return true;
}



