/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : task_data.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-07
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_TASK_DATA_H
#define IMGSRV_TASK_DATA_H


#include <string>
#include "common.h"
#include "connection.h"

namespace imgsrv {

class TaskId {
public:
    TaskId();

    TaskId(const TaskId& other);

    TaskId& operator=(const TaskId& other);

    TaskId(TaskId&& other) noexcept;

    TaskId& operator=(TaskId&& other) noexcept;

    const std::string& GetId() const;

    void SetId(const std::string& id);

    std::string id_;
};

/*enum TargetType {
    kId = 0,
    kUrl,
    kErr
};*/

enum DoCrypt {
    kDecryptFile = 0,
    kEncryptResult,
    kDoBoth,
    kDoNone
};

enum CipherType {
    kNone = 0,
    kAes256Cbc
};

class TaskData {
public:
    TaskData();

    virtual ~TaskData();

    TaskData(const TaskData& other);

    TaskData& operator=(const TaskData& other);

    TaskData(TaskData&& other) noexcept;

    TaskData& operator=(TaskData&& other) noexcept;

    ProcType GetProcType() const;

    void SetProcType(ProcType proc_type);

    CallType GetCallType() const;

    void SetCallType(CallType call_type);

    const std::string& GetBizType() const;

    void SetBizType(const std::string& biz_type);

    const std::string& GetAppId() const;

    void SetAppId(const std::string& app_id);

    TargetType GetTargetType() const;

    void SetTargetType(TargetType target_type);

    const std::string& GetTarget() const;

    void SetTarget(const std::string& target);

    const std::string& GetTargetSuffix() const;

    void SetTargetSuffix(const std::string& target_suffix);

    const std::string& GetTaskId() const;

    void SetTaskId(const std::string& task_id);

    int GetTimeOut() const;

    void SetTimeOut(int time_out);

    const std::string &getUrl() const;

    void setUrl(const std::string &url);

    const std::string& GetInputFile() const;

    void SetInputFile(const std::string& input_file);

    bool IsInputFileDone() const;

    void SetInputFileDone(bool imput_file_done);

    const std::string& GetCipherKey() const;

    void SetCipherKey(const std::string& cipher_key);

    CipherType GetCipherType() const;

    void SetCipherType(CipherType cipher_type);

    DoCrypt GetDoCrypt() const;

    void SetDoCrypt(DoCrypt do_crypt);

    const std::string& GetOutputFile() const;

    void SetOutputFile(const std::string& output_file);

//    const ServerConnectionPtr& GetServerConnectionPtr() const;
    ServerConnectionPtr& GetServerConnectionPtr();

    void SetServerConnectionPtr(const ServerConnectionPtr& server_connection_ptr);

    const FetchConnectionPtr& GetFetchConnectionPtr() const;

    void SetFetchConnectionPtr(const FetchConnectionPtr& fetch_connection_ptr);

public:
    std::string task_id_;

private:
    ProcType proc_type_;
    CallType call_type_;
    std::string biz_type_;
    std::string app_id_;
    TargetType target_type_;
    std::string target_;
    std::string target_suffix_;
    std::string input_file_;
    bool input_file_done_;
    std::string cipher_key_;
    CipherType cipher_type_;
    DoCrypt do_crypt_;
    std::string output_file_;
    int time_out_;
    std::string url_;
    ServerConnectionPtr server_connection_ptr_;
    FetchConnectionPtr fetch_connection_ptr_;
};

class AudioProcTask : public TaskData {
public:
    AudioProcTask();

    AudioProcTask(const AudioProcTask& other);

    AudioProcTask(AudioProcTask&& other);

    AudioProcTask& operator=(const AudioProcTask& other);
    AudioProcTask& operator=(AudioProcTask&& other);

    audio_type GetSf() const;

    void SetSf(audio_type sf);

    int GetSsr() const;

    void SetSsr(int ssr);

    int GetSbr() const;

    void SetSbr(int sbr);

    audio_type GetDf() const;

    void SetDf(audio_type df);

    int GetDsr() const;

    void SetDsr(int dsr);

    int GetDbr() const;

    void SetDbr(int dbr);

    const std::map<audio_type, std::string>& GetAudioTypeMap() const;

private:
    audio_type sf_;
    int ssr_;
    int sbr_;
    audio_type df_;
    int dsr_;
public:
    int GetErrCode() const;

    void SetErrCode(int err_code);

private:
    int dbr_;
    int err_code_;
    std::map<audio_type, std::string> audio_type_map_;
};

class VideoProcTask : public TaskData{

public:
    VideoProcTask();

    VideoProcTask(const VideoCheckTask& other);

    VideoProcTask(VideoCheckTask&& other) noexcept;

    VideoProcTask& operator=(const VideoProcTask& other);
    VideoProcTask& operator=(VideoProcTask&& other);

    const std::string& GetTemplateId() const;

    void SetTemplateId(const std::string& template_id);

private:
    std::string template_id_;
public:
    const std::string &getTemplateId() const;

    void setTemplateId(const std::string &templateId);
};

class VideoCheckTask : public TaskData{

public:
    VideoCheckTask();

    VideoCheckTask(const VideoCheckTask& other);

    VideoCheckTask(VideoCheckTask&& other) noexcept;

    VideoCheckTask& operator=(const VideoCheckTask& other);
    VideoCheckTask& operator=(VideoCheckTask&& other);

    const std::string& GetTemplateId() const;
    void SetTemplateId(const std::string& template_id);

    const std::string& GetSourceFile() const;
    void SetSourceFile(const std::string& source_file);

    bool IsSourceFileDone() const;
    void SetSourceFileDone(bool source_file_done);

    const FetchConnectionPtr& GetSourceFileFetchConnPtr() const;
    void SetSourceFileFetchConnPtr(const FetchConnectionPtr& source_file_fetch_conn_ptr);

private:
    std::string template_id_;
    std::string source_file_;
    bool source_file_done_;
    FetchConnectionPtr source_file_fetch_conn_ptr_;
};

class ImgProcTask : public TaskData {
public:
    ImgProcTask();

    ImgProcTask(const ImgProcTask& other);

    ImgProcTask& operator=(const ImgProcTask& other);

    ImgProcTask(ImgProcTask&& other) noexcept;

    ImgProcTask& operator=(ImgProcTask&& other) noexcept;

//    const std::string& GetCipherKey() const;
//
//    void SetCipherKey(const std::string& cipher_key);
//
//    const std::string& GetCipherType() const;
//
//    void SetCipherType(const std::string& cipher_type);

    const std::string& GetParam1() const;

    void SetParam1(const std::string& param_1);

    const std::string& GetParam2() const;

    void SetParam2(const std::string& param_2);

    const std::string& GetTargetMd5() const;

    void SetTargetMd5(const std::string& target_md_5);

    const std::string &getAlgParam() const;

    void setAlgParam(const std::string &algParam);
//    const std::string& GetTargetSuffix() const;
//
//    void SetTargetSuffix(const std::string& target_suffix);

private:
//    std::string cipher_key_;
//    std::string cipher_type_;
    std::string param_1_;
    std::string param_2_;
    std::string target_md5_;
    std::string alg_param_;
//    std::string target_suffix_;
};

class ImgWmTask : public TaskData {
public:
    ImgWmTask();

    ImgWmTask(const ImgWmTask& other);

    ImgWmTask& operator=(const ImgWmTask& other);

    ImgWmTask(ImgWmTask&& other) noexcept;

    ImgWmTask& operator=(ImgWmTask&& other) noexcept;

    const std::string& GetWmid() const;

    void SetWmid(const std::string& wmid);

    const std::string& GetWmFile() const;

    void SetWmFile(const std::string& wm_file);

    bool IsWmFileDone() const;

    void SetWmFileDone(bool wm_file_done);

    const std::string& GetParam1() const;

    void SetParam1(const std::string& param_1);

    int GetVoffset() const;

    void SetVoffset(int voffset);

    int GetT() const;

    void SetT(int t);

    int GetP() const;

    void SetP(int p);

    int GetX() const;

    void SetX(int x);

    int GetY() const;

    void SetY(int y);

    int GetWp() const;

    void SetWp(int wp);

    const FetchConnectionPtr& GetWmFetchConnectionPtr() const;

    void SetWmFetchConnectionPtr(const FetchConnectionPtr& wm_fetch_connection_ptr);

private:
    std::string wmid_;
    std::string wm_file_;
    bool wm_file_done_;
    std::string param_1_;
    int voffset_;
    int t_;
    int p_;
    int x_;
    int y_;
    int wp_;
    FetchConnectionPtr wm_fetch_connection_ptr_;
};

class TxtWmTask : public TaskData {
public:
    enum FontType {
        kWqyZenHei = 1,
        kWqyMicroHei,
        kFangZhengShuSong,
        kFangZhengKaiTi,
        kFangZhengHeiTi,
        kFangZhengFangSong,
        kDroidSansFallBack
    };

    TxtWmTask();

    TxtWmTask(const TxtWmTask& other);

    TxtWmTask& operator=(const TxtWmTask& other);

    TxtWmTask(TxtWmTask&& other) noexcept;

    TxtWmTask& operator=(TxtWmTask&& other) noexcept;

    int GetVoffset() const;

    void SetVoffset(int voffset);

    int GetT() const;

    void SetT(int t);

    int GetP() const;

    void SetP(int p);

    int GetX() const;

    void SetX(int x);

    int GetY() const;

    void SetY(int y);

    const std::string& GetText() const;

    void SetText(const std::string& text);

    FontType GetFontType() const;

    void SetFontType(FontType font_type);

    const std::string& GetColor() const;

    void SetColor(const std::string& color);

    int GetSize() const;

    void SetSize(int size);

    int GetS() const;

    void SetS(int s);

    int GetRotate() const;

    void SetRotate(int rotate);

    bool IsFill() const;

    void SetFill(bool fill);

private:
    int voffset_;
    int t_;
    int p_;
    int x_;
    int y_;
    std::string text_;
    FontType font_type_;
    std::string color_;
    int size_;
    int s_;
    int rotate_;
    bool fill_;
};
}

typedef boost::shared_ptr<imgsrv::TaskData> TaskDataPtr;
typedef boost::shared_ptr<imgsrv::AudioProcTask> AudioProcTaskPtr;
typedef boost::shared_ptr<imgsrv::ImgProcTask> ImgProcTaskPtr;
typedef boost::shared_ptr<imgsrv::ImgWmTask> ImgWmTaskPtr;
typedef boost::shared_ptr<imgsrv::TxtWmTask> TxtWmTaskPtr;
typedef boost::shared_ptr<imgsrv::VideoProcTask> VideoProcTaskPtr;
typedef boost::shared_ptr<imgsrv::VideoCheckTask> VideoCheckTaskPtr;

#endif //IMGSRV_TASK_DATA_H
