/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : resolver.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_RESOLVER_H
#define IMGSRV_RESOLVER_H


#include <boost/asio.hpp>
#include <boost/concept_check.hpp>
//#include <boost/thread/shared_mutex.hpp>
#include <boost/serialization/singleton.hpp>
#include <util.h>

using namespace boost::asio;
using namespace boost::asio::ip;
namespace imgsrv {

class EpVal {
public:
    EpVal () : ts_(0){}
    explicit EpVal(const tcp::resolver::iterator& ep_it) : it_ep_(ep_it), ts_(Util::GetTimeStamp()){}

    tcp::resolver::iterator it_ep_;
    int64_t ts_;
};

class DnsCache : public boost::serialization::singleton<DnsCache>{
public:
//    typedef boost::shared_mutex WrMutex;
//    typedef boost::unique_lock<WrMutex> WLock;
//    typedef boost::shared_lock<WrMutex> RLock;
    tcp::resolver::iterator Get(const std::string& host, int64_t &ts);
    void Set(const std::string& host, const tcp::resolver::iterator& ep_it);
    DnsCache();
private:
    WrMutex mutex_;
    std::map<std::string, EpVal> cache_;
};

#define DNS_CACHE DnsCache::get_mutable_instance()

class Resolver {
public:
    Resolver(io_service& ios, int64_t rt) :resolver_(ios), refresh_timeout_(rt) {}
    void Cancel() {
        resolver_.cancel();
    }

    template <typename ResolveHandler>
    tcp::resolver::iterator Resolve(const std::string& host, const std::string& service, int64_t ts,
                                    BOOST_ASIO_MOVE_ARG(ResolveHandler) handler) {

        int64_t ep_ts = -1;
        auto ep = DNS_CACHE.Get(GenDnsCacheKey(host, service), ep_ts);
        auto end = tcp::resolver::iterator();
        if ((ep == end) || ((ep != end) && (ts >= (refresh_timeout_ * 1000) + ep_ts)) ) {
//            tcp::resolver::query q(host, "http");
            tcp::resolver::query q(host, service);
            resolver_.async_resolve(q, handler);
            return end;
        }

        return ep;

    }

    template <typename ResolveHandler>
    void ResolveWithoutCache(
            const std::string& host,
            const std::string& service,
            BOOST_ASIO_MOVE_ARG(ResolveHandler) handler) {

//            tcp::resolver::query q(host, "http");
        tcp::resolver::query q(host, service);
        resolver_.async_resolve(q, handler);
    }

    template <typename ResolveHandler>
    tcp::resolver::iterator Resolve(const std::string& host, const std::string& service,
                                    BOOST_ASIO_MOVE_ARG(ResolveHandler) handler) {
        return Resolve(host, service, Util::GetTimeStamp(), handler);
    }

    void SetCache(
            const std::string& host,
            const std::string& service,
            const tcp::resolver::iterator& ep_it) {
        DNS_CACHE.Set(GenDnsCacheKey(host, service), ep_it);
    }

private:
    std::string GenDnsCacheKey(const std::string& host, const std::string& service) {
        std::string key;
        key.append(host).append(":").append(service);
        return key;
    }

private:
    tcp::resolver resolver_;
    int64_t refresh_timeout_; //ms
};
}




#endif //IMGSRV_RESOLVER_H
