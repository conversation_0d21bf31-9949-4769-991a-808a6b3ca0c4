/**
 * ===============================================================
 * Copyright (C) 2017 All rights reserved.
 *
 * @file     : main.cpp
 * <AUTHOR> we<PERSON><PERSON>(<EMAIL>)
 * @date     : 2019/06/20
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/

#include <libgen.h>
#include <cerrno>
#include <sys/prctl.h>
#include <sys/wait.h>
#include <csignal>
#include <sys/stat.h>
#include "fcntl.h"
#include <boost/program_options.hpp>
#include <iostream>
//#include "Magick++.h"
#include <util.h>
#include "config.h"
#include "log_adapter.h"
#include "command_executor.h"
#include "img_dj_cmd_proc.h"
#include "img_wm_proc.h"
#include "video_cmd_proc.h"
#include "worker.h"
#include "server.h"
#include "antvip/antvip_client.h"

using namespace boost::program_options;
using namespace antvip;

pid_t g_child_pid;


std::string Version() {
    std::string version = "1.2.0";
    std::string phase = "beta";
    std::string build = "202104141438";
    static std::string g_version = version + "-" + phase + "." + build;
    return g_version;
}

std::ofstream & GetLogger() {
    static std::ofstream g_wd_logger;
    return g_wd_logger;
}

#define WATCH_LOG WatchLog()

void BlockSignal(int signo) {
    sigset_t new_mask, old_mask;
    sigemptyset(&new_mask);
    sigaddset(&new_mask, signo);
    if(sigprocmask(SIG_BLOCK, &new_mask, &old_mask) < 0){
        std::cout << "sigprocmask error!" << std::endl;
    }
}

int ImgSrvRun(std::string & config_file, std::string& pr_name) {
    imgsrv::Util::EnableLimit();

    if(!CONFIG.Parse(config_file)) {
        std::cout << "Parsing config file :" << config_file << " failed!" << std::endl;
        exit(-1);
    }

    /*if(!CE_LOG_INIT(GET_CONF.ce_log_path, ::util::log::ONE_HOUR, (log_level)GET_CONF.log_level)) {
        std::cout << "Init CE log failed :" << GET_CONF.ce_log_path << std::endl;
        return -1;
    }*/

    if (!CE.Init(GET_CONF.worker_process_num, GET_CONF.third_path, pr_name)) {
        std::cout << "Init CE failed!" << std::endl;
        return -1;
    }

    if(!LOG_INIT(GET_CONF.log_path, ::util::log::ONE_HOUR, (log_level)GET_CONF.log_level)) {
        std::cout << "Init log failed :" << GET_CONF.log_path << std::endl;
        exit(-1);
    }
    if(!STAT_INIT(GET_CONF.stat_path, ::util::log::ONE_HOUR)) {
        std::cout << "Init log failed :" << GET_CONF.stat_path << std::endl;
        exit(-1);
    }

    if(!IMG_DJ_CMD_PROC.Init(GET_CONF.out_file_path, GET_CONF.third_path, GET_CONF.hevc_thread_num)) {
        std::cout << "Init ImgDjCmdProc failed!" << std::endl;
        exit(-1);
    }

    if(!IMG_WM_PROC.Init(GET_CONF.out_file_path, GET_CONF.third_path, GET_CONF.hevc_thread_num)) {
        std::cout << "Init ImgDjCmdProc failed!" << std::endl;
        exit(-1);
    }

    if(!VIDEO_CMD_PROC.Init(GET_CONF.out_file_path, GET_CONF.third_path, GET_CONF.hevc_thread_num)) {
        std::cout << "Init VideoCmdProc failed!" << std::endl;
        exit(-1);
    }

//    Magick::InitializeMagick(nullptr);
    if (CONFIG.state_.antvip_enabled) {
        if(RET_SUCC != AntvipClient::init(
                "mm_cache_server",
                true,
                GET_CONF.antvip.antvip_cloud_inc,
                GET_CONF.antvip.antvip_log,
                GET_CONF.antvip.antvip_log_level)) {
            SLOG_F << "antvip client init failed!";
            LOG_CLOSE();
            STAT_CLOSE();
            return -1;
        }
    }

    if(!WORKER.Init(GET_CONF.worker_thread_num)) {
        std::cout << "Init worker failed!" << std::endl;
        exit(-1);
    }
    WORKER.Run();
    if(!SERVER.Init(GET_CONF.server_port, GET_CONF.server_thread_num, GET_CONF.backlog)) {
        std::cout << "Init server failed!" << std::endl;
        exit(-1);
    }
    SERVER.Run();


    SLOG_I << "imgsrv started!";
    SERVER.Join();
    WORKER.Join();
    SLOG_I << "imgsrv stopped!";
    LOG_CLOSE();
    STAT_CLOSE();
    exit(0);;
}

int OpenWatchLog() {
    if (!GetLogger().is_open()) {
        GetLogger().open("./watchdog.log", std::ios_base::out | std::ios_base::app);
    }
    if (!GetLogger().is_open()) {
        return -1;
    }
    return 0;
}

int CloseWatchLog() {
    if (GetLogger().is_open()) {
        GetLogger().close();
    }
    return 0;
}

std::ofstream & WatchLog() {
    char string[128] = {0};
    time_t today;
    tm *now;
    time(&today);
    now = localtime(&today);
    strftime(string, 128, "%Y-%m-%d %H:%M:%S ", now);
    GetLogger() << string;
    return GetLogger();
}

void SigUsr1(int signo){
    WATCH_LOG << "Stopping imgsrv..." << std::endl;
    kill(g_child_pid, SIGQUIT);
    WATCH_LOG << "Stopping watch dog..." << std::endl;
    exit(0);
}

void WatchDog(
        std::string& core_path,
        int core_num,
        std::string& wk_name,
        std::string& pr_name,
        std::string& config_file) {
    int status;
    while (true) {
        g_child_pid = fork();
        if (g_child_pid < 0) {
            WATCH_LOG << "Fork error!" << std::endl;
            exit(-1);
        } else if (g_child_pid == 0){
//            BlockSignal(SIGUSR1);
            WATCH_LOG << "imgsrv started!" << std::endl;
            prctl(PR_SET_NAME, wk_name.c_str(), NULL, NULL, NULL);
            ImgSrvRun(config_file, pr_name);
            break;
        } else {
            BlockSignal(SIGQUIT);
            signal(SIGUSR1, SigUsr1);
            WATCH_LOG << "Watch dog started!" << std::endl;
            wait(&status);
            if (WIFEXITED(status)) {
                WATCH_LOG << "imgsrv terminated normally, exit status: " << WEXITSTATUS(status) << std::endl;
                exit(0);
            } else if (WIFSIGNALED(status)) {
                WATCH_LOG << "imgsrv terminated abnormally, signal status: " << WTERMSIG(status) << std::endl;
                if (WCOREDUMP(status)) {
                    WATCH_LOG << "imgsrv core file dumped!" << std::endl;
                    //ls -t | sed '1,3d' | xargs rm -rf
                    std::stringstream ss;
                    ss << "ls -t " << core_path << " | grep core." << wk_name << " | sed 's:^:" << core_path
                       << "/: ' | sed '1," << core_num << "d' | xargs rm -rf";
                    WATCH_LOG << "Command: " << ss.str() << std::endl;
                    ::system(ss.str().c_str());
                }
            }
            sleep(1);
        }
    }
}

int main(int argc, char* argv[]) {
    std::string core_path;
    std::string wd_name;
    std::string wk_name;
    std::string pr_name;
    int core_num;
    std::string config_file;

    options_description opts("imgsrv options");
    opts.add_options()
            ("help,h", "help info")
            ("version,v", "show version")
            ("corepath,c", value<std::string>(&core_path)->default_value("/home/<USER>/logs"), "Coredump path")
            ("corenum,n", value<int>(&core_num)->default_value(2), "Reserved coredump number")
            ("wd_name,d", value<std::string>(&wd_name)->default_value("imgsrv_wd"), "imgsrv watch dog name")
            ("wk_name,w", value<std::string>(&wk_name)->default_value(basename(argv[0])), "imgsrv process name")
            ("pr_name,p", value<std::string>(&pr_name)->default_value("img_proc"), "img process name")
            ("conf,f", value<std::string>(&config_file)->default_value("./conf/imgsrv.conf"), "Path to config file");
    variables_map vm;
    try {
        store(parse_command_line(argc, argv, opts), vm);
    } catch (std::exception& e) {
        std::cout << e.what() << std::endl;
        return -1;
    }
    notify(vm);
    if (vm.count("help")) {
        std::cout << opts << std::endl;
        return 0;
    }
    if (vm.count("version")) {
        std::cout << "imgsrv version: " << Version() << std::endl;
        return 0;
    }

    if (OpenWatchLog() != 0) {
        std::cout << "Open watch log failed!" << std::endl;
        return -1;
    }

    if (access(core_path.c_str(), F_OK) == -1) {
        mkdir(core_path.c_str(), S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
        WATCH_LOG << "Core path " << core_path << " is created!" << std::endl;
    } else if (access(core_path.c_str(), W_OK) == -1) {
        WATCH_LOG << "Core path " << core_path << " is not writable!" << std::endl;
        return -1;
    }

    /*if(!CONFIG.Parse(config_file)) {
        std::cout << "Parsing config file :" << config_file << " failed!" << std::endl;
        return -1;
    }*/

    prctl(PR_SET_NAME, wd_name.c_str(), NULL, NULL, NULL);

    WatchDog(core_path, core_num, wk_name, pr_name, config_file);

    CloseWatchLog();
    return 0;
}


