//
// Created by w<PERSON><PERSON><PERSON><PERSON> on 2021/4/16.
//

#include "audio_cmd_proc.h"
#include "task_data.h"
#include "container.h"
#include "common.h"
#include "config.h"
#include "command_executor.h"

//std::string imgsrv::audio_cmd_proc::bin_path_ = GET_CONF.third_path + "/bin";
//std::string imgsrv::audio_cmd_proc::output_path_ = GET_CONF.out_file_path;

bool imgsrv::audio_cmd_proc::Process(const std::string& task_id) {
    bool rst = false;
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        if (task_data_ptr->GetProcType() != kAudioConvert) {
            return false;
        }
        AudioProcTaskPtr audio_proc_task_ptr =
                boost::dynamic_pointer_cast<AudioProcTask>(task_data_ptr);

        Statistics& stat_info = audio_proc_task_ptr->GetServerConnectionPtr()->GetStatInfo();

        std::string tmp_path = GetTmpPath(audio_proc_task_ptr->GetTaskId());
        if (access(tmp_path.c_str(), F_OK) != 0) {
            Util::MkDir(tmp_path + "/");
        }


        stat_info.call_start_time_ = Util::GetTimeStamp();
        std::stringstream ss;

        ss << GetTcPath();

        auto it = audio_proc_task_ptr->GetAudioTypeMap().find(audio_proc_task_ptr->GetSf());
        if (it != audio_proc_task_ptr->GetAudioTypeMap().end()) {
            ss << " " << it->second;
        }

        if (audio_proc_task_ptr->GetSsr() != -1) {
            ss << " -" << audio_proc_task_ptr->GetSsr();
        }

        ss << " " << audio_proc_task_ptr->GetInputFile();

        std::string suffix;
        it = audio_proc_task_ptr->GetAudioTypeMap().find(audio_proc_task_ptr->GetDf());
        if (it != audio_proc_task_ptr->GetAudioTypeMap().end()) {
            suffix = it->second;
            ss << " " << suffix;
        }

        if (audio_proc_task_ptr->GetDsr() != -1) {
            ss << " -" << audio_proc_task_ptr->GetDsr();
        }

        std::string output = GetTmpPath(audio_proc_task_ptr->GetTaskId()) + "/out." + suffix;
        ss << " " << output;

        std::string sys_cmd = ss.str();
        SLOG_I << audio_proc_task_ptr->GetTaskId() << " : " << sys_cmd;
        int ret_code;
        if (!CE.RunCommand(sys_cmd, audio_proc_task_ptr->GetTaskId(), audio_proc_task_ptr->GetTimeOut(), ret_code)) {
            SLOG_E << audio_proc_task_ptr->GetTaskId() << " : system command failed. " << sys_cmd
                   << "; errno=" << errno << " " <<  strerror(errno) << ", exit code: " << ret_code;
            return false;
        }
        audio_proc_task_ptr->SetErrCode(ret_code);
        stat_info.call_end_time_ = Util::GetTimeStamp();
        stat_info.call_total_time_ = stat_info.call_end_time_- stat_info.call_start_time_;

        audio_proc_task_ptr->SetOutputFile(output);
        audio_proc_task_ptr->SetTargetSuffix(suffix);
        return true;

    } else {
        return false;
    }
    return rst;
}

std::string imgsrv::audio_cmd_proc::GetTcPath() {
    return GET_CONF.third_path + "/bin/transcode";
}

std::string imgsrv::audio_cmd_proc::GetTmpPath(const std::string& task_id) {
    return GET_CONF.out_file_path + "/" + task_id;
}

std::string imgsrv::audio_cmd_proc::GenErrReply(imgsrv::Reply::StatusType st, int err_code) {
    std::stringstream ss;

    ss << imgsrv::Reply::GetRspStatusLine(st);
    ss << "Server: " << "RPS" <<"\r\n";
    ss << "Err-Code: " << err_code << "\r\n";
    std::string stock_content = imgsrv::Reply::GetStockContent(st);
    ss << "Content-Length: " << std::to_string(stock_content.size()) << "\r\n";
    ss << "Content-Type: text/html\r\n";
    ss << "Connection: close\r\n\r\n";
    ss << stock_content;
    return ss.str();
}
