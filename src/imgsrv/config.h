/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : config.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-07-31
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/

#ifndef IMGSRV_CONFIG_H
#define IMGSRV_CONFIG_H

#include <string>
#include <boost/serialization/singleton.hpp>
#include <vector>

namespace imgsrv {

typedef struct antvip_conf_s {
    std::string antvip_cloud_inc;
    std::string antvip_log;
    unsigned int antvip_log_level;
    std::string antvip_afts_domain_name;
} antvip_conf;

struct Conf {
    //server
    u_int32_t server_thread_num;
    u_int32_t server_port;
    u_int32_t backlog;
    u_int32_t default_time_out;

    std::string out_file_path;
//    std::string bin_path;
//    std::string font_path;
    std::string third_path;

    //worker
    u_int32_t worker_thread_num;
    u_int32_t worker_process_num;

    //logger
    u_int32_t log_level;
    std::string log_path;
    std::string stat_path;
    std::string ce_log_path;
    std::string gmstat_path;

    int dns_refresh_interval;

    //afts
    std::vector<std::string> host_names;
    antvip_conf antvip;
    int resolve_retry;

    int gm_zoom_type;
    int hevc_thread_num;

    //debug
    bool rm_tmp_dir;
};

typedef struct imgsrv_conf_state_s {
    bool antvip_enabled;
} imgsrv_conf_state;

class Config : public boost::serialization::singleton<Config>{
public:
    Config();
    bool Parse(const std::string& conf_file);
    const Conf& GetConf();
    imgsrv_conf_state state_;
private:
    Conf conf_;
};

} //namespace imgsrv
#define CONFIG imgsrv::Config::get_mutable_instance()
#define GET_CONF imgsrv::Config::get_mutable_instance().GetConf()


#endif //IMGSRV_CONFIG_H
