/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : reply.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_REPLY_H
#define IMGSRV_REPLY_H

#include <vector>
#include <string>
#include <map>
#include <algorithm>
#include <util.h>
#include "header.h"

namespace imgsrv {

class Mime {
public:
    /*enum Type {
        JPG = 1,
        GIF = 2,
        PNG = 3,
        WEBP = 4,
        FILE = 5
    };*/
    static std::string GetMimeFromSuffix(const std::string& suffix) {

        std::string lower_suffix;
        lower_suffix.resize(suffix.size());
        std::transform(suffix.begin(), suffix.end(), lower_suffix.begin(), ::tolower);

        if (lower_suffix == "jpeg" || lower_suffix == "jpg") {
            return "image/jpeg";
        }
        if (lower_suffix == "gif") {
            return "image/gif";
        }
        if (lower_suffix == "png") {
            return "image/png";
        }
        if (lower_suffix == "webp") {
            return "image/webp";
        }
        if (lower_suffix == "ahp1") {
            return "image/x-ahp1";
        }
        if (lower_suffix == "ahp2") {
            return "image/x-ahp2";
        }
        if (lower_suffix == "ahp3") {
            return "image/x-ahp3";
        }
        if (lower_suffix == "pcm") {
            return "audio/pcm";
        }
        if (lower_suffix == "amr") {
            return "audio/amr";
        }
        if (lower_suffix == "silk") {
            return "audio/silk";
        }

        return "application/octet-stream";
    }

    static std::string GetSuffixFromMime(const std::string& mime) {

        std::string lower_mime;
        lower_mime.resize(mime.size());
        std::transform(mime.begin(), mime.end(), lower_mime.begin(), ::tolower);

        if (lower_mime == "image/jpeg") {
            return "jpg";
        }
        if (lower_mime == "image/gif") {
            return "gif";
        }
        if (lower_mime == "image/png") {
            return "png";
        }
        if (lower_mime == "image/webp") {
            return "webp";
        }
        if (lower_mime == "image/x-ahp1") {
            return "ahp1";
        }
        if (lower_mime == "image/x-ahp2") {
            return "ahp2";
        }
        if (lower_mime == "image/x-ahp3") {
            return "ahp3";
        }

        return "";
    }

};


class Reply {
public:
    static const size_t kStockBuffSize = 4096;
    //Reference: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status
    enum StatusType {
        kDumbCode = 0,
        kContinue = 100,
        kOk = 200,
        kCreated = 201,
        kAccepted = 202,
        kNoContent = 204,
        kPartialContent = 206,
        kMultipleChoices = 300,
        kMovedPermanently = 301,
        kMovedTemporarily = 302,
        kNotModified = 304,
        kBadRequest = 400,
        kUnauthorized = 401,
        kForbidden = 403,
        kNotFound = 404,
        kGone = 410,
        kPayloadTooLarge = 413,
        kRangeError = 416,
        kProcessTimeout = 480,
        kInternalServerError = 500,
        kNotImplemented = 501,
        kBadGateway = 502,
        kServiceUnavailable = 503,
        kGatewayTimeout = 504
    };

    Reply();

    void Reset();

    static std::string GetRspStatusLine(StatusType status);

    int StockReply(StatusType st, char* out_buff, size_t out_buff_size);

    StatusType GetStatus() const;

    void SetStatus(StatusType status);

    std::vector<Header>& GetHeaders();

    size_t GetContentLength() const;

    void SetContentLength(size_t content_length);

    size_t GetCurLength() const;

    void SetCurLength(size_t cur_length);

    int GenHeaderToBuffer(char* data, size_t size);

    static std::string GetStockContent(StatusType status);
public:

    WrMutex rp_phase_mutex_;
    enum phase_type {
        rp_start = 0,
        rp_header,
        rp_body,
        rp_failed,
        rp_done
    } rp_phase;

private:
    StatusType status_;
    std::vector<Header> headers_;
    std::string stock_content_;

    size_t content_length_;
    size_t cur_length_;

};

class Response {
public:
    Response();

    int GetStatusCode() const;

    void SetStatusCode(int status_code);

    std::map<std::string, Header>& GetHeaders();

    void SetHeaders(const std::map<std::string, Header>& headers);

    size_t GetContentLength() const;

    void SetContentLength(size_t content_length);

    size_t GetCurLength() const;

    void SetCurLength(size_t cur_length);

private:
    int status_code_;
    std::map<std::string, Header> headers_;
    size_t content_length_;
    size_t cur_length_;

};

}


#endif //IMGSRV_REPLY_H
