//
// Created by t<PERSON><PERSON> on 2023/5/30.
//

#ifndef IMGSRV_VIDEO_CMD_PROC_H
#define IMGSRV_VIDEO_CMD_PROC_H
#include <string>
#include "task_data.h"

namespace imgsrv{
class VideoCmdProc : public boost::serialization::singleton<VideoCmdProc>{
public:
    VideoCmdProc();
    bool Init(const std::string& output_path,const std::string& third_path,int video_thread_num_);
    bool Process(const std::string& task_id);

private:
    bool VideoExtractAudioCmd(VideoProcTaskPtr& video_proc_task_ptr, std::string& command,std::string& output_path);
    bool VideoInfoParseCmd(VideoProcTaskPtr& video_proc_task_ptr, std::string& command,std::string& output_path);
    std::string GetOutputFile(const std::string& task_id, const std::string& target_suffix);
private:
    std::string bin_path_;
    std::string output_path_;
    int video_thread_num_;

};
}

#define VIDEO_CMD_PROC imgsrv::VideoCmdProc::get_mutable_instance()
#endif //IMGSRV_VIDEO_CMD_PROC_H
