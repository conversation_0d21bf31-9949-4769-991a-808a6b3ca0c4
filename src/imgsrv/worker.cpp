/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : worker.cpp
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-12
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include "worker.h"
#include "log_adapter.h"
#include "container.h"
#include "img_wm_proc.h"
#include "img_dj_cmd_proc.h"
#include "audio_cmd_proc.h"
#include "video_cmd_proc.h"
#include "video_check_proc.h"

imgsrv::Worker::Worker() : inited_(false), thread_num_(0), dummy_work_(ios_){
    threads_.clear();
}

bool imgsrv::Worker::Init(size_t thread_num) {
    thread_num_ = thread_num;
    inited_ = true;

    return true;
}

bool imgsrv::Worker::Run() {
    if (!inited_) {
        return false;
    }
    for (size_t i = 0; i < thread_num_; ++i) {
        boost::shared_ptr<boost::thread> t(
                new boost::thread(boost::bind(&boost::asio::io_service::run, &ios_)));
        threads_.push_back(t);
    }
    SLOG_I << "Worker started!";
    return true;
}

bool imgsrv::Worker::Stop() {
    ios_.stop();
    SLOG_I << "Worker stopped!";
    return true;
}

void imgsrv::Worker::Join() {
    for (auto & thread : threads_) {
        thread->join();
    }
}

bool imgsrv::Worker::ProcessNew() {
    int64_t before_q = Util::GetTimeStamp();
    TaskId task_id;
    bool rst = CONTAINER.QueuePopFront(task_id);
    if (!rst) {
        return false;
    }
    int64_t after_q = Util::GetTimeStamp();
    SLOG_D << task_id.GetId() << " : got task from queue, takes: " << after_q - before_q << " ms.";

    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id.GetId());
    if (task_data_ptr) {
        Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
        stat_info.proc_start_time_ = Util::GetTimeStamp();
        if(task_data_ptr->GetProcType() == kVideoProc){
            rst = VIDEO_CMD_PROC.Process(task_id.GetId());
        }else if (task_data_ptr->GetProcType() == kAudioConvert) {
            rst = audio_cmd_proc::Process(task_id.GetId());
        } else if (task_data_ptr->GetProcType() == kImgWaterMark || task_data_ptr->GetProcType() == kTextWaterMark) {
            rst = IMG_WM_PROC.Process(task_id.GetId());
        } else if (task_data_ptr->GetProcType() == kImgAvif) {
            rst = IMG_DJ_CMD_PROC.ProcessSelfAvif(task_id.GetId());
        } else if (task_data_ptr->GetProcType() == kVideoCheck) {
            rst = VIDEO_CHECK_PROC.Process(task_id.GetId());
        }else {
            rst = IMG_DJ_CMD_PROC.Process(task_id.GetId());
        }
        stat_info.proc_end_time_ = Util::GetTimeStamp();
        stat_info.proc_total_time_ = stat_info.proc_end_time_ - stat_info.proc_start_time_;

        auto& reply = task_data_ptr->GetServerConnectionPtr()->GetReply();
        WLock lock(reply.rp_phase_mutex_);
        if (reply.rp_phase != Reply::rp_start) {
            EndProcess(task_id.GetId());
            return false;
        }
        lock.unlock();

        if (rst) {

            //Response
            struct stat st;
            if (stat(task_data_ptr->GetOutputFile().c_str(), &st) != 0) {
                task_data_ptr->GetServerConnectionPtr()->SendError(Reply::kInternalServerError);
                EndProcess(task_id.GetId());
                SLOG_E << task_id.GetId() << " : " << "Get size of output file: " << task_data_ptr->GetOutputFile() << " failed!";
            }
            stat_info.rst_size_ = st.st_size;
            stat_info.proc_rst_ = true;
            SLOG_D << task_id.GetId() << " : start to reply.";
            rst = SendReplyHeader(task_data_ptr->GetServerConnectionPtr(), st.st_size, task_data_ptr->GetTargetSuffix());
        } else {
            if (task_data_ptr->GetProcType() == kAudioConvert) {
                AudioProcTaskPtr audio_proc_task_ptr =
                        boost::dynamic_pointer_cast<AudioProcTask>(task_data_ptr);
                std::string reply =
                        audio_cmd_proc::GenErrReply(Reply::kInternalServerError, audio_proc_task_ptr->GetErrCode());
                audio_proc_task_ptr->GetServerConnectionPtr()->SendCustomReply(reply, Reply::kInternalServerError);
            } else {
                task_data_ptr->GetServerConnectionPtr()->SendError(Reply::kInternalServerError);
            }
            EndProcess(task_id.GetId());
        }
    } else {
        return false;
    }
    return rst;
}

io_service& imgsrv::Worker::GetIoService() {
    return ios_;
}

bool imgsrv::Worker::SendReplyHeader(ServerConnectionPtr& serv_conn, size_t data_size, const std::string& cont_type) {
    auto& reply = serv_conn->GetReply();
    WLock lock(reply.rp_phase_mutex_);
    if (reply.rp_phase == Reply::rp_start) {
        reply.rp_phase = Reply::rp_header;
    } else {
        return false;
    }
    lock.unlock();
    return serv_conn->StartReply(data_size, cont_type);
}

bool imgsrv::Worker::SendReplyBody(ServerConnectionPtr serv_conn, const std::string& task_id) {
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        auto& reply = serv_conn->GetReply();
        WLock lock(reply.rp_phase_mutex_);
        if (reply.rp_phase == Reply::rp_header || reply.rp_phase == Reply::rp_body) {
            reply.rp_phase = Reply::rp_body;
        } else {
            return false;
        }
        lock.unlock();

        std::ifstream is(task_data_ptr->GetOutputFile(), std::ios::in | std::ios::binary);
        if (!is) {
            EndProcess(task_id);
            return false;
        }
        is.seekg (0, std::ifstream::end);
        int length = is.tellg();
        is.seekg (0, std::ifstream::beg);
        std::vector<char>& buffer = serv_conn->GetBuffer();
        buffer.resize(length);
        is.read(&buffer[0], length);
        is.close();
        serv_conn->SetReplyCurLen(length);
        serv_conn->SendRsp(length);
        EndProcess(task_id);
    } else {
        return false;
    }

    return true;
}

void imgsrv::Worker::EndProcess(const std::string& task_id) {
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        task_data_ptr->GetServerConnectionPtr()->CancelTimer();
    }
    CONTAINER.CancelTask(task_id);

}



