/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : img_wm_proc.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019/8/27
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
 
#include <unistd.h>
#include "img_wm_proc.h"
#include "log_adapter.h"
#include "container.h"
#include "command_executor.h"

imgsrv::ImgWmProc::ImgWmProc()
        : bin_path_(""),
          /*wm_bash_(""),*/
          output_path_(""),
          font_path_(""),
          hevc_thread_num_(0) {

}

bool
imgsrv::ImgWmProc::Init(/*const std::string& gm_command,*/
        const std::string& output_path,
        const std::string& third_path,
        int hevc_thread_num) {
    bin_path_ = third_path + "/bin";
    output_path_ = output_path;
    font_path_ = third_path + "/font";
    hevc_thread_num_ = hevc_thread_num;

    std::string wm_bash = bin_path_ + "/trans_detection.sh";
    std::string gm_command = bin_path_ + "/gm";
    if (access(gm_command.c_str(), 0)) {
        SLOG_E << gm_command << " is not accessible!";
        return false;
    }
    if (access(output_path_.c_str(), 0)) {
        SLOG_E << "Path: " << output_path_ << " is not accessible!";
        return false;
    }
    if (access(wm_bash.c_str(), 0)) {
        SLOG_E << "Path: " << wm_bash << " is not accessible!";
        return false;
    }

    std::string path = font_path_ + "/wqy-zenhei.ttf";
    if (access(path.c_str(), 0)) {
        SLOG_E << "Load wqy-zenhei.ttf font failed. path=" << path;
        return false;
    }
    path = font_path_ + "/wqy-microhei.ttf";
    if (access(path.c_str(), 0)) {
        SLOG_E << "Load wqy-microhei.ttf font failed. path=" << path;
        return false;
    }
    path = font_path_ + "/fangzhengshusong.ttf";
    if (access(path.c_str(), 0)) {
        SLOG_E << "Load fangzhengshusong.ttf font failed. path=" << path;
        return false;
    }
    path = font_path_ + "/fangzhengkaiti.ttf";
    if (access(path.c_str(), 0)) {
        SLOG_E << "Load fangzhengkaiti.ttf font failed. path=" << path;
        return false;
    }
    path = font_path_ + "/fangzhengheiti.TTF";
    if (access(path.c_str(), 0)) {
        SLOG_E << "Load fangzhengheiti.TTF font failed. path=" << path;
        return false;
    }
    path = font_path_ + "/fangzhengfangsong.TTF";
    if (access(path.c_str(), 0)) {
        SLOG_E << "Load fangzhengfangsong.TTF font failed. path=" << path;
        return false;
    }
    path = font_path_ + "/droidsansfallback.ttf";
    if (access(path.c_str(), 0)) {
        SLOG_E << "Load droidsansfallback.ttf font failed. path=" << path;
        return false;
    }
    return true;
}

bool imgsrv::ImgWmProc::Process(const std::string& task_id) {
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        if (task_data_ptr->GetProcType() != kImgWaterMark
            && task_data_ptr->GetProcType() != kTextWaterMark) {
            //TODO: what?
            return false;
        }

        std::string command;
        if (task_data_ptr->GetProcType() == kImgWaterMark) {
            ImgWmTaskPtr img_wm_task_ptr =
                    boost::dynamic_pointer_cast<ImgWmTask>(task_data_ptr);
            if ((img_wm_task_ptr->GetT() < 0 || img_wm_task_ptr->GetT() > 100)
                || (img_wm_task_ptr->GetP() < 1 || img_wm_task_ptr->GetP() > 9)
                || (img_wm_task_ptr->GetX() < -4096 || img_wm_task_ptr->GetX() > 4096)
                || (img_wm_task_ptr->GetY() < -4096 || img_wm_task_ptr->GetY() > 4096)) {
                SLOG_E << task_id << " : WM param is illegal. t=" << img_wm_task_ptr->GetT()
                       << " p=" << img_wm_task_ptr->GetP() << " x=" << img_wm_task_ptr->GetX()
                       << " y=" << img_wm_task_ptr->GetY();
                return false;
            }
            if (!ConvertImgWmCmd(img_wm_task_ptr, command)) {
                SLOG_E << "convert wm img command failed.";
                return false;
            }
        } else {
            TxtWmTaskPtr txt_wm_task_ptr =
                    boost::dynamic_pointer_cast<TxtWmTask>(task_data_ptr);
            if ((txt_wm_task_ptr->GetT() < 0 || txt_wm_task_ptr->GetT() > 100)
                || (txt_wm_task_ptr->GetP() < 1 || txt_wm_task_ptr->GetP() > 9)
                || (txt_wm_task_ptr->GetX() < -4096 || txt_wm_task_ptr->GetX() > 4096)
                || (txt_wm_task_ptr->GetY() < -4096 || txt_wm_task_ptr->GetY() > 4096)) {
                SLOG_E << task_id << " : WM param is illegal. t=" << txt_wm_task_ptr->GetT()
                       << " p=" << txt_wm_task_ptr->GetP() << " x=" << txt_wm_task_ptr->GetX()
                       << " y=" << txt_wm_task_ptr->GetY();
                return false;
            }
            if (!ConvertTxtWmCmd(txt_wm_task_ptr, command)) {
                SLOG_E << "convert wm img command failed.";
                return false;
            }
        }
        Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
        stat_info.call_start_time_ = Util::GetTimeStamp();
        SLOG_I << task_data_ptr->GetTaskId() << " : " << command;
        int ret_code;
        if (!CE.RunCommand(command, task_data_ptr->GetTaskId(), task_data_ptr->GetTimeOut(), ret_code)) {
            SLOG_E << task_data_ptr->GetTaskId() << " : system command failed. " << command
                   << "; errno=" << errno << " " <<  strerror(errno);
            return false;
        }
        stat_info.call_end_time_ = Util::GetTimeStamp();
        stat_info.call_total_time_ = stat_info.call_end_time_ - stat_info.call_start_time_;
//        std::string output_path = output_path_ + "/out." + task_data_ptr->GetTargetSuffix();
        std::string output_path = GetOutputFile(task_data_ptr->GetTaskId(), task_data_ptr->GetTargetSuffix());
        if (access(output_path.c_str(), 0)) {
            SLOG_E << task_data_ptr->GetTaskId() << " : not include output. path=" << output_path;
            return false;
        }

        if (task_data_ptr->GetDoCrypt() == kEncryptResult
            || task_data_ptr->GetDoCrypt() == kDoBoth) {

            bool rst = Util::Aes256CbcEncryptFile(
                    task_data_ptr->GetCipherKey(),
                    output_path,
                    output_path);
            if (!rst) {
                SLOG_E << task_data_ptr->GetTaskId() << " : decrypt file error.";
                return false;
            }
        }
        task_data_ptr->SetOutputFile(output_path);

    } else {

        return false;
    }

    return true;
}

bool imgsrv::ImgWmProc::ConvertImgWmCmd(ImgWmTaskPtr& img_wm_task_ptr, std::string& command) {
    if (access(img_wm_task_ptr->GetInputFile().c_str(), 0)) {
        SLOG_E << img_wm_task_ptr->GetTaskId() << " : pic download failed.";
        return false;
    }
    if (img_wm_task_ptr->GetDoCrypt() == kDecryptFile
        || img_wm_task_ptr->GetDoCrypt() == kDoBoth) {

        bool rst = Util::Aes256CbcDecryptFile(
                img_wm_task_ptr->GetCipherKey(),
                img_wm_task_ptr->GetInputFile(),
                img_wm_task_ptr->GetInputFile());
        if (!rst) {
            SLOG_E << img_wm_task_ptr->GetTaskId() << " : decrypt file error.";
            return false;
        }
    }
    if (access(img_wm_task_ptr->GetWmFile().c_str(), 0)) {
        SLOG_E << img_wm_task_ptr->GetTaskId() << " : logo download failed.";
        return false;
    }
    if (img_wm_task_ptr->GetDoCrypt() == kDecryptFile
        || img_wm_task_ptr->GetDoCrypt() == kDoBoth) {

        bool rst = Util::Aes256CbcDecryptFile(
                img_wm_task_ptr->GetCipherKey(),
                img_wm_task_ptr->GetWmFile(),
                img_wm_task_ptr->GetWmFile());
        if (!rst) {
            SLOG_E << img_wm_task_ptr->GetTaskId() << " : decrypt file error.";
            return false;
        }
    }

    std::string tmp_dir = output_path_ + "/" + img_wm_task_ptr->GetTaskId();
    std::string logo = img_wm_task_ptr->GetWmFile();
    std::string gm_command = bin_path_ + "/gm";
    std::stringstream ss;
    if (img_wm_task_ptr->GetWp() > 1 && img_wm_task_ptr->GetWp() < 1000) {
        std::string logo_tmp = tmp_dir + "/logo_tmp.png";
        ss << "ww=`" << gm_command << " identify " << img_wm_task_ptr->GetInputFile()
           << " -format \"%w\"`; hh=`" << gm_command << " identify "
           << img_wm_task_ptr->GetInputFile() << " -format \"%h\"`; "
           << gm_command << " convert " << img_wm_task_ptr->GetWmFile()
           << " -resize ${ww}x${hh} -resize " << img_wm_task_ptr->GetWp()
           << "% " << logo_tmp << ";";
        logo = logo_tmp;
    }

    std::string p_cmd = ConvertP(img_wm_task_ptr->GetP());

//    std::string output_path = output_path_ + "/out." + img_wm_task_ptr->GetTargetSuffix();
    std::string output_path = GetOutputFile(img_wm_task_ptr->GetTaskId(), img_wm_task_ptr->GetTargetSuffix());
    ss << gm_command << " composite " << logo << " " << img_wm_task_ptr->GetInputFile()
       << p_cmd << " -geometry +" << img_wm_task_ptr->GetX() << "+"
       << img_wm_task_ptr->GetY() << " -dissolve " << img_wm_task_ptr->GetT()
       << " -define hevc:numberOfThreads=" << hevc_thread_num_ << " " << output_path;
    command = ss.str();
    return true;
}

bool imgsrv::ImgWmProc::ConvertTxtWmCmd(TxtWmTaskPtr& txt_wm_task_ptr, std::string& command) {
    if (txt_wm_task_ptr->GetDoCrypt() == kDecryptFile
        || txt_wm_task_ptr->GetDoCrypt() == kDoBoth) {

        bool rst = Util::Aes256CbcDecryptFile(
                txt_wm_task_ptr->GetCipherKey(),
                txt_wm_task_ptr->GetInputFile(),
                txt_wm_task_ptr->GetInputFile());
        if (!rst) {
            SLOG_E << txt_wm_task_ptr->GetTaskId() << " : decrypt file error.";
            return false;
        }
    }
    std::string p_cmd = ConvertP(txt_wm_task_ptr->GetP());
    std::string font_path;
    switch (txt_wm_task_ptr->GetFontType()) {
        case TxtWmTask::kWqyZenHei:
            font_path = font_path_ + "/wqy-zenhei.ttf";
            break;
        case TxtWmTask::kWqyMicroHei:
            font_path += font_path_ + "/wqy-microhei.ttf";
            break;
        case TxtWmTask::kFangZhengShuSong:
            font_path += font_path_ + "/fangzhengshusong.ttf";
            break;
        case TxtWmTask::kFangZhengKaiTi:
            font_path += font_path_ + "/fangzhengkaiti.ttf";
            break;
        case TxtWmTask::kFangZhengHeiTi:
            font_path += font_path_ + "/fangzhengheiti.TTF";
            break;
        case TxtWmTask::kFangZhengFangSong:
            font_path += font_path_ + "/fangzhengfangsong.TTF";
            break;
        case TxtWmTask::kDroidSansFallBack:
            font_path += font_path_ + "/droidsansfallback.ttf";
            break;
        default:
            SLOG_E << txt_wm_task_ptr->GetTaskId() << " : unsupport font. ="
                   << txt_wm_task_ptr->GetFontType();
            return false;
    }

    std::string font_cmd = " -font " + font_path;
    std::string tmp_dir = output_path_ + "/" + txt_wm_task_ptr->GetTaskId();
    std::string text_out = tmp_dir + "/text.png";
    std::string text = txt_wm_task_ptr->GetText();
    Util::RemoveUnsafeChar(text);

    std::string color = txt_wm_task_ptr->GetColor();
    Util::RemoveUnsafeChar(color);
    std::stringstream ss;
//    std::string output_path = tmp_dir + "/out." + txt_wm_task_ptr->GetTargetSuffix();
    std::string output_path = GetOutputFile(txt_wm_task_ptr->GetTaskId(), txt_wm_task_ptr->GetTargetSuffix());
    if (txt_wm_task_ptr->IsFill()) {
        std::string wm_bash = bin_path_ + "/trans_detection.sh";
        if (color[0] == '#') color.erase(0, 1);
        ss << wm_bash << " 4 " << bin_path_ << " " << txt_wm_task_ptr->GetInputFile()
           << " " << output_path << " " << font_path << " " << text << " "
           << txt_wm_task_ptr->GetSize() << " " << txt_wm_task_ptr->GetX() << " "
           << txt_wm_task_ptr->GetY() << " " << color << " " << txt_wm_task_ptr->GetT() << " "
           << txt_wm_task_ptr->GetS() << " " << txt_wm_task_ptr->GetRotate() << " 1";

    } else {
        std::string gm_command = bin_path_ + "/gm";
        ss << gm_command << " convert " << font_cmd << " -fill \"" << color
           << "\" -pointsize " << txt_wm_task_ptr->GetSize() << " -background transparent label:\'"
           << text << "\' " << text_out << "; " << gm_command << " composite "
           << text_out << " " << txt_wm_task_ptr->GetInputFile() << p_cmd
           << " -geometry +" << txt_wm_task_ptr->GetX() << "+" << txt_wm_task_ptr->GetY()
           << " -dissolve " << txt_wm_task_ptr->GetT() << " " << output_path;

    }
    command = ss.str();

    return true;
}

std::string imgsrv::ImgWmProc::ConvertP(int p) {
    std::string gm_cmd = " -gravity ";
    switch (p) {
        case 1:
            gm_cmd += "NorthWest";
            break;
        case 2:
            gm_cmd += "North";
            break;
        case 3:
            gm_cmd += "NorthEast";
            break;
        case 4:
            gm_cmd += "West";
            break;
        case 5:
            gm_cmd += "Center";
            break;
        case 6:
            gm_cmd += "East";
            break;
        case 7:
            gm_cmd += "SouthWest";
            break;
        case 8:
            gm_cmd += "South";
            break;
        case 9:
            gm_cmd += "SouthEast";
            break;
        default:
            gm_cmd += "Center";
            break;
    }
    return gm_cmd;
}

std::string imgsrv::ImgWmProc::GetOutputFile(const std::string& task_id, const std::string& target_suffix) {
    return output_path_ + "/" + task_id + "/out." + target_suffix;
}

/*
void imgsrv::ImgWmProc::RemoveUnsafeChar(std::string& text) {
    StringReplace(text, "`", "");
    StringReplace(text, "'", "");
    StringReplace(text, "\"", "");
    StringReplace(text, ")", "");
    StringReplace(text, "(", "");
    StringReplace(text, ">", "");
    StringReplace(text, "<", "");
    StringReplace(text, ";", "");
    StringReplace(text, ".", "");
    StringReplace(text, "&", "");
    StringReplace(text, "|", "");
    StringReplace(text, ",", "");
    StringReplace(text, "$", "");
}

void imgsrv::ImgWmProc::StringReplace(std::string& s1, const std::string s2, const std::string s3) {
    std::string::size_type pos=0;
    std::string::size_type a=s2.size();
    std::string::size_type b=s3.size();
    while((pos=s1.find(s2,pos)) != std::string::npos) {
        s1.replace(pos,a,s3);
        pos += b;
    }
}
*/


