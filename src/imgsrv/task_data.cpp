/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : task_data.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-07
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include "task_data.h"

imgsrv::TaskData::TaskData()
        : task_id_(""),
          proc_type_(kTypeSafeguardBeg),
          call_type_(kBin),
          biz_type_(""),
          app_id_(""),
          target_type_(kErr),
          target_(""),
          target_suffix_("src"),
          input_file_(""),
          input_file_done_(false),
          cipher_key_(""),
          cipher_type_(kNone),
          do_crypt_(kDoNone),
          output_file_(""),
          time_out_(0),
          server_connection_ptr_(),
          fetch_connection_ptr_(){

}

imgsrv::TaskData::TaskData(const imgsrv::TaskData& other)
        : task_id_(other.task_id_),
          proc_type_(other.proc_type_),
          call_type_(other.call_type_),
          biz_type_(other.biz_type_),
          app_id_(other.app_id_),
          target_type_(other.target_type_),
          target_(other.target_),
          target_suffix_(other.target_suffix_),
          input_file_(other.input_file_),
          input_file_done_(other.input_file_done_),
          cipher_key_(other.cipher_key_),
          cipher_type_(other.cipher_type_),
          do_crypt_(other.do_crypt_),
          output_file_(other.output_file_),
          time_out_(other.time_out_),
          server_connection_ptr_(other.server_connection_ptr_),
          fetch_connection_ptr_(other.fetch_connection_ptr_){

}

imgsrv::TaskData& imgsrv::TaskData::operator=(const imgsrv::TaskData& other) {
    if (this != &other) {
        task_id_ = other.task_id_;
        proc_type_ = other.proc_type_;
        call_type_ = other.call_type_;
        biz_type_ = other.biz_type_;
        app_id_ = other.app_id_;
        target_type_ = other.target_type_;
        target_ = other.target_;
        target_suffix_ = other.target_suffix_;
        input_file_ = other.input_file_;
        input_file_done_ = other.input_file_done_;
        cipher_key_ = other.cipher_key_;
        cipher_type_ = other.cipher_type_;
        do_crypt_ = other.do_crypt_;
        output_file_ = other.output_file_;
        time_out_ = other.time_out_;
        server_connection_ptr_ = other.server_connection_ptr_;
        fetch_connection_ptr_ = other.fetch_connection_ptr_;
    }
    return *this;
}

imgsrv::TaskData::TaskData(imgsrv::TaskData&& other) noexcept
        : task_id_(std::move(other.task_id_)),
          proc_type_(other.proc_type_),
          call_type_(other.call_type_),
          biz_type_(std::move(other.biz_type_)),
          app_id_(std::move(other.app_id_)),
          target_type_(other.target_type_),
          target_(std::move(other.target_)),
          target_suffix_(std::move(other.target_suffix_)),
          input_file_(std::move(other.input_file_)),
          input_file_done_(other.input_file_done_),
          cipher_key_(other.cipher_key_),
          cipher_type_(other.cipher_type_),
          do_crypt_(other.do_crypt_),
          output_file_(std::move(other.output_file_)),
          time_out_(other.time_out_),
          server_connection_ptr_(std::move(other.server_connection_ptr_)),
          fetch_connection_ptr_(std::move(other.fetch_connection_ptr_)){

}

imgsrv::TaskData& imgsrv::TaskData::operator=(imgsrv::TaskData&& other) noexcept{
    if (this != &other) {
        task_id_ = std::move(other.task_id_);
        proc_type_ = other.proc_type_;
        call_type_ = other.call_type_;
        biz_type_ = std::move(other.biz_type_);
        app_id_ = std::move(other.app_id_);
        target_type_ = other.target_type_;
        target_ = std::move(other.target_);
        target_suffix_ = std::move(other.target_suffix_);
        input_file_ = std::move(other.input_file_);
        input_file_done_ = other.input_file_done_;
        cipher_key_ = other.cipher_key_;
        cipher_type_ = other.cipher_type_;
        do_crypt_ = other.do_crypt_;
        output_file_ = std::move(other.output_file_);
        time_out_ = other.time_out_;
        server_connection_ptr_ = std::move(other.server_connection_ptr_);
        fetch_connection_ptr_ = std::move(other.fetch_connection_ptr_);
    }
    return *this;
}


const std::string& imgsrv::TaskData::GetBizType() const {
    return biz_type_;
}

void imgsrv::TaskData::SetBizType(const std::string& biz_type) {
    biz_type_ = biz_type;
}

const std::string& imgsrv::TaskData::GetAppId() const {
    return app_id_;
}

void imgsrv::TaskData::SetAppId(const std::string& app_id) {
    app_id_ = app_id;
}

const std::string& imgsrv::TaskData::GetTaskId() const {
    return task_id_;
}

void imgsrv::TaskData::SetTaskId(const std::string& task_id) {
    task_id_ = task_id;
}

int imgsrv::TaskData::GetTimeOut() const {
    return time_out_;
}

void imgsrv::TaskData::SetTimeOut(int time_out) {
    time_out_ = time_out;
}

const std::string& imgsrv::TaskData::GetInputFile() const {
    return input_file_;
}

void imgsrv::TaskData::SetInputFile(const std::string& input_file) {
    input_file_ = input_file;
}

imgsrv::ProcType imgsrv::TaskData::GetProcType() const {
    return proc_type_;
}

void imgsrv::TaskData::SetProcType(imgsrv::ProcType proc_type) {
    proc_type_ = proc_type;
}

//const ServerConnectionPtr& imgsrv::TaskData::GetServerConnectionPtr() const {
//    return server_connection_ptr_;
//}
ServerConnectionPtr& imgsrv::TaskData::GetServerConnectionPtr() {
    return server_connection_ptr_;
}

void imgsrv::TaskData::SetServerConnectionPtr(const ServerConnectionPtr& server_connection_ptr) {
    server_connection_ptr_ = server_connection_ptr;
}

const std::string& imgsrv::TaskData::GetTarget() const {
    return target_;
}

void imgsrv::TaskData::SetTarget(const std::string& target) {
    target_ = target;
}

imgsrv::TargetType imgsrv::TaskData::GetTargetType() const {
    return target_type_;
}

void imgsrv::TaskData::SetTargetType(imgsrv::TargetType target_type) {
    target_type_ = target_type;
}

const FetchConnectionPtr& imgsrv::TaskData::GetFetchConnectionPtr() const {
    return fetch_connection_ptr_;
}

void imgsrv::TaskData::SetFetchConnectionPtr(const FetchConnectionPtr& fetch_connection_ptr) {
    fetch_connection_ptr_ = fetch_connection_ptr;
}

bool imgsrv::TaskData::IsInputFileDone() const {
    return input_file_done_;
}

void imgsrv::TaskData::SetInputFileDone(bool imput_file_done) {
    input_file_done_ = imput_file_done;
}

imgsrv::TaskData::~TaskData() {

}

const std::string& imgsrv::TaskData::GetTargetSuffix() const {
    return target_suffix_;
}

void imgsrv::TaskData::SetTargetSuffix(const std::string& target_suffix) {
    target_suffix_ = target_suffix;
}

const std::string& imgsrv::TaskData::GetCipherKey() const {
    return cipher_key_;
}

void imgsrv::TaskData::SetCipherKey(const std::string& cipher_key) {
    cipher_key_ = cipher_key;
}

imgsrv::DoCrypt imgsrv::TaskData::GetDoCrypt() const {
    return do_crypt_;
}

void imgsrv::TaskData::SetDoCrypt(imgsrv::DoCrypt do_crypt) {
    do_crypt_ = do_crypt;
}

imgsrv::CipherType imgsrv::TaskData::GetCipherType() const {
    return cipher_type_;
}

void imgsrv::TaskData::SetCipherType(imgsrv::CipherType cipher_type) {
    cipher_type_ = cipher_type;
}

const std::string& imgsrv::TaskData::GetOutputFile() const {
    return output_file_;
}

void imgsrv::TaskData::SetOutputFile(const std::string& output_file) {
    output_file_ = output_file;
}

imgsrv::CallType imgsrv::TaskData::GetCallType() const {
    return call_type_;
}

void imgsrv::TaskData::SetCallType(imgsrv::CallType call_type) {
    call_type_ = call_type;
}

const std::string &imgsrv::TaskData::getUrl() const {
    return url_;
}

void imgsrv::TaskData::setUrl(const std::string &url) {
    url_ = url;
}

imgsrv::VideoProcTask::VideoProcTask(): TaskData(){}

imgsrv::VideoProcTask::VideoProcTask(const AudioProcTask& other): TaskData(){}

imgsrv::VideoProcTask::VideoProcTask(AudioProcTask&& other) noexcept : TaskData(std::move(other)) {}

const std::string &imgsrv::VideoProcTask::GetTemplateId() const {
    return template_id_;
}

void imgsrv::VideoProcTask::SetTemplateId(const std::string &templateId) {
    template_id_ = templateId;
}

imgsrv::VideoCheckTask::VideoCheckTask(): TaskData(){}

imgsrv::VideoCheckTask::VideoCheckTask(const VideoCheckTask& other): TaskData(){}

imgsrv::VideoCheckTask::VideoCheckTask(VideoCheckTask&& other) noexcept : TaskData(std::move(other)) {}

const std::string &imgsrv::VideoCheckTask::GetTemplateId() const {
    return template_id_;
}

void imgsrv::VideoCheckTask::SetTemplateId(const std::string &templateId) {
    template_id_ = templateId;
}

const std::string &imgsrv::VideoCheckTask::GetTemplateId() const {
    return template_id_;
}

void imgsrv::VideoCheckTask::SetTemplateId(const std::string &templateId) {
    template_id_ = templateId;
}

imgsrv::ImgProcTask::ImgProcTask()
        : TaskData(),
//          cipher_key_(""),
//          cipher_type_(""),
          param_1_(""),
          param_2_(""),
          target_md5_("")/*,
          target_suffix_("")*/ {

}

imgsrv::ImgProcTask::ImgProcTask(const imgsrv::ImgProcTask& other)
        : TaskData(other),
//          cipher_key_(other.cipher_key_),
//          cipher_type_(other.cipher_type_),
          param_1_(other.param_1_),
          param_2_(other.param_2_),
          target_md5_(other.target_md5_)/*,
          target_suffix_(other.target_suffix_)*/ {

}

imgsrv::ImgProcTask& imgsrv::ImgProcTask::operator=(const imgsrv::ImgProcTask& other) {
    if (this != &other) {
        TaskData::operator=(other);
//        cipher_key_ = other.cipher_key_;
//        cipher_type_ = other.cipher_type_;
        param_1_ = other.param_1_;
        param_2_ = other.param_2_;
        target_md5_ = other.target_md5_;
//        target_suffix_ = other.target_suffix_;
    }
    return *this;
}

imgsrv::ImgProcTask::ImgProcTask(imgsrv::ImgProcTask&& other) noexcept
        : TaskData(std::move(other)),
//          cipher_key_(std::move(other.cipher_key_)),
//          cipher_type_(std::move(other.cipher_type_)),
          param_1_(std::move(other.param_1_)),
          param_2_(std::move(other.param_2_)),
          target_md5_(std::move(other.target_md5_))/*,
          target_suffix_(std::move(other.target_suffix_))*/ {

}

imgsrv::ImgProcTask& imgsrv::ImgProcTask::operator=(imgsrv::ImgProcTask&& other) noexcept {
    if (this != &other) {
        TaskData::operator=(std::move(other));
//        cipher_key_ = std::move(other.cipher_key_);
//        cipher_type_ = std::move(other.cipher_type_);
        param_1_ = std::move(other.param_1_);
        param_2_ = std::move(other.param_2_);
        target_md5_ = std::move(other.target_md5_);
//        target_suffix_ = std::move(other.target_suffix_);
    }
    return *this;
}

//const std::string& imgsrv::ImgProcTask::GetCipherKey() const {
//    return cipher_key_;
//}
//
//void imgsrv::ImgProcTask::SetCipherKey(const std::string& cipher_key) {
//    cipher_key_ = cipher_key;
//}
//
//const std::string& imgsrv::ImgProcTask::GetCipherType() const {
//    return cipher_type_;
//}
//
//void imgsrv::ImgProcTask::SetCipherType(const std::string& cipher_type) {
//    cipher_type_ = cipher_type;
//}

const std::string& imgsrv::ImgProcTask::GetParam1() const {
    return param_1_;
}

void imgsrv::ImgProcTask::SetParam1(const std::string& param_1) {
    param_1_ = param_1;
}

const std::string& imgsrv::ImgProcTask::GetParam2() const {
    return param_2_;
}

void imgsrv::ImgProcTask::SetParam2(const std::string& param_2) {
    param_2_ = param_2;
}

const std::string& imgsrv::ImgProcTask::GetTargetMd5() const {
    return target_md5_;
}

void imgsrv::ImgProcTask::SetTargetMd5(const std::string& target_md_5) {
    target_md5_ = target_md_5;
}

const std::string &imgsrv::ImgProcTask::getAlgParam() const {
    return alg_param_;
}

void imgsrv::ImgProcTask::setAlgParam(const std::string &algParam) {
    alg_param_ = algParam;
}

/*const std::string& imgsrv::ImgProcTask::GetTargetSuffix() const {
    return target_suffix_;
}

void imgsrv::ImgProcTask::SetTargetSuffix(const std::string& target_suffix) {
    target_suffix_ = target_suffix;
}*/

imgsrv::ImgWmTask::ImgWmTask()
        : wmid_(""),
          wm_file_(""),
          wm_file_done_(false),
          param_1_(""),
          voffset_(0),
          t_(80),
          p_(5),
          x_(5),
          y_(10),
          wp_(0),
          wm_fetch_connection_ptr_(){

}

imgsrv::ImgWmTask::ImgWmTask(const imgsrv::ImgWmTask& other)
        : TaskData(other),
          wmid_(other.wmid_),
          wm_file_(other.wm_file_),
          wm_file_done_(other.wm_file_done_),
          param_1_(other.param_1_),
          voffset_(other.voffset_),
          t_(other.t_),
          p_(other.p_),
          x_(other.x_),
          y_(other.y_),
          wp_(other.wp_),
          wm_fetch_connection_ptr_(other.wm_fetch_connection_ptr_) {

}

imgsrv::ImgWmTask& imgsrv::ImgWmTask::operator=(const imgsrv::ImgWmTask& other) {
    if (this != &other) {
        TaskData::operator=(other);
        wmid_ = other.wmid_;
        wm_file_ = other.wm_file_;
        wm_file_done_ = other.wm_file_done_;
        param_1_ = other.param_1_;
        voffset_ = other.voffset_;
        t_ = other.t_;
        p_ = other.p_;
        x_ = other.x_;
        y_ = other.y_;
        wp_ = other.wp_;
        wm_fetch_connection_ptr_ = other.wm_fetch_connection_ptr_;
    }
    return *this;
}

imgsrv::ImgWmTask::ImgWmTask(imgsrv::ImgWmTask&& other) noexcept
        : TaskData(std::move(other)),
          wmid_(std::move(other.wmid_)),
          wm_file_(std::move(other.wm_file_)),
          wm_file_done_(other.wm_file_done_),
          param_1_(std::move(other.param_1_)),
          voffset_(other.voffset_),
          t_(other.t_),
          p_(other.p_),
          x_(other.x_),
          y_(other.y_),
          wp_(other.wp_),
          wm_fetch_connection_ptr_(std::move(other.wm_fetch_connection_ptr_)) {

}

imgsrv::ImgWmTask& imgsrv::ImgWmTask::operator=(imgsrv::ImgWmTask&& other) noexcept {
    if (this != &other) {
        TaskData::operator=(std::move(other));
        wmid_ = std::move(other.wmid_);
        wm_file_ = std::move(other.wm_file_);
        wm_file_done_ = other.wm_file_done_;
        param_1_ = std::move(other.param_1_);
        voffset_ = other.voffset_;
        t_ = other.t_;
        p_ = other.p_;
        x_ = other.x_;
        y_ = other.y_;
        wp_ = other.wp_;
        wm_fetch_connection_ptr_ = std::move(other.wm_fetch_connection_ptr_);
    }
    return *this;
}

const std::string& imgsrv::ImgWmTask::GetWmid() const {
    return wmid_;
}

void imgsrv::ImgWmTask::SetWmid(const std::string& wmid) {
    wmid_ = wmid;
}

const std::string& imgsrv::ImgWmTask::GetParam1() const {
    return param_1_;
}

void imgsrv::ImgWmTask::SetParam1(const std::string& param_1) {
    param_1_ = param_1;
}

int imgsrv::ImgWmTask::GetVoffset() const {
    return voffset_;
}

void imgsrv::ImgWmTask::SetVoffset(int voffset) {
    voffset_ = voffset;
}

int imgsrv::ImgWmTask::GetT() const {
    return t_;
}

void imgsrv::ImgWmTask::SetT(int t) {
    t_ = t;
}

int imgsrv::ImgWmTask::GetP() const {
    return p_;
}

void imgsrv::ImgWmTask::SetP(int p) {
    p_ = p;
}

int imgsrv::ImgWmTask::GetX() const {
    return x_;
}

void imgsrv::ImgWmTask::SetX(int x) {
    x_ = x;
}

int imgsrv::ImgWmTask::GetY() const {
    return y_;
}

void imgsrv::ImgWmTask::SetY(int y) {
    y_ = y;
}

int imgsrv::ImgWmTask::GetWp() const {
    return wp_;
}

void imgsrv::ImgWmTask::SetWp(int wp) {
    wp_ = wp;
}

const std::string& imgsrv::ImgWmTask::GetWmFile() const {
    return wm_file_;
}

void imgsrv::ImgWmTask::SetWmFile(const std::string& wm_file) {
    wm_file_ = wm_file;
}

bool imgsrv::ImgWmTask::IsWmFileDone() const {
    return wm_file_done_;
}

void imgsrv::ImgWmTask::SetWmFileDone(bool wm_file_done) {
    wm_file_done_ = wm_file_done;
}

const FetchConnectionPtr& imgsrv::ImgWmTask::GetWmFetchConnectionPtr() const {
    return wm_fetch_connection_ptr_;
}

void imgsrv::ImgWmTask::SetWmFetchConnectionPtr(const FetchConnectionPtr& wm_fetch_connection_ptr) {
    wm_fetch_connection_ptr_ = wm_fetch_connection_ptr;
}

imgsrv::TxtWmTask::TxtWmTask()
        : voffset_(0),
          t_(80),
          p_(5),
          x_(5),
          y_(10),
          text_(""),
          font_type_(kFangZhengHeiTi),
          color_("#FFFFFF"),
          size_(40),
          s_(0),
          rotate_(0),
          fill_(false){

}

imgsrv::TxtWmTask::TxtWmTask(const imgsrv::TxtWmTask& other)
        : voffset_(other.voffset_),
          t_(other.t_),
          p_(other.p_),
          x_(other.x_),
          y_(other.y_),
          text_(other.text_),
          font_type_(other.font_type_),
          color_(other.color_),
          size_(other.size_),
          s_(other.s_),
          rotate_(other.rotate_),
          fill_(other.fill_){

}

imgsrv::TxtWmTask& imgsrv::TxtWmTask::operator=(const imgsrv::TxtWmTask& other) {
    if (this != &other) {
        TaskData::operator=(other);
        voffset_ = other.voffset_;
        t_ = other.t_;
        p_ = other.p_;
        x_ = other.x_;
        y_ = other.y_;
        text_ = other.text_;
        font_type_ = other.font_type_;
        color_ = other.color_;
        size_ = other.size_;
        s_ = other.s_;
        rotate_ = other.rotate_;
        fill_ = other.fill_;
    }
    return *this;
}

imgsrv::TxtWmTask::TxtWmTask(imgsrv::TxtWmTask&& other) noexcept
        : voffset_(other.voffset_),
          t_(other.t_),
          p_(other.p_),
          x_(other.x_),
          y_(other.y_),
          text_(std::move(other.text_)),
          font_type_(other.font_type_),
          color_(std::move(other.color_)),
          size_(other.size_),
          s_(other.s_),
          rotate_(other.rotate_),
          fill_(other.fill_){

}

imgsrv::TxtWmTask& imgsrv::TxtWmTask::operator=(imgsrv::TxtWmTask&& other) noexcept {
    if (this != &other) {
        TaskData::operator=(std::move(other));
        voffset_ = other.voffset_;
        t_ = other.t_;
        p_ = other.p_;
        x_ = other.x_;
        y_ = other.y_;
        text_ = std::move(other.text_);
        font_type_ = other.font_type_;
        color_ = std::move(other.color_);
        size_ = other.size_;
        s_ = other.s_;
        rotate_ = other.rotate_;
        fill_ = other.fill_;
    }
    return *this;
}

int imgsrv::TxtWmTask::GetVoffset() const {
    return voffset_;
}

void imgsrv::TxtWmTask::SetVoffset(int voffset) {
    voffset_ = voffset;
}

int imgsrv::TxtWmTask::GetT() const {
    return t_;
}

void imgsrv::TxtWmTask::SetT(int t) {
    t_ = t;
}

int imgsrv::TxtWmTask::GetP() const {
    return p_;
}

void imgsrv::TxtWmTask::SetP(int p) {
    p_ = p;
}

int imgsrv::TxtWmTask::GetX() const {
    return x_;
}

void imgsrv::TxtWmTask::SetX(int x) {
    x_ = x;
}

int imgsrv::TxtWmTask::GetY() const {
    return y_;
}

void imgsrv::TxtWmTask::SetY(int y) {
    y_ = y;
}

const std::string& imgsrv::TxtWmTask::GetText() const {
    return text_;
}

void imgsrv::TxtWmTask::SetText(const std::string& text) {
    text_ = text;
}

imgsrv::TxtWmTask::FontType imgsrv::TxtWmTask::GetFontType() const {
    return font_type_;
}

void imgsrv::TxtWmTask::SetFontType(imgsrv::TxtWmTask::FontType font_type) {
    font_type_ = font_type;
}

const std::string& imgsrv::TxtWmTask::GetColor() const {
    return color_;
}

void imgsrv::TxtWmTask::SetColor(const std::string& color) {
    color_ = color;
}

int imgsrv::TxtWmTask::GetSize() const {
    return size_;
}

void imgsrv::TxtWmTask::SetSize(int size) {
    size_ = size;
}

int imgsrv::TxtWmTask::GetS() const {
    return s_;
}

void imgsrv::TxtWmTask::SetS(int s) {
    s_ = s;
}

int imgsrv::TxtWmTask::GetRotate() const {
    return rotate_;
}

void imgsrv::TxtWmTask::SetRotate(int rotate) {
    rotate_ = rotate;
}

bool imgsrv::TxtWmTask::IsFill() const {
    return fill_;
}

void imgsrv::TxtWmTask::SetFill(bool fill) {
    fill_ = fill;
}

imgsrv::TaskId::TaskId() : id_(""){

}

imgsrv::TaskId::TaskId(const imgsrv::TaskId& other) : id_(other.id_){

}

imgsrv::TaskId& imgsrv::TaskId::operator=(const imgsrv::TaskId& other) {
    if (this != &other) {
        id_ = other.id_;
    }
    return *this;
}

imgsrv::TaskId::TaskId(imgsrv::TaskId&& other) noexcept : id_(std::move(other.id_)){

}

imgsrv::TaskId& imgsrv::TaskId::operator=(imgsrv::TaskId&& other) noexcept {
    if (this != &other) {
        id_ = std::move(other.id_);
    }
    return *this;
}

const std::string& imgsrv::TaskId::GetId() const {
    return id_;
}

void imgsrv::TaskId::SetId(const std::string& id) {
    id_ = id;
}

imgsrv::AudioProcTask::AudioProcTask()
        : TaskData(),
          sf_(kerr),
          ssr_(-1),
          sbr_(-1),
          df_(kerr),
          dsr_(-1),
          dbr_(-1),
          err_code_(-1){
    audio_type_map_[kpcm] = "pcm";
    audio_type_map_[kamr] = "amr";
    audio_type_map_[ksilk] = "silk";
}

imgsrv::AudioProcTask::AudioProcTask(const imgsrv::AudioProcTask& other)
        : TaskData(other),
          sf_(other.sf_),
          ssr_(other.ssr_),
          sbr_(other.sbr_),
          df_(other.df_),
          dsr_(other.dsr_),
          dbr_(other.dbr_),
          err_code_(other.err_code_){}

imgsrv::AudioProcTask::AudioProcTask(imgsrv::AudioProcTask&& other)
        : TaskData(std::move(other)),
          sf_(std::move(other.sf_)),
          ssr_(std::move(other.ssr_)),
          sbr_(std::move(other.sbr_)),
          df_(std::move(other.df_)),
          dsr_(std::move(other.dsr_)),
          dbr_(std::move(other.dbr_)),
          err_code_(std::move(other.err_code_)){}

imgsrv::AudioProcTask& imgsrv::AudioProcTask::operator=(const imgsrv::AudioProcTask& other) {
    if (this != &other) {
        TaskData::operator=(other);
        sf_ = other.sf_;
        ssr_ = other.ssr_;
        sbr_ = other.sbr_;
        df_ = other.df_;
        dsr_ = other.dsr_;
        dbr_ = other.dbr_;
        err_code_ = other.err_code_;
    }
    return *this;
}

imgsrv::AudioProcTask& imgsrv::AudioProcTask::operator=(imgsrv::AudioProcTask&& other) {
    if (this != &other) {
        TaskData::operator=(std::move(other));
        sf_ = std::move(other.sf_);
        ssr_ = std::move(other.ssr_);
        sbr_ = std::move(other.sbr_);
        df_ = std::move(other.df_);
        dsr_ = std::move(other.dsr_);
        dbr_ = std::move(other.dbr_);
        err_code_ = std::move(other.err_code_);
    }
    return *this;
}

imgsrv::audio_type imgsrv::AudioProcTask::GetSf() const {
    return sf_;
}

void imgsrv::AudioProcTask::SetSf(imgsrv::audio_type sf) {
    sf_ = sf;
}

int imgsrv::AudioProcTask::GetSsr() const {
    return ssr_;
}

void imgsrv::AudioProcTask::SetSsr(int ssr) {
    ssr_ = ssr;
}

int imgsrv::AudioProcTask::GetSbr() const {
    return sbr_;
}

void imgsrv::AudioProcTask::SetSbr(int sbr) {
    sbr_ = sbr;
}

imgsrv::audio_type imgsrv::AudioProcTask::GetDf() const {
    return df_;
}

void imgsrv::AudioProcTask::SetDf(imgsrv::audio_type df) {
    df_ = df;
}

int imgsrv::AudioProcTask::GetDsr() const {
    return dsr_;
}

void imgsrv::AudioProcTask::SetDsr(int dsr) {
    dsr_ = dsr;
}

int imgsrv::AudioProcTask::GetDbr() const {
    return dbr_;
}

void imgsrv::AudioProcTask::SetDbr(int dbr) {
    dbr_ = dbr;
}

const std::map<imgsrv::audio_type, std::string>& imgsrv::AudioProcTask::GetAudioTypeMap() const {
    return audio_type_map_;
}

int imgsrv::AudioProcTask::GetErrCode() const {
    return err_code_;
}

void imgsrv::AudioProcTask::SetErrCode(int err_code) {
    err_code_ = err_code;
}
