/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : header.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_HEADER_H
#define IMGSRV_HEADER_H

#include <string>

namespace imgsrv {
/*struct Header {
    std::string name;
    std::string value;
};*/

class Header {
public:
    Header() : name(""), value(""){

    }
    std::string name;
    std::string value;
};
}
#endif //IMGSRV_HEADER_H
