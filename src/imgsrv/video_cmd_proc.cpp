//
// Created by t<PERSON><PERSON> on 2023/5/30.
//

#include <unistd.h>
#include "video_cmd_proc.h"
#include "log_adapter.h"
#include "container.h"
#include "command_executor.h"

imgsrv::VideoCmdProc::VideoCmdProc()
        : bin_path_(""),
          output_path_(""),
          video_thread_num_(0) {
}

bool imgsrv::VideoCmdProc::Init(const std::string& output_path,const std::string& third_path,int video_thread_num) {
    bin_path_ = third_path + "/bin";
    output_path_ = output_path;
    video_thread_num_ = video_thread_num;

    std::string video_bash = bin_path_ + "/trans_detection.sh";
    if (access(output_path_.c_str(), 0)) {
        SLOG_E << "Path: " << output_path_ << " is not accessible!";
        return false;
    }
    if (access(video_bash.c_str(), 0)) {
        SLOG_E << "Path: " << video_bash << " is not accessible!";
        return false;
    }
    return true;
}

bool imgsrv::VideoCmdProc::Process(const std::string& task_id) {

    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        if (task_data_ptr->GetProcType() != kVideoProc) {
            return false;
        }

        std::string output_path;
        std::string command;
        VideoProcTaskPtr video_proc_task_ptr = boost::dynamic_pointer_cast<VideoProcTask>(task_data_ptr);

        if(video_proc_task_ptr->GetTemplateId() == "video_info_parse"){
            if (!VideoInfoParseCmd(video_proc_task_ptr, command,output_path)) {
                SLOG_E << "video info parse command failed.";
                return false;
            }
        }else if(video_proc_task_ptr->GetTemplateId() == "video_extract_audio"){
            if (!VideoExtractAudioCmd(video_proc_task_ptr, command,output_path)) {
                SLOG_E << "video extract video command failed.";
                return false;
            }
        } else{
            SLOG_E << "video templateId error.";
            return false;
        }

        Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
        stat_info.call_start_time_ = Util::GetTimeStamp();
        SLOG_I << task_data_ptr->GetTaskId() << " : " << command;
        int ret_code;
        if (!CE.RunCommand(command, task_data_ptr->GetTaskId(), task_data_ptr->GetTimeOut(), ret_code)) {
            SLOG_E << task_data_ptr->GetTaskId() << " : system command failed. " << command<< "; errno=" << errno << " " <<  strerror(errno);
            return false;
        }

        stat_info.call_end_time_ = Util::GetTimeStamp();
        stat_info.call_total_time_ = stat_info.call_end_time_ - stat_info.call_start_time_;
        if (access(output_path.c_str(), 0)) {
            SLOG_E << task_data_ptr->GetTaskId() << " : not include output. path=" << output_path;
            return false;
        }
        task_data_ptr->SetOutputFile(output_path);

    } else {
        return false;
    }
    return true;
}

bool imgsrv::VideoCmdProc::VideoExtractAudioCmd(VideoProcTaskPtr& video_proc_task_ptr, std::string& command,std::string& output_path) {
    std::stringstream ss;
    output_path = GetOutputFile(video_proc_task_ptr->GetTaskId(), video_proc_task_ptr->GetTargetSuffix());
    std::string video_bash = bin_path_ + "/trans_detection.sh";
    ss << video_bash << " 6 " << bin_path_ << " " << video_proc_task_ptr->GetInputFile()<< " " << output_path;
    command = ss.str();
    return true;
}
// echo 'command' | timeout 30 - 需要关注下url中带有&等特殊字符 
bool imgsrv::VideoCmdProc::VideoInfoParseCmd(VideoProcTaskPtr& video_proc_task_ptr, std::string& command,std::string& output_path) {
    std::stringstream ss;
    output_path = output_path_ + "/" + video_proc_task_ptr->GetTaskId()+"/info.json";
    std::string video_bash = bin_path_ + "/trans_detection.sh";
    ss << video_bash << " 7 " << bin_path_ << " '\\''" << video_proc_task_ptr->getUrl()<< "'\\'' " << output_path;
    command = ss.str();
    return true;
}

std::string imgsrv::VideoCmdProc::GetOutputFile(const std::string& task_id, const std::string& target_suffix) {
    return output_path_ + "/" + task_id + "/out." + target_suffix;
}



