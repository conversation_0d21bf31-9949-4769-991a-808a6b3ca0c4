/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : video_check_proc.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2024-01-01
 * @version  :
 * @brief    : Video check processor using Python script
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_VIDEO_CHECK_PROC_H
#define IMGSRV_VIDEO_CHECK_PROC_H

#include <boost/serialization/singleton.hpp>
#include <string>
#include "task_data.h"

namespace imgsrv {

class VideoCheckProc : public boost::serialization::singleton<VideoCheckProc> {
public:
    VideoCheckProc();
    
    bool Init(const std::string& output_path,
              const std::string& third_path);
    
    bool Process(const std::string& task_id);

private:
    bool DoVideoCheck(const std::string& task_id, 
                      TaskDataPtr& task_data_ptr, 
                      const std::string& params);
    
    std::string GetTmpPath(const std::string& task_id);
    std::string GetPythonScriptPath();
    
private:
    std::string output_path_;
    std::string third_path_;
};

}

#define VIDEO_CHECK_PROC imgsrv::VideoCheckProc::get_mutable_instance()

#endif //IMGSRV_VIDEO_CHECK_PROC_H
