/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : command_executor.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019/8/23
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include "command_executor.h"
#include "log_adapter.h"
#include "config.h"
#include "container.h"
#include <csignal>
#include <sys/wait.h>
#include <fcntl.h>
#include <sys/prctl.h>
#include <iostream>
#include <boost/algorithm/string/predicate.hpp>
#include <boost/algorithm/string/classification.hpp>
#include <boost/algorithm/string/split.hpp>
//#include <fstream>

imgsrv::CommandExecutor::CommandExecutor() : total_process_num_(0), working_process_num_(0){
    standby_pool_.clear();
    running_pool_.clear();
}

//imgsrv::CommandExecutor::~CommandExecutor() {
//
//}

/*void imgsrv::CommandExecutor::SigchldHandler(int sig) {
    pid_t child_pid;
    int status;
    while ((child_pid = waitpid(-1, &status, WNOHANG)) > 0) {
        slog_e(g_ce_id) << "Child: " << child_pid << "is dead!";
        CE.DeleteChild(child_pid);
    }
    while (CE.GetWorkingProcessNum() < CE.GetTotalProcessNum()) {
        CE.CreateChildProcess();
    }
}*/

bool imgsrv::CommandExecutor::Init(
        int process_num,
        const std::string& tool_dir,
        const std::string& process_name) {
    total_process_num_ = process_num;
    tool_dir_ = tool_dir;
    for (int i = 0; i < total_process_num_; i++) {
        CreateChildProcess(process_name);
    }
    return true;
}
/*bool imgsrv::CommandExecutor::Init(int process_num, const std::string& tool_dir) {
    total_process_num_ = process_num;
    tool_dir_ = tool_dir;
    switch(fork()) {
        case -1:
            return false;
        case 0: {
//            struct sigaction sa;
//            sigemptyset(&sa.sa_mask);
//            sa.sa_flags = 0;
//            sa.sa_handler = SigchldHandler;
//            if (sigaction(SIGCHLD, &sa, NULL) == -1) {
//                return false;
//            }
//
//            sigset_t block_mask, empty_mask;
//            sigemptyset(&block_mask);
//            sigaddset(&block_mask, SIGCHLD);
//            if (sigprocmask(SIG_SETMASK, &block_mask, NULL) == -1) {
//                return false;
//            }
            signal(SIGCHLD, SigchldHandler);
            for (int i = 0; i < total_process_num_; i++) {
                CreateChildProcess();
            }

//            sigemptyset(&empty_mask);
//            while (true) {
//                if (sigsuspend(&empty_mask) == -1 && errno != EINTR)
//                sigCnt++;
//            }

        }
            break;
    }

    return true;
}*/

bool imgsrv::CommandExecutor::CreateChildProcess(const std::string& process_name) {
    slog_i(g_ce_id) << "CreateChildProcess";
    PidNode node{};
    node.status = kPidWait;
    if (0 != pipe(node.c2p_pipe)) {
        std::cout << "create chlid to parent pipe failed.";
        return false;
    }
    fcntl(node.c2p_pipe[0], F_SETFL, O_NONBLOCK);
    fcntl(node.c2p_pipe[1], F_SETFL, O_NONBLOCK);
    if (0 != pipe(node.p2c_pipe)) {
        std::cout << "create parent to chlid pipe failed.";
        return false;
    }
    fcntl(node.p2c_pipe[0], F_SETFL, O_NONBLOCK);
    fcntl(node.p2c_pipe[1], F_SETFL, O_NONBLOCK);

    node.pid = fork();
    if (node.pid < (pid_t)0) {
        std::cout << "fork child process failed. pid=" << node.pid;
        return false;
    }
    if ((pid_t)0 == node.pid) {
        prctl(PR_SET_NAME, process_name.c_str(), NULL, NULL, NULL);
        if(!CE_LOG_INIT(GET_CONF.ce_log_path, ::util::log::ONE_HOUR, (log_level)GET_CONF.log_level)) {
            std::cout << "Init CE log failed :" << GET_CONF.ce_log_path << std::endl;
            return -1;
        }
        int my_pid = getpid();
        close(node.p2c_pipe[1]);
        close(node.c2p_pipe[0]);
        slog_i(g_ce_id) << "create chlid pid=" << my_pid;
        int err_cnt = 0;
        while (true) {
            if (err_cnt >= 3) {
                slog_e(g_ce_id) << "chlid " << my_pid << " EPIPE catched "
                       << err_cnt << " times. exit";
                break;
            }
            std::string cmd = ReadFromPipe(node.p2c_pipe[0]);
            if (errno == EPIPE) {
                err_cnt++;
                usleep(100000);
                continue;
            }

            if (cmd == "mmpp_exit") {
                slog_e(g_ce_id) << "child " << my_pid << " exit.";
                break;
            }
            int ret_code = -1;
            if (!SystemCmd(cmd, ret_code)) {
                slog_e(g_ce_id) << "child " << my_pid << " : " << cmd << " error.";
                std::string err_string = "mmpp_err," + std::to_string(ret_code);
                WriteToPipe(node.c2p_pipe[1], err_string);
            } else {
                slog_i(g_ce_id) << "child " << my_pid << " : " << cmd << " success.";
                WriteToPipe(node.c2p_pipe[1], "mmpp_suc");
            }
        }
        close(node.p2c_pipe[0]);
        close(node.c2p_pipe[1]);
        CE_LOG_CLOSE();
        exit(0);
    } else {
        close(node.c2p_pipe[1]);
        close(node.p2c_pipe[0]);
    }
//    pidNodes_.push_back(node);
    WLock w_lock(mutex_);
    standby_pool_[node.pid] = node;
    working_process_num_++;
    return true;
}

std::string imgsrv::CommandExecutor::ReadFromPipe(int file, int timeout) {
    char buffer[kPipeBufSize] = {0};
    int size = 0, t = 0, n = 0;
    while (true) {
        size = read(file, buffer, kPipeBufSize);
        if (size == -1) {
            if (errno == EAGAIN) {
                usleep(1000);
                if (timeout == -1) {
                    continue;
                } else {
                    n += 1;
                    if (n > timeout) {
//                        slog_e(g_ce_id) << "Read Pipe timeout.";
                        std::string ret = "mmpp_exit";
                        return ret;
                    }
                }
            } else/* if (errno == EPIPE)*/ {
                if (t > 3) {
                    std::string ret = "mmpp_exit";
//                    slog_e(g_ce_id) << "Read Pipe failed.";
                    return ret;
                }
                usleep(1000);
                t++;
                continue;
            }
        } else break;
    }
    buffer[size] = '\0';
    std::string s(buffer, size);
//    slog_i(g_ce_id) << "pipe read : " << s << " size=" << size;
    return s;
}

int imgsrv::CommandExecutor::WriteToPipe(int file, const std::string& s, int timeout) {
//    slog_i(g_ce_id) << "pipe write : " << s;
    if (s.size() >= kPipeBufSize) {
//        slog_e(g_ce_id) << "ERROR : Command is too long. " << s;
        return 0;
    }
    int t = 0, size = 0, n = 0;
    while (true) {
        size = write(file, s.c_str(), s.size());
        if (size == -1) {
            if (errno == EPIPE) {
                if (t >= 3) {
//                    slog_e(g_ce_id) << "writeToPipe error : "
//                           << strerror(errno) << "(" << errno << ")";
                    return -1;
                }
                usleep(1000);
                t++;
                continue;
            }
            if (errno == EAGAIN) {
                usleep(1000);
                if (timeout == -1) {
                    continue;
                } else {
                    n += 1;
                    if (n > timeout) {
//                        slog_e(g_ce_id) << "Write Pipe timeout.";
                        return -1;
                    }
                }
            }
        } else break;

    }
    if (size != (int)s.size()) {
//        slog_e(g_ce_id) << "write pipe failed. size=" << size
//               << " errno=" << errno << " " << strerror(errno);
    }
    return size;
}

bool imgsrv::CommandExecutor::SystemCmd(const std::string& cmd, int& ret_code) {
    pid_t status;
    slog_i(g_ce_id) << "system command : " << cmd;
    /*std::ofstream write( "debug.log", std::ios::app);
    write << cmd << std::endl;
    write.close();*/

    sighandler_t old_handler;
    old_handler = signal(SIGCHLD, SIG_DFL);

//    std::string cmd_temp = cmd;no output
//    replace(cmd_temp.begin(),cmd_temp.end(),'"','_');
//    char command[512];
//    snprintf(command, 512, "ls -l /home/<USER>/wenjun/code/imgsrv/build/output/imgsrv/tmp/* 2>> debug.log 1>>debug.log");
//    system(command);
    int64_t start_time = Util::GetTimeStamp();
    status = system(cmd.c_str());
    int64_t end_time = Util::GetTimeStamp();

//    snprintf(command, 512, "ls -l /home/<USER>/wenjun/code/imgsrv/build/output/imgsrv/tmp/* 2>> debug.log 1>>debug.log");
//    system(command);

    signal(SIGCHLD, old_handler);

    if (-1 == status) {
        slog_e(g_ce_id) << "system error!";
    } else {
        if (WIFEXITED(status)) {
            if (0 == WEXITSTATUS(status)) {
                ret_code = 0;
                slog_i(g_ce_id) << "run system command successfully. takes " << end_time - start_time << " ms";
                return true;
            } else {
                ret_code = WEXITSTATUS(status);
                slog_e(g_ce_id) << "run shell script fail, script exit code: " << ret_code;
            }
        } else {
            ret_code = WEXITSTATUS(status);
            slog_e(g_ce_id) << "exit status=" << ret_code;
        }
    }

    return false;
}

bool imgsrv::CommandExecutor::DeleteChild(pid_t pid) {
    PidNode node{};
    WLock w_lock(mutex_);
    auto it = standby_pool_.find(pid);
    if (it != standby_pool_.end()) {
        node = it->second;
        close(node.c2p_pipe[0]);
        close(node.p2c_pipe[1]);
        kill(node.pid, SIGKILL);
    }
    standby_pool_.erase(pid);

    it = running_pool_.find(pid);
    if (it != running_pool_.end()) {
        node = it->second;
        close(node.c2p_pipe[0]);
        close(node.p2c_pipe[1]);
        kill(node.pid, SIGKILL);
    }
    running_pool_.erase(pid);
    working_process_num_--;
    return true;
}

int imgsrv::CommandExecutor::GetTotalProcessNum() {
    RLock r_lock(mutex_);
    return total_process_num_;
}

int imgsrv::CommandExecutor::GetWorkingProcessNum() {
    RLock r_lock(mutex_);
    return working_process_num_;
}

bool imgsrv::CommandExecutor::Stop() {

    for (const auto& i : standby_pool_) {
        WriteToPipe(i.second.p2c_pipe[1], "mmpp_exit");
        close(i.second.p2c_pipe[1]);
        close(i.second.c2p_pipe[0]);
    }
    for (const auto& i : running_pool_) {
        WriteToPipe(i.second.p2c_pipe[1], "mmpp_exit");
        close(i.second.p2c_pipe[1]);
        close(i.second.c2p_pipe[0]);
    }
    SLOG_I << "CE stopped.";
    return true;
}

bool imgsrv::CommandExecutor::RunCommand(
        const std::string& cmd,
        const std::string& task_id,
        int wait_time,
        int& ret_code) {
    ret_code = 0;
    std::string command = "export LD_LIBRARY_PATH=LD_LIBRARY_PATH:"
            + tool_dir_ + "/lib/sal:"
            + tool_dir_ + "/lib/gm/lib:"
            + tool_dir_ + "/lib/ffmpeg:"
            + tool_dir_ + "/lib/common:"
            + tool_dir_ + "/lib/opencv/lib; export PATH=$PATH:"
            + tool_dir_ + "/bin; export MAGICK_CONFIGURE_PATH="
            + tool_dir_ + "/config/gm/; echo \'"
            + cmd + "\' | timeout 30 bash -";
    while (true) {
        PidNode node;

//        boost::condition_variable_any cv;
        WLock w_lock(mutex_);
        while (standby_pool_.empty()) {
            if(!cv_.timed_wait(w_lock, boost::posix_time::milliseconds(wait_time))) {
                SLOG_E << task_id << ", RunCommand wait timeout!";
                return false;
            }
            SLOG_W << task_id << ": No idle work process available right now!";
        }
        TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
        if (task_data_ptr) {
            if (task_data_ptr->GetFetchConnectionPtr()) {
                if (task_data_ptr->GetFetchConnectionPtr()->IsTimeout()) {
                    SLOG_E << task_id << ",task timeout for RunCommand!";
                    return false;
                }
            } else {
                SLOG_W << task_id << ", RunCommand error, task is gone!";
                return false;
            }
        } else {
            SLOG_W << task_id << ", RunCommand error, task is gone!";
        }

        auto it = standby_pool_.begin();
        node = it->second;
        running_pool_[node.pid] = node;
        standby_pool_.erase(it);

        if (w_lock.owns_lock()) {
            SLOG_D << task_id << " still owns lock";
            w_lock.unlock();
            SLOG_D << task_id << " lock released";
        }

        /*WLock w_lock(mutex_);
        auto it = standby_pool_.begin();
        if (it != standby_pool_.end()) {
            node = it->second;
            running_pool_[node.pid] = node;
            standby_pool_.erase(it);
            w_lock.unlock();
        } else {
            w_lock.unlock();
            SLOG_W << task_id << ": No idle work process available right now!";
            usleep(100000);
            continue;
        }
        */

        SLOG_D << task_id << ": send cmd to child process: " << node.pid;
        int size = WriteToPipe(node.p2c_pipe[1], command, 30000);
        SLOG_I << task_id << ": send cmd to child process: " << node.pid << ",cmd1: " << command;
        if (size == -1) {
            SLOG_E << task_id << ": write pipe is broken. remove child process:" << node.pid;
            DeleteChild(node.pid);
            if (GetWorkingProcessNum() < 0) {
                return false;
            } else {
                continue;
            }
        } else {
            std::string ret = ReadFromPipe(node.c2p_pipe[0], 30000);
            w_lock.lock();
            running_pool_.erase(node.pid);
            standby_pool_[node.pid] = node;
            w_lock.unlock();
            cv_.notify_one();
            if (ret == "mmpp_suc") {
                return true;
//            } else if (ret == "mmpp_err") {
            } else if (boost::starts_with(ret, "mmpp_err")) {
                SLOG_E << task_id << ": Command error : " << ret;

                std::vector<std::string> vec;
                boost::split(vec, ret, boost::is_any_of(", "), boost::token_compress_on);
                if (vec.size() != 2) {
                    SLOG_E << "Wrong range format!";
                    return false;
                }
                ret_code = std::stoi(vec[1]);
                return false;
            } else if (ret == "mmpp_exit") {
                SLOG_E << task_id << ": read pipe is broken. remove child process:" << node.pid;
                DeleteChild(node.pid);
                return false;
            } else {
                return false;
            }
        }

    }
}
