/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : video_check_proc.cpp
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2024-01-01
 * @version  :
 * @brief    : Video check processor using Python script
 *
 * @details  :
 *================================================================*/
#include "video_check_proc.h"
#include "log_adapter.h"
#include "container.h"
#include "config.h"
#include "command_executor.h"
#include <util.h>
#include <sstream>
#include <unistd.h>

imgsrv::VideoCheckProc::VideoCheckProc() : output_path_(""), third_path_("") {

}

bool imgsrv::VideoCheckProc::Init(const std::string& output_path,
                                  const std::string& third_path) {
    output_path_ = output_path;
    third_path_ = third_path;
    return true;
}

bool imgsrv::VideoCheckProc::Process(const std::string& task_id) {
    bool rst = false;
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        if (task_data_ptr->GetProcType() != kVideoCheck) {
            return false;
        }

        // 使用VideoCheckTask来存储参数
        VideoCheckTaskPtr video_check_task_ptr =
                boost::dynamic_pointer_cast<VideoCheckTask>(task_data_ptr);
        rst = DoVideoCheck(task_id, task_data_ptr, video_check_task_ptr->GetTemplateId());
    } else {
        return false;
    }
    return rst;
}

bool imgsrv::VideoCheckProc::DoVideoCheck(const std::string& task_id,
                                          TaskDataPtr& task_data_ptr,
                                          const std::string& params) {
    std::string tmp_path = GetTmpPath(task_id);
    if (access(tmp_path.c_str(), F_OK) != 0) {
        Util::MkDir(tmp_path + "/");
    }

    std::string output = tmp_path + "/result.json";
    std::stringstream ss;
    std::string pyPath = GetPythonScriptPath();

    // 构建Python命令
    ss << "cd " << third_path_ << "/avgt && ";
    ss << "python3 " << pyPath << " '" << params << "' > " << output;

    std::string sys_cmd = ss.str();
    SLOG_I << task_data_ptr->GetTaskId() << " : " << sys_cmd;

    int ret_code = 0;
    bool rst = CE.RunCommand(sys_cmd, task_id, 30, ret_code);

    if (!rst || ret_code != 0) {
        SLOG_E << task_data_ptr->GetTaskId() << " : video check command failed, ret_code: " << ret_code;
        return false;
    }

    // 检查输出文件是否存在
    if (access(output.c_str(), F_OK) != 0) {
        SLOG_E << task_data_ptr->GetTaskId() << " : video check output file not found: " << output;
        return false;
    }

    task_data_ptr->SetOutputFile(output);
    task_data_ptr->SetTargetSuffix("json");

    SLOG_I << task_data_ptr->GetTaskId() << " : video check completed successfully";
    return true;
}

std::string imgsrv::VideoCheckProc::GetTmpPath(const std::string& task_id) {
    return output_path_ + "/" + task_id;
}

std::string imgsrv::VideoCheckProc::GetPythonScriptPath() {
    return "main.py";
}
