/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : img_wm_proc.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019/8/27
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_IMG_WM_PROC_H
#define IMGSRV_IMG_WM_PROC_H


#include <string>
#include "task_data.h"

namespace imgsrv {
class ImgWmProc : public boost::serialization::singleton<ImgWmProc>{
public:
    ImgWmProc();
    bool Init(/*const std::string& gm_command,*/
            const std::string& output_path,
            const std::string& third_path,
            int hevc_thread_num);
    bool Process(const std::string& task_id);

private:
    bool ConvertImgWmCmd(ImgWmTaskPtr& img_wm_task_ptr, std::string& command);

    bool ConvertTxtWmCmd(TxtWmTaskPtr& txt_wm_task_ptr, std::string& command);
    std::string ConvertP(int p);
    std::string GetOutputFile(const std::string& task_id, const std::string& target_suffix);
    /*void RemoveUnsafeChar(std::string& text);
    void StringReplace(std::string&s1, const std::string s2, const std::string s3);*/

private:
    std::string bin_path_;
//    std::string wm_bash_;
    std::string output_path_;
    std::string font_path_;
    int hevc_thread_num_;
};
}

#define IMG_WM_PROC imgsrv::ImgWmProc::get_mutable_instance()
#endif //IMGSRV_IMG_WM_PROC_H
