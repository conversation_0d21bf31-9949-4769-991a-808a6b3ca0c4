/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : connection.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_CONNECTION_H
#define IMGSRV_CONNECTION_H

#include <string>
#include <boost/atomic.hpp>
#include <boost/noncopyable.hpp>
#include <boost/enable_shared_from_this.hpp>
#include <boost/asio/io_service.hpp>
#include <boost/asio/ip/tcp.hpp>
#include <util.h>
#include <boost/asio/deadline_timer.hpp>
#include <fstream>
#include "http_request_parser.h"
#include "resolver.h"
#include "statistics.h"
//#include "task_data.h"

using namespace boost::asio;
using namespace boost::asio::ip;
using namespace boost::system;

namespace imgsrv {

const std::size_t kConnBufSize = 50 * 1024;
//const std::size_t kHeadBufSize = 5 * 1024;

class Connection {
public:
    Connection()
            : start_time_(0),
              end_time_(0),
              tag_("") {
//        seq_.store(0);
        std::stringstream tag;
        start_time_ = Util::GetTimeStamp();
        tag << start_time_ << this;
        tag_ = tag.str();
        buffer_.clear();
    }
    virtual void Start() = 0;


    const int64_t& GetStartTime();

    const int64_t& GetEndTime();

    void SetStartTime(int64_t start);

    void SetEndTime(int64_t end);

    const std::string& GetTag() const;

    void SetTag(const std::string& tag);

    int64_t GetElapsedTime();

    std::vector<char>& GetBuffer();

protected:
    int64_t start_time_;
    int64_t end_time_;
//    static boost::atomic<unsigned long> seq_;
    std::string tag_;
//    std::array<char, kConnBufSize> buffer_;
    std::vector<char> buffer_;
};

class ServerConnection : boost::noncopyable, public Connection,
        public boost::enable_shared_from_this<ServerConnection> {
public:
    explicit ServerConnection(io_service& ios);
    ~ServerConnection();

    void Start() override;

    tcp::socket& socket();

    const Request& GetRequest() const;

    bool StartReply(int data_size, const std::string& cont_type);

    void SendRsp(size_t size);

    void SetReplyCurLen(size_t size);

    void SendError(Reply::StatusType status);

    void SendCustomReply(const std::string& reply, Reply::StatusType st);

    void CancelTimer();

    Statistics& GetStatInfo();

private:

    void ReadRequest();

    void HandleReadRequest(const error_code& e, size_t bytes_trans);

    void HandleError(const error_code& e);

    void HandleCustomReply(const error_code& e);

    void HandleTimeOut(const error_code& e, const std::string& task_id, int time_out);

    bool ProcessAudio();
    bool ProcessImg();
    bool ProcessVideo();
    bool ProcessNew();
    bool CheckVideo();

    bool GenRspHeader(size_t data_size, size_t& header_size, const std::string& cont_type);

    void HandleSendRsp(const error_code& e);

public:
    Reply& GetReply();

private:
    tcp::socket socket_;
    deadline_timer timer_;
    bool err_send_;
    HttpRequestParser request_parser_;
    Request request_;
    Reply reply_;
    Statistics stat_info_;
//    WrMutex send_err_mutex_;
//    std::array<char, kConnBufSize> buffer_;
};

class FetchConnection : boost::noncopyable, public Connection,
        public boost::enable_shared_from_this<FetchConnection> {
public:
    enum resolve_type {
        DUMMY_RESOLVE_TYPE = 0,
        LVS = 1,
        ANTVIP = 2
    };
    FetchConnection(
            const std::string& task_id,
            const std::string& host,
            const std::string& service,
            const std::string& req_str,
            TargetType target_type,
            const std::string& out_file,
            io_service& ios);
    ~FetchConnection();

    bool Init();

    void Start();

    void Resolve();

    const std::string& GetTaskId() const;

    void SetTaskId(const std::string& task_id);

    bool IsTimeout() const;

    void SetTimeout(bool timeout);

    TargetType GetTargetType() const;

    void SetTargetType(TargetType target_type);

private:

    void HandleResolve(const error_code& e, tcp::resolver::iterator ep_it);
    void AsyncConnect(tcp::resolver::iterator ep_it);
    void HandleConnect(const error_code &e);
//    bool GenReqStr(const std::string& path, const std::string& query, const std::string& target);
//    std::string GenPassport(const std::string& file_id);
    void HandleWrite(const error_code &e);
    void HandleReadHeader(const error_code &e, size_t bytes_transferred);
    void ReadRspBody();
    void HandleReadBody(const error_code &e, size_t bytes_transferred);
    bool WriteFile(size_t bytes_transferred);
    bool PrepareTask();
    void Abort(Reply::StatusType status);
    void CollectStatInfo();

private:
    std::string task_id_;
//    std::string host_;
//    std::string service_;
    std::string out_file_;
    tcp::socket socket_;
    Resolver resolver_;
    resolve_type res_type_;
    TargetType target_type_;
    short retries_;
    boost::asio::streambuf hbuf_;
//    std::array<char, kConnBufSize> buffer_;

    bool timeout_;
    FetchRequest fetch_request_;
    Response response_;
    std::ofstream ofs_;
};
}

typedef boost::shared_ptr<imgsrv::ServerConnection> ServerConnectionPtr;
typedef boost::shared_ptr<imgsrv::FetchConnection> FetchConnectionPtr;
#endif //IMGSRV_CONNECTION_H
