/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : connection.cpp
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include <boost/asio/placeholders.hpp>
#include <boost/bind/bind.hpp>
#include <boost/asio/completion_condition.hpp>
#include <boost/asio/write.hpp>
#include <boost/algorithm/string/regex.hpp>
#include <openssl/md5.h>
#include <boost/algorithm/string.hpp>
#include "connection.h"
#include "log_adapter.h"
#include "worker.h"
#include "container.h"
#include "config.h"
#include "util.h"
#include "antvip/antvip_client.h"
using namespace antvip;

//boost::atomic<unsigned long> imgsrv::Connection::seq_{0};

const std::string& imgsrv::Connection::GetTag() const {
    return tag_;
}

const int64_t& imgsrv::Connection::GetStartTime() {
    return start_time_;
}

const int64_t& imgsrv::Connection::GetEndTime() {
    return end_time_;
}

void imgsrv::Connection::SetStartTime(int64_t start) {
    start_time_ = start;
}

void imgsrv::Connection::SetEndTime(int64_t end) {
    end_time_ = end;
}

void imgsrv::Connection::SetTag(const std::string& tag) {
    tag_ = tag;
}

int64_t imgsrv::Connection::GetElapsedTime() {
    return GetEndTime() - GetStartTime();
}

std::vector<char>& imgsrv::Connection::GetBuffer() {
    return buffer_;
}

imgsrv::ServerConnection::ServerConnection(io_service& ios)
        : socket_(ios),
          timer_(ios)/*,
          err_send_(false)*/ {
    request_parser_.Reset();
    request_.Reset();
    reply_.Reset();
}

imgsrv::ServerConnection::~ServerConnection() {
    if (!request_.GetTid().empty()) {
        std::string tmp_path = GET_CONF.out_file_path + "/" + request_.GetTid();
        if (access(tmp_path.c_str(), F_OK) == 0) {
            if (GET_CONF.rm_tmp_dir) {
                SLOG_D << "Remove tmp path: " << tmp_path << ", conn: " << GetTag();
                Util::Remove(tmp_path);
            } else {
                SLOG_D << "tmp path: " << tmp_path
                       << " is not removed for debugging purpose, conn: " << GetTag();
            }
        }
    }
    if (GetEndTime() == 0) {
        SetEndTime(Util::GetTimeStamp());
    }
    stat_info_.status_type_ = reply_.GetStatus();
    stat_info_.end_time_ = GetEndTime();
    stat_info_.total_time_ = GetEndTime() - GetStartTime();
    SLOG_I << "Server connection ended, conn: " << GetTag();
}

void imgsrv::ServerConnection::Start() {
    SetStartTime(Util::GetTimeStamp());
    stat_info_.start_time_ = GetStartTime();

    std::stringstream tag;
    tag << GetStartTime() << this;
    SetTag(tag.str());

    buffer_.resize(kConnBufSize);
    ReadRequest();
    error_code ec;
    boost::asio::ip::tcp::endpoint endpoint = socket().remote_endpoint(ec);
    if (!ec) {
        request_.SetRemote(endpoint.address().to_string());
        stat_info_.req_ip_ = request_.GetRemote();
    }
    SLOG_I << "serv_connection started, " << "conn: " << GetTag() << ", sts: "
           << GetStartTime() << ", remote: " << request_.GetRemote();
}

tcp::socket& imgsrv::ServerConnection::socket() {
    return socket_;
}

void imgsrv::ServerConnection::ReadRequest() {
    socket_.async_read_some(buffer(buffer_),
                            boost::bind(&ServerConnection::HandleReadRequest, shared_from_this(),
                                 boost::asio::placeholders::error,
                                 boost::asio::placeholders::bytes_transferred));
}

void imgsrv::ServerConnection::HandleReadRequest(const error_code& e, size_t bytes_trans) {
    if (!e) {
        size_t bytes_parsed = 0;
        HttpRequestParser::ParseResult rst = request_parser_.ParseHeader(
                request_, buffer_.data(), buffer_.data() + bytes_trans, bytes_parsed);

        stat_info_.req_type_ = request_.GetReqType();
        if (rst == HttpRequestParser::kDone) {
            if (request_.GetReqType() == kGet) {
                Reply::StatusType status = Reply::kOk;
                if (!request_.ValidateGet(status)) {
                    SLOG_E << "Invalid request," << " conn: " << GetTag() << ", request: "
                           << request_.ToString();
                    SendError(status);
                    return;
                }
                stat_info_.req_url_ = request_.GetUri();
                SLOG_I << "New get request started :" << request_.GetUri() << " conn: " << GetTag()
                       << " tid:" << request_.GetTid() << " sts: " << GetStartTime();
                std::stringstream ss;
                ss << " conn: " << GetTag() << " tid:" << request_.GetTid() << ", headers: \n";
                for (const auto& i : request_.GetHeaders()) {
                    ss << i.first << ": " << i.second.value << "\n";
                }
                SLOG_D << ss.str();
                if (!ProcessNew()) {
                    SendError(Reply::kBadRequest);
                }
            } else if (request_.GetReqType() == kPost) {
                //Not supported yet
                SLOG_E << "Request method is not supported!" << request_.GetMethod()
                       << request_.GetUri() << " conn: " << GetTag() << " tid:" << request_.GetTid();
                SendError(Reply::kBadRequest);
            } else {
                SLOG_E << "Request method is not supported!" << request_.GetMethod()
                       << request_.GetUri() << " conn: " << GetTag() << " tid:" << request_.GetTid();
                SendError(Reply::kBadRequest);
            }
        } else if (rst == HttpRequestParser::kNotYet) {
            ReadRequest();
        } else {
            SLOG_E << "Reqeust parsing error, " << "conn: " << GetTag();
            SendError(Reply::kBadRequest);
            return;
        }
    } else {
        stat_info_.SetValid(false);
        if (e == boost::asio::error::eof) {
            SLOG_I << "Connction closed by client, " << "conn: " << GetTag();
        } else if (e == boost::asio::error::connection_reset) {
            //Antvip health checking
            SLOG_I << "Service check OK!" << "conn: " << GetTag();
        } else {
            SLOG_E << "Read request error, " << "conn: " << GetTag() << e.message();
            SendError(Reply::kBadRequest);
        }
    }
}

void imgsrv::ServerConnection::SendError(const Reply::StatusType status) {
    WLock lock(reply_.rp_phase_mutex_);
    if (reply_.rp_phase == Reply::rp_start) {
        reply_.rp_phase = Reply::rp_header;
    } else {
        return;
    }
    lock.unlock();
    buffer_.resize(Reply::kStockBuffSize);
    int size = reply_.StockReply(status, buffer_.data(), buffer_.size());
    assert(size > 0);
    boost::asio::async_write(socket_, buffer(buffer_), boost::asio::transfer_exactly(size),
                             bind(&ServerConnection::HandleError, shared_from_this(), boost::asio::placeholders::error));
}

void imgsrv::ServerConnection::SendCustomReply(const std::string& reply, const Reply::StatusType status) {
    WLock lock(reply_.rp_phase_mutex_);
    if (reply_.rp_phase == Reply::rp_start) {
        reply_.rp_phase = Reply::rp_header;
    } else {
        return;
    }
    lock.unlock();

    reply_.SetStatus(status);
    strncpy(buffer_.data(), reply.c_str(), reply.size());
    boost::asio::async_write(socket_, buffer(buffer_), boost::asio::transfer_exactly(reply.size()),
                             bind(&ServerConnection::HandleCustomReply, shared_from_this(), boost::asio::placeholders::error));
}

void imgsrv::ServerConnection::HandleCustomReply(const error_code& e) {
    if (!e) {
        boost::system::error_code ignore_ec;
        socket_.shutdown(boost::asio::ip::tcp::socket::shutdown_both, ignore_ec);
        end_time_ = Util::GetTimeStamp();
        SLOG_I << "Request ended with custom reply, conn: " << tag_ << " key: " << request_.GetUri()
               << ", current length: " << reply_.GetCurLength() << ", total length: " << reply_.GetContentLength()
               << " ets: " << end_time_;
    }
}

void imgsrv::ServerConnection::CancelTimer() {
    timer_.cancel();
}

void imgsrv::ServerConnection::HandleError(const error_code& e ) {
    if (!e) {
        boost::system::error_code ignore_ec;
        socket_.shutdown(boost::asio::ip::tcp::socket::shutdown_both, ignore_ec);
        end_time_ = Util::GetTimeStamp();
        SLOG_E << "Request failed, conn: " << GetTag() << " key: " << request_.GetUri()
               << ", current length: " << reply_.GetCurLength() << ", total length: " << reply_.GetContentLength()
               << " ets: " << end_time_;
    }
}

static std::string GenPassport(const std::string& file_id) {
    int64_t ts = imgsrv::Util::GetTimeStamp() * 1000;
    std::stringstream ss;
    unsigned char md5[16];
    char signature[32 + 1] = {0};
    char * hexstr = signature;
    ss << file_id << "-" << ts << "-" << "imgsrv_secret";
    MD5((const unsigned char*)ss.str().c_str(), ss.str().size(), md5);
    for(int i = 0; i < 16; i++) {
        snprintf(hexstr, 3, "%02x", md5[i]);
        hexstr += 2;
    }
    ss.str("");
    ss << "&appId=" << "imgsrv"
       << "&timestamp=" << ts
       << "&token=" << signature;
    return ss.str();
}

static std::string GenReqStr(
        const std::string& host,
        const std::string& path,
        const std::string& query,
        const std::string& target) {

    std::stringstream ss;
    ss << "GET " << path << "?" << query << GenPassport(target);
    ss << " HTTP/1.0\r\n";
    ss << "Host: " << host << "\r\n";
    ss << "Accept: */*\r\n";
    ss << "Connection: close\r\n\r\n";

    return ss.str();
}

static std::string GenImgProcReqStr(
        const std::string& host,
        const std::string& file_id,
        const std::string& zoom,
        const std::string& tid) {

    std::string query = "fileid=" + file_id + "&zoom=" + zoom + "&uritype=id";

    std::stringstream ss;
    ss << "GET " << "/img/imgproc" << "?" << query << GenPassport(file_id);
    ss << " HTTP/1.0\r\n";
    ss << "Host: " << host << "\r\n";
    ss << "Accept: */*\r\n";
    ss << "biztype: imgsrv\r\n";
    ss << "appid: imgsrv\r\n";
    ss << "taskid: " << tid + "_1" <<"\r\n";
//    ss << "timeout: 3000\r\n";
    ss << "Connection: close\r\n\r\n";
    return ss.str();
}

bool imgsrv::ServerConnection::ProcessNew() {
    const Request& request = GetRequest();
    const Url& url = request.GetUrl();
//    const std::map<std::string, ProcType>& api_type = request.GetApiType();
    const std::map<std::string, Header>& headers = request.GetHeaders();
    std::string task_id = request.GetTid();
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        SLOG_E << "Task id: " << task_id << " is already in processing!" << ", conn: " << GetTag();
        return false;
    }
    stat_info_.task_id_ = request.GetTid();
    stat_info_.trace_id_ = request.GetTraceId();
    stat_info_.rpc_id_ = request.GetRpcId();

    auto it_param = url.GetParams().find("fileid");
    if (it_param == url.GetParams().end()) {
        SLOG_E << "fileid is empty!" << ", conn: " << GetTag() << " tid:" << request.GetTid();
        return false;
    }
//    std::string file_id = it_param->second;
    stat_info_.file_id_ = it_param->second;

    std::string biz_type = "";
    auto it_header = headers.find("biztype");
    if (it_header != headers.end()) {
        biz_type = it_header->second.value;
        stat_info_.biz_type_ = biz_type;
    }

    it_header = headers.find("appid");
    if (it_header != headers.end()) {
        stat_info_.app_id_ = it_header->second.value;
    }

    if (request.proc_type_ == kImgProc || request.proc_type_ == kImgWaterMark
    || request.proc_type_ == kTextWaterMark || request.proc_type_ == kImgAvif) {
        return ProcessImg();
    } else if (request.proc_type_ == kAudioConvert) {
        return ProcessAudio();
    } else if (request.proc_type_ == kVideoProc) {
        return ProcessVideo();
    } else if (request.proc_type_ == kVideoCheck) {
        return CheckVideo();
    } else{
        return false;
    }
    return false;
}

bool imgsrv::ServerConnection::ProcessVideo() {
    const Request& request = GetRequest();
    const Url& url = request.GetUrl();
    const std::map<std::string, Header>& headers = request.GetHeaders();

    //获取目标格式尾缀
    std::string uri_suffix = "mp4";
    auto it_param = url.GetParams().find("urisuffix");
    if (it_param != url.GetParams().end()) {
        uri_suffix = it_param->second;
    }

    //获取视频处理模版
    std::string template_id = "video_info_parse";//默认解析视频
    it_param = url.GetParams().find("templateid");
    if (it_param != url.GetParams().end()) {
        template_id = it_param->second;
    }

    //获取token
    std::string token = "";
    it_param = url.GetParams().find("token");
    if (it_param != url.GetParams().end()) {
        token = it_param->second;
    }

    //获取mass-token
    std::string mass_token = "";
    it_param = url.GetParams().find("mtoken");
    if (it_param != url.GetParams().end()) {
        mass_token = it_param->second;
    }

    int time_out = 0;
    auto it_header = headers.find("timeout");
    if (it_header != headers.end()) {
        time_out = std::stoi(it_header->second.value);
    }

    VideoProcTaskPtr video_proc_task_ptr = VideoProcTaskPtr(new VideoProcTask());
    video_proc_task_ptr->SetTaskId(request.GetTid());
    video_proc_task_ptr->SetProcType(request.GetProcType());
    video_proc_task_ptr->SetBizType(stat_info_.biz_type_);
    video_proc_task_ptr->SetAppId(stat_info_.app_id_);
    video_proc_task_ptr->SetTimeOut(time_out);
    video_proc_task_ptr->SetTargetSuffix(uri_suffix);
    video_proc_task_ptr->SetTemplateId(template_id);

    //解析视频传入url给ffprobe解析
    if(template_id == "video_info_parse"){
        std::string video_url;
        // 预计算总长度以减少内存分配
        size_t total_len = strlen("http://mass.alipay.com/afts_flow/afts/video/")
                        + stat_info_.file_id_.size()
                        + 3  // "?t="的长度
                        + token.size();
        if (!mass_token.empty()) {
            total_len += 4 + mass_token.size();  // "&mt="的长度加上mass_token的长度
        }
        // 预分配内存,通过追加构建字符串
        video_url.reserve(total_len);
        video_url += "http://mass.alipay.com/afts_flow/afts/video/";
        video_url += stat_info_.file_id_;
        video_url += "?t=";
        video_url += token;
        if (!mass_token.empty()) {
            video_url += "&mt=";
            video_url += mass_token;
        }
        video_proc_task_ptr->setUrl(video_url);
        SLOG_I << "video_url:" << video_url << ", task_id: " << request.GetTid();
    }

    std::string input_path = GET_CONF.out_file_path + "/" + request.GetTid() + "/";
    if (access(input_path.c_str(), F_OK) != 0) {
        Util::MkDir(input_path);
    }

    std::string input_file = input_path + stat_info_.file_id_;

    video_proc_task_ptr->SetInputFile(input_file);
    video_proc_task_ptr->SetInputFileDone(false);
    video_proc_task_ptr->SetServerConnectionPtr(shared_from_this());

    TaskDataPtr data_ptr = video_proc_task_ptr;
    //回源
    int64_t index = Util::GetTimeStamp() % GET_CONF.host_names.size();
    std::string host = GET_CONF.host_names[index];
    std::string service = "http";
    //[by tuwei 写死一个无意义小文件去下载ATqItdQbn3NKMAAAAAAAAAAAAADpl2AQ]
    std::string query = "fileIds=ATqItdQbn3NKMAAAAAAAAAAAAADpl2AQ";
    if (!stat_info_.biz_type_.empty()) {
        query.append("&bizType=").append(stat_info_.biz_type_);
    }
    std::string req_str = GenReqStr(host, "/file/1.0/down", query, "ATqItdQbn3NKMAAAAAAAAAAAAADpl2AQ");
    FetchConnectionPtr fetch_connection_ptr(new FetchConnection(
            request.GetTid(),
            host,
            service,
            req_str,
            kId,
            input_file,
#if BOOST_VERSION >= 107000
            (boost::asio::io_context&)(socket_).get_executor().context()
#else
            socket_.get_io_service()
#endif
    ));

    if (!fetch_connection_ptr->Init()) {
        SLOG_E << "Init fetch connection error: "
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        return false;
    }

    CONTAINER.PoolInsert(data_ptr);

    stat_info_.file_fetch_start_time_ = Util::GetTimeStamp();
    data_ptr->SetFetchConnectionPtr(fetch_connection_ptr);
    fetch_connection_ptr->Start();


//    int time_out;
    if (data_ptr->GetTimeOut() > 0) {
        time_out = data_ptr->GetTimeOut();
    } else if (GET_CONF.default_time_out > 0) {
        time_out = GET_CONF.default_time_out;
    } else {
        time_out = 0;
    }
    if (time_out > 0) {
        data_ptr->SetTimeOut(time_out);
        stat_info_.time_out_ = time_out;
        SLOG_D << "Set timer for " << time_out << " ms"
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        timer_.expires_from_now(boost::posix_time::milliseconds(time_out));
        timer_.async_wait(boost::bind(
                &imgsrv::ServerConnection::HandleTimeOut,
                shared_from_this(),
                boost::asio::placeholders::error,
                request.GetTid(),
                time_out));
    }
    return true;
}

bool imgsrv::ServerConnection::ProcessAudio() {
    const Request& request = GetRequest();
    const Url& url = request.GetUrl();
    const std::map<std::string, Header>& headers = request.GetHeaders();

    std::string sf;
    auto it_param = url.GetParams().find("sf");
    if (it_param != url.GetParams().end()) {
        sf = it_param->second;
    }

    int ssr = -1;
    it_param = url.GetParams().find("ssr");
    if (it_param != url.GetParams().end()) {
        ssr = std::stoi(it_param->second);
    }

    int sbr = -1;
    it_param = url.GetParams().find("sbr");
    if (it_param != url.GetParams().end()) {
        sbr = std::stoi(it_param->second);
    }

    std::string df;
    it_param = url.GetParams().find("df");
    if (it_param != url.GetParams().end()) {
        df = it_param->second;
    }

    int dsr = -1;
    it_param = url.GetParams().find("dsr");
    if (it_param != url.GetParams().end()) {
        dsr = std::stoi(it_param->second);
    }

    int dbr = -1;
    it_param = url.GetParams().find("dbr");
    if (it_param != url.GetParams().end()) {
        dbr = std::stoi(it_param->second);
    }

    int time_out = 0;
    auto it_header = headers.find("timeout");
    if (it_header != headers.end()) {
        time_out = std::stoi(it_header->second.value);
    }


    AudioProcTaskPtr audio_proc_task_ptr = AudioProcTaskPtr(new AudioProcTask());

    audio_proc_task_ptr->SetTaskId(request.GetTid());
    audio_proc_task_ptr->SetProcType(request.GetProcType());
    audio_proc_task_ptr->SetBizType(stat_info_.biz_type_);
    audio_proc_task_ptr->SetAppId(stat_info_.app_id_);
    audio_proc_task_ptr->SetTimeOut(time_out);

    std::string input_path = GET_CONF.out_file_path + "/" + request.GetTid() + "/";
    if (access(input_path.c_str(), F_OK) != 0) {
        Util::MkDir(input_path);
    }

    std::string input_file = input_path + stat_info_.file_id_;

    audio_proc_task_ptr->SetInputFile(input_file);
    audio_proc_task_ptr->SetInputFileDone(false);

    audio_proc_task_ptr->SetServerConnectionPtr(shared_from_this());

    audio_proc_task_ptr->SetSf(request.GetAudioType().find(sf)->second);
    if (ssr != -1) {
        audio_proc_task_ptr->SetSsr(ssr);
    }
    if (sbr != -1) {
        audio_proc_task_ptr->SetSbr(sbr);
    }
    audio_proc_task_ptr->SetDf(request.GetAudioType().find(df)->second);
    if (dsr != -1) {
        audio_proc_task_ptr->SetDsr(dsr);
    }
    if (dbr != -1) {
        audio_proc_task_ptr->SetDbr(dbr);
    }

    TaskDataPtr data_ptr = audio_proc_task_ptr;

    //回源
    int64_t index = Util::GetTimeStamp() % GET_CONF.host_names.size();
    std::string host = GET_CONF.host_names[index];
    std::string service = "http";
    std::string query = "fileIds=" + stat_info_.file_id_;
    if (!stat_info_.biz_type_.empty()) {
        query.append("&bizType=").append(stat_info_.biz_type_);
    }
    std::string req_str = GenReqStr(host, "/file/1.0/down", query, stat_info_.file_id_);
    FetchConnectionPtr fetch_connection_ptr(new FetchConnection(
            request.GetTid(),
            host,
            service,
            req_str,
            kId,
            input_file,
#if BOOST_VERSION >= 107000
            (boost::asio::io_context&)(socket_).get_executor().context()
#else
            socket_.get_io_service()
#endif
    ));

    if (!fetch_connection_ptr->Init()) {
        SLOG_E << "Init fetch connection error: "
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        return false;
    }

    CONTAINER.PoolInsert(data_ptr);

    stat_info_.file_fetch_start_time_ = Util::GetTimeStamp();
    data_ptr->SetFetchConnectionPtr(fetch_connection_ptr);
    fetch_connection_ptr->Start();


//    int time_out;
    if (data_ptr->GetTimeOut() > 0) {
        time_out = data_ptr->GetTimeOut();
    } else if (GET_CONF.default_time_out > 0) {
        time_out = GET_CONF.default_time_out;
    } else {
        time_out = 0;
    }
    if (time_out > 0) {
        data_ptr->SetTimeOut(time_out);
        stat_info_.time_out_ = time_out;
        SLOG_D << "Set timer for " << time_out << " ms"
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        timer_.expires_from_now(boost::posix_time::milliseconds(time_out));
        timer_.async_wait(boost::bind(
                &imgsrv::ServerConnection::HandleTimeOut,
                shared_from_this(),
                boost::asio::placeholders::error,
                request.GetTid(),
                time_out));
    }
    return true;
}

bool imgsrv::ServerConnection::ProcessImg() {
    const Request& request = GetRequest();
    const Url& url = request.GetUrl();
    const std::map<std::string, Header>& headers = request.GetHeaders();

    std::string file_id = stat_info_.file_id_;

    std::string uri_suffix = "src";
    auto it_param = url.GetParams().find("urisuffix");
    if (it_param != url.GetParams().end()) {
        uri_suffix = it_param->second;
    }

    it_param = url.GetParams().find("uritype");
    if (it_param == url.GetParams().end()) {
        SLOG_E << "uritype is empty!" << ", conn: " << GetTag() << " tid:" << request_.GetTid();
        return false;
    }
    std::string uri_type = it_param->second;
    TargetType target_type;
    if (uri_type == "id") {
        target_type = kId;
    } else if (uri_type == "url") {
        target_type = kUrl;
    } else {
        SLOG_E << "uritype is invalid!" << ", conn: " << GetTag() << " tid:" << request_.GetTid();
        return false;
    }
    stat_info_.target_type_ = target_type;

    std::string biz_type = stat_info_.biz_type_;

    std::string host;
    std::string service;
    std::string req_str;

    if (target_type == kUrl) {
        Url target_url;
        target_url.Parse(file_id);
        if (target_url.GetHost().empty() || target_url.GetPath().empty()/* || target_url.GetQuery().empty()*/) {
            SLOG_E << "Target url format error: " << file_id
                   << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
            return false;
        } else {
            host = target_url.GetHost();
            service = target_url.GetPort().empty() ? "http" : target_url.GetPort();
            req_str = GenReqStr(host, target_url.GetPath(), target_url.GetQuery(), file_id);
        }
    } else {
        int64_t index = Util::GetTimeStamp() % GET_CONF.host_names.size();
        host = GET_CONF.host_names[index];
        service = "http";
        std::string query = "fileIds=" + file_id;
        if (!biz_type.empty()) {
            query.append("&bizType=").append(biz_type);
        }
        req_str = GenReqStr(host, "/file/1.0/down", query, file_id);
    }

    std::string appid = stat_info_.app_id_;

    TaskDataPtr data_ptr;

    std::string input_path = GET_CONF.out_file_path + "/" + request.GetTid() + "/";
    if (access(input_path.c_str(), F_OK) != 0) {
        Util::MkDir(input_path);
    }

    std::string input_file = input_path;
    if (target_type == kUrl) {
        unsigned char md5[16];
        char signature[32 + 1] = {0};
        char * hexstr = signature;
        MD5((const unsigned char*)file_id.c_str(), file_id.size(), md5);
        for(int i = 0; i < 16; i++) {
            snprintf(hexstr, 3, "%02x", md5[i]);
            hexstr += 2;
        }
        input_file += signature;
    } else {
        input_file += file_id;
    }
    //std::string input_file = input_path + file_id;

    FetchConnectionPtr wm_fetch_connection_ptr;

    int time_out = 0;
    try {
        auto it_header = headers.find("timeout");
        if (it_header != headers.end()) {
            time_out = std::stoi(it_header->second.value);
//            stat_info_.time_out_ = time_out;
        }

        CallType call_type = kBin;
        it_param = url.GetParams().find("calltype");
        if (it_param != url.GetParams().end()) {
            if (std::stoi(it_param->second) == kApi) {
                call_type = kApi;
            }
        }
        stat_info_.call_type_ = call_type;
        stat_info_.proc_type_ = request.GetProcType();
        switch (request.GetProcType()) {
            case kImgProc: {
                ImgProcTaskPtr img_proc_task_ptr = ImgProcTaskPtr(new ImgProcTask());
                img_proc_task_ptr->SetTaskId(request.GetTid());
                img_proc_task_ptr->SetProcType(request.GetProcType());
                img_proc_task_ptr->SetCallType(call_type);
                img_proc_task_ptr->SetBizType(biz_type);
                img_proc_task_ptr->SetAppId(appid);

                img_proc_task_ptr->SetTargetType(target_type);

                img_proc_task_ptr->SetTarget(file_id);
                img_proc_task_ptr->SetTargetSuffix(uri_suffix);
                img_proc_task_ptr->SetInputFile(input_file);
                img_proc_task_ptr->SetInputFileDone(false);
                img_proc_task_ptr->SetTimeOut(time_out);

                img_proc_task_ptr->SetServerConnectionPtr(shared_from_this());

                /*it_header = headers.find("ciphertype");
                if (it_header != headers.end()) {
                    img_proc_task_ptr->SetCipherType(it_header->second.value);

                    it_header = headers.find("cipherkey");
                    if (it_header != headers.end()) {
                        img_proc_task_ptr->SetCipherKey(it_header->second.value);
                    } else {
                        return false;
                    }
                }*/

                it_header = headers.find("ciphertype");
                if (it_header != headers.end()) {
                    int cipher_type = std::stoi(it_header->second.value);
                    if(cipher_type != 0 && cipher_type != 1) {
                        SLOG_E << "ciphertype error: " << it_header->second.value
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    }
                    img_proc_task_ptr->SetCipherType((CipherType)cipher_type);

                    it_header = headers.find("cipherkey");
                    if (it_header == headers.end()) {
                        SLOG_E << "cipherkey empty: "
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        img_proc_task_ptr->SetCipherKey(it_header->second.value);
                    }

                    it_header = headers.find("docrypt");
                    if (it_header == headers.end()) {
                        SLOG_E << "docrypt empty: "
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        int docrypt = std::stoi(it_header->second.value);
                        if (docrypt != 0 && docrypt != 1 && docrypt != 2) {
                            SLOG_E << "docrypt error: " << it_header->second.value
                                   << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                            return false;
                        } else {
                            img_proc_task_ptr->SetDoCrypt((DoCrypt)docrypt);
                        }
                    }
                }

                it_header = headers.find("md5");
                if (it_header != headers.end()) {
                    img_proc_task_ptr->SetTargetMd5(it_header->second.value);
                }

                it_param = url.GetParams().find("zoom");
                if (it_param == url.GetParams().end()) {
                    SLOG_E << "zoom is empty!" << ", conn: " << GetTag()
                           << ", task_id: " << request.GetTid();
                    return false;
                } else {
                    img_proc_task_ptr->SetParam1(it_param->second);
                }

                it_param = url.GetParams().find("zoom2");
                if (it_param != url.GetParams().end()) {
                    img_proc_task_ptr->SetParam2(it_param->second);
                }

                data_ptr = img_proc_task_ptr;

                break;
            }
            case kImgAvif: {
                ImgProcTaskPtr img_proc_task_ptr = ImgProcTaskPtr(new ImgProcTask());
                img_proc_task_ptr->SetTaskId(request.GetTid());
                img_proc_task_ptr->SetProcType(request.GetProcType());
                img_proc_task_ptr->SetCallType(call_type);
                img_proc_task_ptr->SetBizType(biz_type);
                img_proc_task_ptr->SetAppId(appid);

                img_proc_task_ptr->SetTargetType(target_type);

                img_proc_task_ptr->SetTarget(file_id);
                img_proc_task_ptr->SetTargetSuffix(uri_suffix);
                img_proc_task_ptr->SetInputFile(input_file);
                img_proc_task_ptr->SetInputFileDone(false);
                img_proc_task_ptr->SetTimeOut(time_out);

                img_proc_task_ptr->SetServerConnectionPtr(shared_from_this());

                /*it_header = headers.find("ciphertype");
                if (it_header != headers.end()) {
                    img_proc_task_ptr->SetCipherType(it_header->second.value);

                    it_header = headers.find("cipherkey");
                    if (it_header != headers.end()) {
                        img_proc_task_ptr->SetCipherKey(it_header->second.value);
                    } else {
                        return false;
                    }
                }*/

                it_header = headers.find("ciphertype");
                if (it_header != headers.end()) {
                    int cipher_type = std::stoi(it_header->second.value);
                    if(cipher_type != 0 && cipher_type != 1) {
                        SLOG_E << "ciphertype error: " << it_header->second.value
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    }
                    img_proc_task_ptr->SetCipherType((CipherType)cipher_type);

                    it_header = headers.find("cipherkey");
                    if (it_header == headers.end()) {
                        SLOG_E << "cipherkey empty: "
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        img_proc_task_ptr->SetCipherKey(it_header->second.value);
                    }

                    it_header = headers.find("docrypt");
                    if (it_header == headers.end()) {
                        SLOG_E << "docrypt empty: "
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        int docrypt = std::stoi(it_header->second.value);
                        if (docrypt != 0 && docrypt != 1 && docrypt != 2) {
                            SLOG_E << "docrypt error: " << it_header->second.value
                                   << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                            return false;
                        } else {
                            img_proc_task_ptr->SetDoCrypt((DoCrypt)docrypt);
                        }
                    }
                }

                it_header = headers.find("md5");
                if (it_header != headers.end()) {
                    img_proc_task_ptr->SetTargetMd5(it_header->second.value);
                }

                it_param = url.GetParams().find("zoom");
                if (it_param == url.GetParams().end()) {
                    SLOG_E << "zoom is empty!" << ", conn: " << GetTag()
                           << ", task_id: " << request.GetTid();
                    return false;
                } else {
                    img_proc_task_ptr->SetParam1(it_param->second);
                }

                it_param = url.GetParams().find("zoom2");
                if (it_param != url.GetParams().end()) {
                    img_proc_task_ptr->SetParam2(it_param->second);
                }

                it_param = url.GetParams().find("algParam");
                if (it_param != url.GetParams().end()) {
                    img_proc_task_ptr->setAlgParam(it_param->second);
                }

                data_ptr = img_proc_task_ptr;

                break;
            }
            case kImgWaterMark: {
                ImgWmTaskPtr img_wm_task_ptr = ImgWmTaskPtr(new ImgWmTask());
                img_wm_task_ptr->SetTaskId(request.GetTid());
                img_wm_task_ptr->SetProcType(request.GetProcType());
                img_wm_task_ptr->SetCallType(call_type);
                img_wm_task_ptr->SetBizType(biz_type);
                img_wm_task_ptr->SetAppId(appid);

                img_wm_task_ptr->SetTargetType(target_type);

                img_wm_task_ptr->SetTarget(file_id);
                img_wm_task_ptr->SetTargetSuffix(uri_suffix);
                img_wm_task_ptr->SetInputFile(input_file);
                img_wm_task_ptr->SetInputFileDone(false);
                img_wm_task_ptr->SetTimeOut(time_out);

                img_wm_task_ptr->SetServerConnectionPtr(shared_from_this());

                it_header = headers.find("ciphertype");
                if (it_header != headers.end()) {
                    int cipher_type = std::stoi(it_header->second.value);
                    if(cipher_type != 0 && cipher_type != 1) {
                        SLOG_E << "ciphertype error: " << it_header->second.value
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    }
                    img_wm_task_ptr->SetCipherType((CipherType)cipher_type);

                    it_header = headers.find("cipherkey");
                    if (it_header == headers.end()) {
                        SLOG_E << "cipherkey empty: " << ", conn: " << GetTag()
                               << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        img_wm_task_ptr->SetCipherKey(it_header->second.value);
                    }

                    it_header = headers.find("docrypt");
                    if (it_header == headers.end()) {
                        SLOG_E << "docrypt empty: " << ", conn: " << GetTag()
                               << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        int docrypt = std::stoi(it_header->second.value);
                        if (docrypt != 0 && docrypt != 1 && docrypt != 2) {
                            SLOG_E << "docrypt error: " << it_header->second.value
                                   << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                            return false;
                        } else {
                            img_wm_task_ptr->SetDoCrypt((DoCrypt)docrypt);
                        }
                    }
                }

                it_param = url.GetParams().find("zoom");
                if (it_param != url.GetParams().end()) {
                    if (0 != strcasecmp(it_param->second.c_str(), "original")) {
                        img_wm_task_ptr->SetParam1(it_param->second);

                        if (target_type == kUrl) {
                            SLOG_E << "target type cannot be url when zoom param is set!" << ", conn: " << GetTag()
                                   << ", task_id: " << request.GetTid();
                            return false;
                        }
                        host = "127.0.0.1";
                        service = std::to_string(GET_CONF.server_port);
                        req_str = GenImgProcReqStr(host, file_id, it_param->second, request.GetTid());

                        input_file = input_file + "_" + it_param->second;
                        img_wm_task_ptr->SetInputFile(input_file);
                    }
                }

                it_param = url.GetParams().find("wmid");
                if (it_param == url.GetParams().end()) {
                    SLOG_E << "wmid is empty!" << ", conn: " << GetTag()
                           << ", task_id: " << request.GetTid();
                    return false;
                } else {
                    img_wm_task_ptr->SetWmid(it_param->second);
                    stat_info_.logo_file_id_ = it_param->second;
                }

//                img_wm_task_ptr->SetWmFile(input_path + img_wm_task_ptr->GetWmid());
                img_wm_task_ptr->SetWmFile(input_path + "wm_" + img_wm_task_ptr->GetWmid());
                img_wm_task_ptr->SetWmFileDone(false);

                it_param = url.GetParams().find("voffset");
                if (it_param != url.GetParams().end()) {
                    img_wm_task_ptr->SetVoffset(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("t");
                if (it_param != url.GetParams().end()) {
                    img_wm_task_ptr->SetT(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("p");
                if (it_param != url.GetParams().end()) {
                    img_wm_task_ptr->SetP(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("x");
                if (it_param != url.GetParams().end()) {
                    img_wm_task_ptr->SetX(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("y");
                if (it_param != url.GetParams().end()) {
                    img_wm_task_ptr->SetY(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("wp");
                if (it_param != url.GetParams().end()) {
                    img_wm_task_ptr->SetWp(std::stoi(it_param->second));
                }

                std::string wm_host;
                std::string wm_service;
                std::string wm_req_str;
                if (target_type == kUrl) {
                    Url wm_url;
                    wm_url.Parse(img_wm_task_ptr->GetWmid());
                    if (wm_url.GetHost().empty() || wm_url.GetPath().empty()/* || wm_url.GetQuery().empty()*/) {
                        SLOG_E << "wmid url format error: " << img_wm_task_ptr->GetWmid()
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        wm_host = wm_url.GetHost();
                        wm_service = wm_url.GetPort().empty() ? "http" : wm_url.GetPort();
                        wm_req_str = GenReqStr(
                                wm_host, wm_url.GetPath(), wm_url.GetQuery(), img_wm_task_ptr->GetWmid());
                    }
                } else {
                    int64_t index = Util::GetTimeStamp() % GET_CONF.host_names.size();
                    wm_host = GET_CONF.host_names[index];
                    wm_service = "http";
                    std::string query = "fileIds=" + img_wm_task_ptr->GetWmid();
                    if (!biz_type.empty()) {
                        query.append("&bizType=").append(biz_type);
                    }
                    wm_req_str = GenReqStr(
//                            host,
                            wm_host,
                            "/file/1.0/down", query,
                            img_wm_task_ptr->GetWmid());
                }
    //            FetchConnectionPtr wm_fetch_connection_ptr(new FetchConnection(
                wm_fetch_connection_ptr.reset(new FetchConnection(
                        request.GetTid(),
                        wm_host,
                        wm_service,
                        wm_req_str,
                        target_type,
                        img_wm_task_ptr->GetWmFile(),
    #if BOOST_VERSION >= 107000
                        (boost::asio::io_context&)(socket_).get_executor().context()
    #else
                        socket_.get_io_service()
    #endif
                ));
                if (!wm_fetch_connection_ptr->Init()) {
                    SLOG_E << "Init fetch connection error: "
                           << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                    return false;
                }
    //            wm_fetch_connection_ptr->Start();
                img_wm_task_ptr->SetWmFetchConnectionPtr(wm_fetch_connection_ptr);
                data_ptr = img_wm_task_ptr;
                break;
            }
            case kTextWaterMark: {
                TxtWmTaskPtr txt_wm_task_ptr = TxtWmTaskPtr(new TxtWmTask());
                txt_wm_task_ptr->SetTaskId(request.GetTid());
                txt_wm_task_ptr->SetProcType(request.GetProcType());
                txt_wm_task_ptr->SetCallType(call_type);
                txt_wm_task_ptr->SetBizType(biz_type);
                txt_wm_task_ptr->SetAppId(appid);

                txt_wm_task_ptr->SetTargetType(target_type);

                txt_wm_task_ptr->SetTarget(file_id);
                txt_wm_task_ptr->SetTargetSuffix(uri_suffix);
                txt_wm_task_ptr->SetInputFile(input_file);
                txt_wm_task_ptr->SetInputFileDone(false);
                txt_wm_task_ptr->SetTimeOut(time_out);

                txt_wm_task_ptr->SetServerConnectionPtr(shared_from_this());
                it_header = headers.find("ciphertype");
                if (it_header != headers.end()) {
                    int cipher_type = std::stoi(it_header->second.value);
                    if(cipher_type != 0 && cipher_type != 1) {
                        SLOG_E << "ciphertype error: " << it_header->second.value
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    }
                    txt_wm_task_ptr->SetCipherType((CipherType)cipher_type);

                    it_header = headers.find("cipherkey");
                    if (it_header == headers.end()) {
                        SLOG_E << "cipherkey empty: " << ", conn: " << GetTag()
                               << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        txt_wm_task_ptr->SetCipherKey(it_header->second.value);
                    }

                    it_header = headers.find("docrypt");
                    if (it_header == headers.end()) {
                        SLOG_E << "docrypt empty: " << ", conn: " << GetTag()
                               << ", task_id: " << request.GetTid();
                        return false;
                    } else {
                        int docrypt = std::stoi(it_header->second.value);
                        if (docrypt != 0 && docrypt != 1 && docrypt != 2) {
                            SLOG_E << "docrypt error: " << it_header->second.value
                                   << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                            return false;
                        } else {
                            txt_wm_task_ptr->SetDoCrypt((DoCrypt)docrypt);
                        }
                    }
                }

                it_param = url.GetParams().find("zoom");
                if (it_param != url.GetParams().end()) {
                    if (0 != strcasecmp(it_param->second.c_str(), "original")) {
//                    txt_wm_task_ptr->SetParam1(it_param->second);

                        if (target_type == kUrl) {
                            SLOG_E << "target type cannot be url when zoom param is set!" << ", conn: " << GetTag()
                                   << ", task_id: " << request.GetTid();
                            return false;
                        }
                        host = "127.0.0.1";
                        service = std::to_string(GET_CONF.server_port);
                        req_str = GenImgProcReqStr(host, file_id, it_param->second, request.GetTid());
                        input_file = input_file + "_" + it_param->second;
                        txt_wm_task_ptr->SetInputFile(input_file);
                    }
                }

                it_header = headers.find("text");
                if (it_header != headers.end()) {
                    std::string text;
                    Url::UrlDecode(it_header->second.value, text);
                    txt_wm_task_ptr->SetText(text);
                }
                it_header = headers.find("fonttype");
                if (it_header != headers.end()) {
                    txt_wm_task_ptr->SetFontType((
                            imgsrv::TxtWmTask::FontType)std::stoi(it_header->second.value));
                }

                it_param = url.GetParams().find("voffset");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetVoffset(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("t");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetT(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("p");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetP(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("x");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetX(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("y");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetY(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("color");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetColor(it_param->second);
                }
                it_param = url.GetParams().find("size");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetSize(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("s");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetS(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("rotate");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetRotate(std::stoi(it_param->second));
                }
                it_param = url.GetParams().find("fill");
                if (it_param != url.GetParams().end()) {
                    txt_wm_task_ptr->SetFill(std::stoi(it_param->second) != 0);
                }

                data_ptr = txt_wm_task_ptr;
                break;
            }
            case kVideoCheck: {
                VideoCheckTaskPtr video_check_task_ptr = VideoCheckTaskPtr(new VideoCheckTask());
                video_check_task_ptr->SetTaskId(request.GetTid());
                video_check_task_ptr->SetProcType(request.GetProcType());
                video_check_task_ptr->SetCallType(call_type);
                video_check_task_ptr->SetBizType(biz_type);
                video_check_task_ptr->SetAppId(appid);

                video_check_task_ptr->SetTargetType(target_type);
                video_check_task_ptr->SetTarget(file_id);
                video_check_task_ptr->SetTargetSuffix(uri_suffix);
                video_check_task_ptr->SetInputFile(input_file);
                video_check_task_ptr->SetInputFileDone(false);
                video_check_task_ptr->SetTimeOut(time_out);
                video_check_task_ptr->SetServerConnectionPtr(shared_from_this());

                // 获取检测类型参数
                it_param = url.GetParams().find("templateid");
                if (it_param != url.GetParams().end()) {
                    video_check_task_ptr->SetTemplateId(it_param->second);
                } else {
                    // 默认使用stream_checker
                    video_check_task_ptr->SetTemplateId("stream_checker");
                }

                // 获取源文件参数
                it_param = url.GetParams().find("source");
                if (it_param != url.GetParams().end()) {
                    std::string source_file = input_path + "source_" + file_id;
                    video_check_task_ptr->SetSourceFile(source_file);
                    video_check_task_ptr->SetSourceFileDone(false);

                    // 创建源文件下载连接
                    std::string source_host;
                    std::string source_service;
                    std::string source_req_str;

                    if (target_type == kUrl) {
                        Url source_url;
                        source_url.Parse(it_param->second);
                        if (source_url.GetHost().empty() || source_url.GetPath().empty()) {
                            SLOG_E << "source_video url format error: " << it_param->second
                                   << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                            return false;
                        } else {
                            source_host = source_url.GetHost();
                            source_service = source_url.GetPort().empty() ? "http" : source_url.GetPort();
                            source_req_str = GenReqStr(
                                    source_host, source_url.GetPath(), source_url.GetQuery(), it_param->second);
                        }
                    } else {
                        int64_t index = Util::GetTimeStamp() % GET_CONF.host_names.size();
                        source_host = GET_CONF.host_names[index];
                        source_service = "http";
                        std::string query = "fileIds=" + it_param->second;
                        if (!biz_type.empty()) {
                            query.append("&bizType=").append(biz_type);
                        }
                        source_req_str = GenReqStr(source_host, "/file/1.0/down", query, it_param->second);
                    }

                    FetchConnectionPtr source_fetch_connection_ptr(new FetchConnection(
                            request.GetTid(),
                            source_host,
                            source_service,
                            source_req_str,
                            target_type,
                            source_file,
#if BOOST_VERSION >= 107000
                            (boost::asio::io_context&)(socket_).get_executor().context()
#else
                            socket_.get_io_service()
#endif
                    ));

                    if (!source_fetch_connection_ptr->Init()) {
                        SLOG_E << "Init source fetch connection error: "
                               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                        return false;
                    }

                    video_check_task_ptr->SetSourceFileFetchPtr(source_fetch_connection_ptr);
                } else {
                    SLOG_E << "source_video parameter is required for video check"
                           << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
                    return false;
                }

                data_ptr = video_check_task_ptr;
                break;
            }
            default:
                return false;
        }
    } catch (const std::invalid_argument& e) {
        SLOG_E << "Invalid argument: " << e.what()
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        return false;
    } catch (const std::out_of_range& e) {
        SLOG_E << "Argument out of range: " << e.what()
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        return false;
    } catch (const std::exception& e) {
        SLOG_E << "Undefined error: " << e.what()
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        return false;
    }

    //回源
    FetchConnectionPtr fetch_connection_ptr(new FetchConnection(
            request.GetTid(),
            host,
            service,
            req_str,
            target_type,
            input_file,
#if BOOST_VERSION >= 107000
            (boost::asio::io_context&)(socket_).get_executor().context()
#else
            socket_.get_io_service()
#endif
    ));

    if (!fetch_connection_ptr->Init()) {
        SLOG_E << "Init fetch connection error: "
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        return false;
    }

    CONTAINER.PoolInsert(data_ptr);

    if (request.GetProcType() == kImgWaterMark) {
//        stat_info_.logo_fetch_start_time_ = Util::GetTimeStamp();
        wm_fetch_connection_ptr->Start();
    } else if (request.GetProcType() == kVideoCheck) {
        VideoCheckTaskPtr video_check_task_ptr =
                boost::dynamic_pointer_cast<VideoCheckTask>(data_ptr);
        if (video_check_task_ptr->GetSourceFileFetchPtr()) {
            video_check_task_ptr->GetSourceFileFetchPtr()->Start();
        }
    }
    stat_info_.file_fetch_start_time_ = Util::GetTimeStamp();
    data_ptr->SetFetchConnectionPtr(fetch_connection_ptr);
    fetch_connection_ptr->Start();


//    int time_out;
    if (data_ptr->GetTimeOut() > 0) {
        time_out = data_ptr->GetTimeOut();
    } else if (GET_CONF.default_time_out > 0) {
        time_out = GET_CONF.default_time_out;
    } else {
        time_out = 0;
    }
    if (time_out > 0) {
        data_ptr->SetTimeOut(time_out);
        stat_info_.time_out_ = time_out;
        SLOG_D << "Set timer for " << time_out << " ms"
               << ", conn: " << GetTag() << ", task_id: " << request.GetTid();
        timer_.expires_from_now(boost::posix_time::milliseconds(time_out));
        timer_.async_wait(boost::bind(
                &imgsrv::ServerConnection::HandleTimeOut,
                shared_from_this(),
                boost::asio::placeholders::error,
                request.GetTid(),
                time_out));
    }

    return true;
}

const imgsrv::Request& imgsrv::ServerConnection::GetRequest() const {
    return request_;
}

void imgsrv::ServerConnection::HandleTimeOut(const error_code& e, const std::string& task_id, int time_out) {
    if (!e) {
        SLOG_I << "Task: " << task_id << " times out after " << time_out << " ms.";
        stat_info_.is_time_out_ = true;
        TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
        if (task_data_ptr) {
            if (task_data_ptr->GetFetchConnectionPtr()) {
                task_data_ptr->GetFetchConnectionPtr()->SetTimeout(true);
            }
            if (task_data_ptr->GetProcType() == kImgWaterMark) {
                ImgWmTaskPtr img_wm_task_ptr =
                        boost::dynamic_pointer_cast<ImgWmTask>(task_data_ptr);
                if (img_wm_task_ptr->GetWmFetchConnectionPtr()) {
                    img_wm_task_ptr->GetWmFetchConnectionPtr()->SetTimeout(true);
                }
            } else if (task_data_ptr->GetProcType() == kVideoCheck) {
                VideoCheckTaskPtr video_check_task_ptr =
                        boost::dynamic_pointer_cast<VideoCheckTask>(task_data_ptr);
                if (video_check_task_ptr->GetSourceFileFetchPtr()) {
                    video_check_task_ptr->GetSourceFileFetchPtr()->SetTimeout(true);
                }
            }
        }
        SendError(Reply::kProcessTimeout);
        CONTAINER.CancelTask(task_id);
    }
}

bool imgsrv::ServerConnection::StartReply(int data_size, const std::string& cont_type) {
    size_t header_size = 0;
    bool rst = GenRspHeader(data_size, header_size, cont_type);
    if (rst) {
        SendRsp(header_size);
        return true;
    }
    CancelTimer();
    CONTAINER.CancelTask(request_.GetTid());
    return false;
}

bool imgsrv::ServerConnection::GenRspHeader(size_t data_size, size_t& header_size, const std::string& cont_type) {
    reply_.SetStatus(Reply::kOk);
    reply_.SetCurLength(0);
    reply_.SetContentLength(data_size);

    reply_.GetHeaders().emplace_back(Header());
    reply_.GetHeaders().back().name = "Server";
    reply_.GetHeaders().back().value = "imgsrv";

    reply_.GetHeaders().emplace_back(Header());
    reply_.GetHeaders().back().name = "Content-Type";
    std::string mime = cont_type;
    if (mime.empty()) {
        auto it_param = request_.GetUrl().GetParams().find("urisuffix");
        if (it_param != request_.GetUrl().GetParams().end()) {
            mime = it_param->second;
        }
    }
    reply_.GetHeaders().back().value = Mime::GetMimeFromSuffix(mime);

    reply_.GetHeaders().emplace_back(Header());
    reply_.GetHeaders().back().name = "Content-Length";
    reply_.GetHeaders().back().value = std::to_string(data_size);

    reply_.GetHeaders().emplace_back(Header());
    reply_.GetHeaders().back().name = "Connection";
    reply_.GetHeaders().back().value = "close";

    header_size = reply_.GenHeaderToBuffer(buffer_.data(), buffer_.size());
    return true;
}

void imgsrv::ServerConnection::SendRsp(size_t size) {
    boost::asio::async_write(socket_, buffer(buffer_), boost::asio::transfer_exactly(size),
                             bind(&ServerConnection::HandleSendRsp, shared_from_this(),
                                  boost::asio::placeholders::error));
}

void imgsrv::ServerConnection::HandleSendRsp(const error_code& e) {
    if (!e) {
        if (reply_.GetCurLength() >= reply_.GetContentLength()) {
            WLock lock(reply_.rp_phase_mutex_);
            reply_.rp_phase = Reply::rp_done;
            lock.unlock();
            boost::system::error_code ignore_ec;
            socket_.shutdown(boost::asio::ip::tcp::socket::shutdown_both, ignore_ec);
            //trans done
            SetEndTime(Util::GetTimeStamp());
            SLOG_I << "Request ended successfully, conn: " << GetTag()
                   << ", task_id: " << request_.GetTid() << ", content-len: "
                   << reply_.GetContentLength() << " ets: " << GetEndTime()
                   << ", elp(ms):" << GetEndTime() - GetStartTime();

            /*1.1 keep alive
             * req_reset();
            read_req();*/
        } else {
            io_service& ios = WORKER.GetIoService();
            ios.post(bind(&imgsrv::Worker::SendReplyBody, &WORKER, shared_from_this(), request_.GetTid()));
        }
    } else {
        SetEndTime(Util::GetTimeStamp());
        CancelTimer();
        CONTAINER.CancelTask(request_.GetTid());
        SLOG_E << "Send reply to client error, conn: " << GetTag()
               << ", task_id: " << request_.GetTid() << " ets: " << GetEndTime()
               << ", elp(ms):" << GetEndTime() - GetStartTime();
    }
}

void imgsrv::ServerConnection::SetReplyCurLen(size_t size) {
//    reply_.SetContentLength(size);
    reply_.SetCurLength(size);
}

imgsrv::Statistics& imgsrv::ServerConnection::GetStatInfo() {
    return stat_info_;
}

imgsrv::Reply& imgsrv::ServerConnection::GetReply() {
    return reply_;
}

imgsrv::FetchConnection::FetchConnection(
        const std::string& task_id,
        const std::string& host,
        const std::string& service,
        const std::string& req_str,
        TargetType target_type,
        const std::string& out_file,
        io_service& ios)
        : task_id_(task_id),
//          host_(host),
//          service_(service),
          out_file_(out_file),
          socket_(ios),
          resolver_(ios,GET_CONF.dns_refresh_interval),
          res_type_(DUMMY_RESOLVE_TYPE),
          target_type_(target_type),
          retries_(2),
          hbuf_(kConnBufSize),
          timeout_(false) {
    fetch_request_.SetHost(host);
    fetch_request_.SetService(service);
    fetch_request_.SetReqStr(req_str);
    if (CONFIG.state_.antvip_enabled) {
        res_type_ = ANTVIP;
    } else {
        res_type_ = LVS;
    }
}

bool imgsrv::FetchConnection::IsTimeout() const {
    return timeout_;
}

void imgsrv::FetchConnection::SetTimeout(bool timeout) {
    timeout_ = timeout;
}

bool imgsrv::FetchConnection::Init() {
    ofs_.open(out_file_, std::ios::out | std::ios::binary | std::ios::app);
    if(!ofs_.is_open()) {
        SLOG_E << "file: " << out_file_ << ", open error, fetch connection start failed.";
        return false;
    }
    return true;
}

void imgsrv::FetchConnection::Start() {
    SetStartTime(Util::GetTimeStamp());
    std::stringstream tag;
    tag << GetStartTime() << this;
    SetTag(tag.str());

    buffer_.resize(kConnBufSize);
    SLOG_I << "Fetch connection started, conn: " << GetTag() << ", task_id: "
           << GetTaskId() << ", sts: " << GetStartTime();
    Resolve();
}

const std::string& imgsrv::FetchConnection::GetTaskId() const {
    return task_id_;
}

void imgsrv::FetchConnection::SetTaskId(const std::string& task_id) {
    task_id_ = task_id;
}

void imgsrv::FetchConnection::Resolve() {
    if (res_type_ == ANTVIP && fetch_request_.GetHost() != "127.0.0.1" && GetTargetType() == kId) {
        SLOG_D << "Start resolving from antvip, conn: " << GetTag() << ", task_id: "
               << GetTaskId();
        retries_++;
        RealNode real_node;
        RET_CODE result = AntvipClient::get_real_node(
                GET_CONF.antvip.antvip_afts_domain_name, real_node);
        if(result != RET_SUCC) {
            if (retries_ < GET_CONF.resolve_retry) {
                SLOG_I << "Retry resolving from antvip: " << retries_;
                if (IsTimeout()) {
                    return;
                }
                Resolve();
            } else {
//                    abort(MC_F_REQ_RESOLVE_HOST_ERR);
                SLOG_E << "Resolve from antvip error" << ", conn: " << GetTag() << ", task_id: "
                       << GetTaskId() << ", host: " << GET_CONF.antvip.antvip_afts_domain_name
                       << ", downgrade to lvs resolving.";
                retries_ = 0;
                res_type_ = LVS;
                Resolve();
            }

        } else {
            fetch_request_.SetHost(real_node.ip);
//          us_req_.service_ = std::to_string(real_node.healthCheckPort);
            fetch_request_.SetService("http");
            resolver_.ResolveWithoutCache(
                    fetch_request_.GetHost(),
                    fetch_request_.GetService(),
                    bind(&FetchConnection::HandleResolve,
                         shared_from_this(),
                         boost::asio::placeholders::error,
                         boost::asio::placeholders::iterator));
        }
    } else {
        SLOG_D << "Start resolving from lvs, conn: " << GetTag() << ", task_id: " << GetTaskId();
        auto ep = resolver_.Resolve(fetch_request_.GetHost(), fetch_request_.GetService(),
                                    bind(&imgsrv::FetchConnection::HandleResolve, shared_from_this(),
                                         boost::asio::placeholders::error, boost::asio::placeholders::iterator));
        if (ep != tcp::resolver::iterator()) {
            SLOG_D << "Resolving done(from cache), conn: " << GetTag() << ", task_id: " << GetTaskId();
            AsyncConnect(ep);
        }
        retries_++;
    }

}

void imgsrv::FetchConnection::HandleResolve(const error_code& e, tcp::resolver::iterator ep_it) {
    if (IsTimeout()) {
        SLOG_E << "Time out, fetch cancelled," << GetTag() << ", task_id: " << GetTaskId();
        return;
    }
    if (!e) {
        retries_ = 0;
        SLOG_D << "Resolving done, conn: " << GetTag() << ", task_id: " << GetTaskId();
        AsyncConnect(ep_it);

        if (res_type_ == LVS) {
            SLOG_D << "Set Resolved host : " << fetch_request_.GetHost() << "->"
                   << ep_it->endpoint().address().to_string() << " into cache, conn: "
                   << GetTag() << ", task_id: " << GetTaskId();
            resolver_.SetCache(fetch_request_.GetHost(), fetch_request_.GetService(), ep_it);
        }

    } else {

        if (retries_ < GET_CONF.resolve_retry) {
            SLOG_I << "Retry resolving: " << retries_ << GetTag() << ", task_id: " << GetTaskId();;
            Resolve();
        } else {
            if (res_type_ == ANTVIP && fetch_request_.GetHost() != "127.0.0.1" && GetTargetType() == kId) {
                SLOG_E << "Resolve from antvip error" << ", conn: " << GetTag() << ", task_id: "
                       << GetTaskId() << ", host: " << GET_CONF.antvip.antvip_afts_domain_name
                       << ", downgrade to lvs resolving.";
                retries_ = 0;
                res_type_ = LVS;
                Resolve();
            } else {
                Abort(Reply::kBadGateway);
                SLOG_E << "Resolve error" << ", conn: " << GetTag() << ", task_id: " << GetTaskId()
                       << ", host: " << fetch_request_.GetHost() << ", " << e.message();
            }
        }

    }
}

void imgsrv::FetchConnection::AsyncConnect(tcp::resolver::iterator ep_it) {
    if (IsTimeout()) {
        SLOG_E << "Time out, fetch cancelled," << GetTag() << ", task_id: " << GetTaskId();
        return;
    }
    SLOG_I << "Start connecting:" << ep_it->endpoint().address().to_string()
           << ", conn: " << GetTag() << ", task_id: " << GetTaskId();
    boost::asio::async_connect(socket_, ep_it,
            bind(&FetchConnection::HandleConnect, shared_from_this(), boost::asio::placeholders::error));
}

void imgsrv::FetchConnection::HandleConnect(const error_code& e) {
    if (IsTimeout()) {
        SLOG_E << "Time out, fetch cancelled," << GetTag() << ", task_id: " << GetTaskId();
        return;
    }
    if (!e) {

        SLOG_D << "Connecting done, conn: " << GetTag() << ", task_id: " << GetTaskId();

        /*TaskDataPtr task_data_ptr = CONTAINER.PoolFind(GetTaskId());
        if (task_data_ptr) {
            if (task_data_ptr->GetTargetType() == kId) {
                GenReqStr("/file/1.0/down", "fileIds=" + task_data_ptr->GetTarget(), task_data_ptr->GetTarget());
            }
        } else {
            abort(-4);
            return;
        }*/

        if (res_type_ == ANTVIP && fetch_request_.GetHost() != "127.0.0.1" && GetTargetType() == kId) {
            std::vector<std::string> vec;
            std::string old_req = fetch_request_.GetReqStr();
            std::stringstream req_ss;
            boost::split_regex(vec, old_req, boost::regex("\r\n"));
            for (auto it = vec.begin(); it != vec.end(); it++) {
                if (boost::starts_with(*it, "Host:")) {
                    req_ss << "Host: " << fetch_request_.GetHost() << "\r\n";
                } else {
                    if (it->empty()) {
                        continue;
                    }
                    req_ss << *it << "\r\n";
                }
            }
            req_ss << "\r\n";
            fetch_request_.SetReqStr(req_ss.str());
        }


        SLOG_I << "conn: " << GetTag() << ", task_id: " << GetTaskId() << ", fetching req:\n"
               << fetch_request_.GetReqStr();
        std::ostream request_stream(&hbuf_);
        request_stream << fetch_request_.GetReqStr();
        boost::asio::async_write(socket_, hbuf_,
                bind(&FetchConnection::HandleWrite, shared_from_this(),
                        boost::asio::placeholders::error));
    } else {
        if (res_type_ == ANTVIP && fetch_request_.GetHost() != "127.0.0.1" && GetTargetType() == kId) {
            Abort(Reply::kBadGateway);
            SLOG_E << "Connect error, conn: " << GetTag() << ", task_id: " << GetTaskId()
                   << ", host: " << GET_CONF.antvip.antvip_afts_domain_name << ", " << e.message();
        } else {
            Abort(Reply::kBadGateway);
            SLOG_E << "Connect error, conn: " << GetTag() << ", task_id: " << GetTaskId()
                    << ", host: " << fetch_request_.GetHost() << ", " << e.message();
        }
    }
}

void imgsrv::FetchConnection::HandleWrite(const error_code& e) {
    if (IsTimeout()) {
        SLOG_E << "Time out, fetch cancelled," << GetTag() << ", task_id: " << GetTaskId();
        return;
    }
    if (!e) {
        SLOG_D << "Read response, conn: "  << GetTag() << ", task_id: " << GetTaskId();
        boost::asio::async_read_until(socket_, hbuf_, "\r\n\r\n",
                bind(&FetchConnection::HandleReadHeader, shared_from_this(),
                        boost::asio::placeholders::error, boost::asio::placeholders::bytes_transferred));
    } else {
        Abort(Reply::kBadGateway);
        SLOG_E << "Send request error, conn: " << GetTag() << ", task_id: " << GetTaskId()
               << ", host: " << fetch_request_.GetHost() << ", " << e.message();
    }
}

void imgsrv::FetchConnection::HandleReadHeader(const error_code& e, size_t bytes_transferred) {
    if (IsTimeout()) {
        SLOG_E << "Time out, fetch cancelled," << GetTag() << ", task_id: " << GetTaskId();
        return;
    }
    if (!e && bytes_transferred > 4) {
        try {
            std::ostringstream ss;
            ss << &hbuf_;
            std::string data(ss.str());
            std::vector<std::string> vec;
            std::string header_str = data.substr(0, (bytes_transferred - 4));
            std::size_t data_size = data.size() - bytes_transferred;
            SLOG_D << "HandleReadHeader received size: " << bytes_transferred
                   << ", data size: " << data_size << ",conn: " << GetTag()
                   << ", task_id: " << GetTaskId();
            boost::split_regex(vec, header_str, boost::regex("\r\n"));
            auto it = vec.begin();
            std::vector<std::string> tmp_vec;
            boost::split_regex(tmp_vec, *it, boost::regex(" "));
            if (tmp_vec[0].substr(0, 5) != "HTTP/") {
                Abort(Reply::kBadGateway);
                throw std::string("Invalid response!");
            }
            int status_code = std::stoi(tmp_vec[1]);
            response_.SetStatusCode(status_code);
            if (status_code != 200) {
                Abort((imgsrv::Reply::StatusType)status_code);
                throw std::string("Upstream response with status code: ").append(tmp_vec[1]);
            }

            Header h;
            for (++it; it != vec.end(); ++it) {
                tmp_vec.clear();
                boost::split_regex(tmp_vec, *it, boost::regex(": "));
                h.name = tmp_vec[0];
                h.value = tmp_vec[1];
                std::string lower_header;
                lower_header.resize(h.name.size());
                transform(h.name.begin(), h.name.end(), lower_header.begin(), ::tolower);
                response_.GetHeaders()[lower_header] = h;
            }

            auto mit = response_.GetHeaders().find("content-length");
            if (mit == response_.GetHeaders().end()) {
                Abort(Reply::kBadGateway);
                throw std::string("Content-length error");
            } else {
                response_.SetContentLength(std::stoul(mit->second.value));
                if (response_.GetContentLength() <= 0) {
                    Abort(Reply::kBadGateway);
                    throw std::string("Content size equal to or smaller than 0 :").append(mit->second.value);
                }
                response_.SetCurLength(0);
            }

            SLOG_D << "conn: " << GetTag() << ", task_id: " << GetTaskId()
                   << ", header: " << header_str;
            if (data_size == 0) {
                ReadRspBody();
            } else {
                boost::system::error_code ignore_ec;
                memcpy(buffer_.data(), (data.c_str() + (data.size() - data_size)), data_size);
                HandleReadBody(ignore_ec, data_size);
            }
        } catch (std::string& e) {
            SLOG_E << e << ", conn: " << GetTag() << ", task_id: " << GetTaskId();
            return;
        }

    } else {
        Abort(Reply::kNotFound);
        SLOG_E << "Get http header from reply error" << ", conn: " << GetTag() << ", task_id: "
               << GetTaskId() << ", host[" << fetch_request_.GetHost() << "]." << e.message();
    }
}

void imgsrv::FetchConnection::ReadRspBody() {
    if (IsTimeout()) {
        SLOG_E << "Time out, fetch cancelled," << GetTag() << ", task_id: " << GetTaskId();
        return;
    }

    if (response_.GetContentLength() > response_.GetCurLength()) {
        size_t left_size = response_.GetContentLength() - response_.GetCurLength();
        size_t recv_size = (left_size < kConnBufSize) ? left_size : kConnBufSize;
        SLOG_D << "To receive size: " << recv_size << ", left_size: " << left_size << ", cont_len: "
               << response_.GetContentLength() << ", cur_len:" << response_.GetCurLength();
        boost::asio::async_read(socket_, buffer(buffer_), boost::asio::transfer_at_least(recv_size),
                                bind(&FetchConnection::HandleReadBody, shared_from_this(), boost::asio::placeholders::error,
                                     boost::asio::placeholders::bytes_transferred));
    }
}

void imgsrv::FetchConnection::HandleReadBody(const error_code& e, size_t bytes_transferred) {
    if (IsTimeout()) {
        SLOG_E << "Time out, fetch cancelled," << GetTag() << ", task_id: " << GetTaskId();
        return;
    }
    if (!e && bytes_transferred > 0) {
        if (response_.GetCurLength() < response_.GetContentLength()) {
            if (response_.GetCurLength() + bytes_transferred > response_.GetContentLength()) {
                bytes_transferred = response_.GetContentLength() - response_.GetCurLength();
            }
            SLOG_D << "Got data of size: " << bytes_transferred << ", conn: " << GetTag()
                   << ", task_id: " << GetTaskId();
            WriteFile(bytes_transferred);
        } else {
            SLOG_E << "Fetched more useless data" << ", conn: " << GetTag() << ", task_id: " << GetTaskId()
                   << ", host[" << fetch_request_.GetHost() << "].";
        }
    } else {
        Abort(Reply::kBadGateway);
        SLOG_E << "Get data from server error" << ", conn: " << GetTag() << ", task_id: " << GetTaskId()
               << ", host[" << fetch_request_.GetHost() << "]." << e.message();
    }
}

bool imgsrv::FetchConnection::WriteFile(size_t bytes_transferred) {
//    ofs_.write(buffer_.data(), buffer_.size());
    ofs_.write(buffer_.data(), bytes_transferred);
    response_.SetCurLength(response_.GetCurLength() + bytes_transferred);
    if (response_.GetCurLength() < response_.GetContentLength()) {
        ReadRspBody();
    } else {
        //Read done!
        ofs_.close();

        SetEndTime(Util::GetTimeStamp());
        SLOG_I << "Fetch file " << out_file_ << " done, conn: " << GetTag()
               << ", task_id: " << GetTaskId() << ", ets: " << GetEndTime();

        TaskDataPtr task_data_ptr = CONTAINER.PoolFind(GetTaskId());
        if (task_data_ptr) {
            Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
            auto it = response_.GetHeaders().find("content-type");
            std::string format = Mime::GetSuffixFromMime(it->second.name);
            if (task_data_ptr->GetProcType() == kImgWaterMark) {
                ImgWmTaskPtr img_wm_task_ptr =
                        boost::dynamic_pointer_cast<ImgWmTask>(task_data_ptr);
                if(out_file_ == task_data_ptr->GetInputFile()) {
                    task_data_ptr->SetInputFileDone(true);
                    stat_info.file_fetch_format_ = format;
                } else if (out_file_ == img_wm_task_ptr->GetWmFile()) {
                    img_wm_task_ptr->SetWmFileDone(true);
                    stat_info.logo_fetch_format_ = format;
                } else {
                    Abort(Reply::kInternalServerError);
                    SLOG_E << "Output file name doesn't match: " << out_file_
                           << ", conn: " << GetTag() << ", task_id: " << GetTaskId();
                    return false;
                }
            } else if (task_data_ptr->GetProcType() == kVideoCheck) {
                VideoCheckTaskPtr video_check_task_ptr =
                        boost::dynamic_pointer_cast<VideoCheckTask>(task_data_ptr);
                if(out_file_ == task_data_ptr->GetInputFile()) {
                    task_data_ptr->SetInputFileDone(true);
                    stat_info.file_fetch_format_ = format;
                } else if (out_file_ == video_check_task_ptr->GetSourceFile()) {
                    video_check_task_ptr->SetSourceFileDone(true);
                    stat_info.logo_fetch_format_ = format;
                } else {
                    Abort(Reply::kInternalServerError);
                    SLOG_E << "Output file name doesn't match: " << out_file_
                           << ", conn: " << GetTag() << ", task_id: " << GetTaskId();
                    return false;
                }
            } else {
                task_data_ptr->SetInputFileDone(true);
                stat_info.file_fetch_format_ = format;
            }
        } else {
            Abort(Reply::kInternalServerError);
            SLOG_E << "Task is gone, conn: " << GetTag() << ", task_id: " << GetTaskId();
            return false;
        }
        CollectStatInfo();
        PrepareTask();
    }

    return true;
}

bool imgsrv::FetchConnection::PrepareTask() {
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(GetTaskId());
    if (task_data_ptr) {
        TaskId task_id;
        task_id.SetId(GetTaskId());
        if (task_data_ptr->GetProcType() == kImgWaterMark) {
            ImgWmTaskPtr img_wm_task_ptr =
                    boost::dynamic_pointer_cast<ImgWmTask>(task_data_ptr);
            if (img_wm_task_ptr->IsInputFileDone() && img_wm_task_ptr->IsWmFileDone()) {
                CONTAINER.QueuePushBack(std::move(task_id));
                WORKER.GetIoService().post(
                        boost::bind(&imgsrv::Worker::ProcessNew, &WORKER));
            } else {
//                Abort(Reply::kInternalServerError);
                return false;
            }
        } else if (task_data_ptr->GetProcType() == kVideoCheck) {
            VideoCheckTaskPtr video_check_task_ptr =
                    boost::dynamic_pointer_cast<VideoCheckTask>(task_data_ptr);
            if (task_data_ptr->IsInputFileDone() && video_check_task_ptr->IsSourceFileDone()) {
                SLOG_D << task_id.GetId() << " : push video check task to queue.";
                CONTAINER.QueuePushBack(std::move(task_id));
                WORKER.GetIoService().post(
                        boost::bind(&imgsrv::Worker::ProcessNew, &WORKER));
            } else {
//                Abort(Reply::kInternalServerError);
                return false;
            }
        } else {
            if (task_data_ptr->IsInputFileDone()) {
                SLOG_D << task_id.GetId() << " : push task to queue.";
                CONTAINER.QueuePushBack(std::move(task_id));
                WORKER.GetIoService().post(
                        boost::bind(&imgsrv::Worker::ProcessNew, &WORKER));
            } else {
                Abort(Reply::kInternalServerError);
                return false;
            }
        }
    } else {
        Abort(Reply::kInternalServerError);
        return false;
    }
    return true;
}

imgsrv::FetchConnection::~FetchConnection() {
    if (ofs_.is_open()) {
        ofs_.close();
    }

    SLOG_I << "Fetch connection ended, conn: " << GetTag() << ", task_id: "
           << GetTaskId();
}

void imgsrv::FetchConnection::Abort(imgsrv::Reply::StatusType status) {
    SetEndTime(Util::GetTimeStamp());
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id_);
    if (task_data_ptr) {
        CollectStatInfo();
        task_data_ptr->GetServerConnectionPtr()->CancelTimer();
        task_data_ptr->GetServerConnectionPtr()->SendError(status);
    }
    CONTAINER.CancelTask(task_id_);
}

void imgsrv::FetchConnection::CollectStatInfo() {
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id_);
    if (task_data_ptr) {
        Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
        if(out_file_ == task_data_ptr->GetInputFile()) {
            stat_info.file_fetch_start_time_ = GetStartTime();
            stat_info.file_fetch_end_time_ = GetEndTime();
            stat_info.file_fetch_total_time_ =
                    stat_info.file_fetch_end_time_ - stat_info.file_fetch_start_time_;
            stat_info.file_fetch_rst_ = task_data_ptr->IsInputFileDone();
            stat_info.file_fetch_size_ = response_.GetContentLength();
        }

        if (task_data_ptr->GetProcType() == kImgWaterMark) {
            ImgWmTaskPtr img_wm_task_ptr =
                    boost::dynamic_pointer_cast<ImgWmTask>(task_data_ptr);
            if (out_file_ == img_wm_task_ptr->GetWmFile()) {
                stat_info.logo_fetch_start_time_ = GetStartTime();
                stat_info.logo_fetch_end_time_ = GetEndTime();
                stat_info.logo_fetch_total_time_ =
                        stat_info.logo_fetch_end_time_ - stat_info.logo_fetch_start_time_;
                stat_info.logo_fetch_rst_ = task_data_ptr->IsInputFileDone();
                stat_info.logo_fetch_size_ = response_.GetContentLength();
                stat_info.logo_fetch_rst_ = img_wm_task_ptr->IsWmFileDone();
                stat_info.logo_fetch_size_ = response_.GetContentLength();
            }
        } else if (task_data_ptr->GetProcType() == kVideoCheck) {
            VideoCheckTaskPtr video_check_task_ptr =
                    boost::dynamic_pointer_cast<VideoCheckTask>(task_data_ptr);
            if (out_file_ == video_check_task_ptr->GetSourceFile()) {
                stat_info.logo_fetch_start_time_ = GetStartTime();
                stat_info.logo_fetch_end_time_ = GetEndTime();
                stat_info.logo_fetch_total_time_ =
                        stat_info.logo_fetch_end_time_ - stat_info.logo_fetch_start_time_;
                stat_info.logo_fetch_rst_ = video_check_task_ptr->IsSourceFileDone();
                stat_info.logo_fetch_size_ = response_.GetContentLength();
            }
        }

    }
}


imgsrv::TargetType imgsrv::FetchConnection::GetTargetType() const {
    return target_type_;
}

void imgsrv::FetchConnection::SetTargetType(imgsrv::TargetType target_type) {
    target_type_ = target_type;
}