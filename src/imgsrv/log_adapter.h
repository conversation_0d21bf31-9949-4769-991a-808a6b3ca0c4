/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : log_adapter.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-07-31
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/

#ifndef IMGSRV_LOG_ADAPTER_H
#define IMGSRV_LOG_ADAPTER_H
#ifdef DUMMY_LOG
inline void dummy_log_fun() {
    return;
}
#define LOG_LD(fmt, ...) dummy_log_fun()
#define LOG_LI(fmt, ...) dummy_log_fun()
#define LOG_LW(fmt, ...) dummy_log_fun()
#define LOG_LE(fmt, ...) dummy_log_fun()
#define LOG_LF(fmt, ...) dummy_log_fun()

#define LOG_D(fmt, ...) dummy_log_fun()
#define LOG_I(fmt, ...) dummy_log_fun()
#define LOG_W(fmt, ...) dummy_log_fun()
#define LOG_E(fmt, ...) dummy_log_fun()
#define LOG_F(fmt, ...) dummy_log_fun()
#else

#include <logger/log.h>

extern sink_id g_sk_id;
extern sink_id g_stat_id;
extern sink_id g_ce_id;

bool LOG_INIT(const std::string& log_path, split_interval interval, log_level level);
bool STAT_INIT(const std::string& stat_path, split_interval interval);
bool CE_LOG_INIT(const std::string& ce_log_path, split_interval interval, log_level level);
int LOG_CLOSE();
int STAT_CLOSE();
int CE_LOG_CLOSE();

#define SLOG_LD slog_ld(g_sk_id)
#define SLOG_LI slog_li(g_sk_id)
#define SLOG_LW slog_lw(g_sk_id)
#define SLOG_LE slog_le(g_sk_id)
#define SLOG_LF slog_lf(g_sk_id)

#define SLOG_D slog_d(g_sk_id)
#define SLOG_I slog_i(g_sk_id)
#define SLOG_W slog_w(g_sk_id)
#define SLOG_E slog_e(g_sk_id)
#define SLOG_F slog_f(g_sk_id)


#define LOG_LD(fmt, ...) log_ld(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_LI(fmt, ...) log_li(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_LW(fmt, ...) log_lw(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_LE(fmt, ...) log_le(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_LF(fmt, ...) log_lf(g_sk_id, fmt, ##__VA_ARGS__)

#define LOG_D(fmt, ...) log_d(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_I(fmt, ...) log_i(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_W(fmt, ...) log_w(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_E(fmt, ...) log_e(g_sk_id, fmt, ##__VA_ARGS__)
#define LOG_F(fmt, ...) log_f(g_sk_id, fmt, ##__VA_ARGS__)

#define S_STAT slog_n(g_stat_id)
#define STAT(fmt, ...) log_n(g_stat_id, fmt, ##__VA_ARGS__)


//#define SCELOG_LD slog_ld(g_ce_id)
//#define SCELOG_LI slog_li(g_ce_id)
//#define SCELOG_LW slog_lw(g_ce_id)
//#define SCELOG_LE slog_le(g_ce_id)
//#define SCELOG_LF slog_lf(g_ce_id)
//
//#define SCELOG_D slog_d(g_ce_id)
//#define SCELOG_I slog_i(g_ce_id)
//#define SCELOG_W slog_w(g_ce_id)
//#define SCELOG_E slog_e(g_ce_id)
//#define SCELOG_F slog_f(g_ce_id)

#endif

#endif //IMGSRV_LOG_ADAPTER_H
