/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : config.cpp
 * <AUTHOR> we<PERSON><PERSON>(<EMAIL>)
 * @date     : 2019-07-31
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/

#include "config.h"
#include <iostream>
#include <libconfig.h++>
#define LOOKUP_REQUIRED_VALUE(S, N, V) {if(!S.lookupValue(N, V)) {std::cout << N << " is required!"; return false;}}
imgsrv::Config::Config() {
    conf_.server_thread_num = 0;
    conf_.server_port = 0;
    conf_.backlog = 0;
    conf_.log_path.clear();
    conf_.stat_path.clear();
    conf_.gmstat_path.clear();
}

bool imgsrv::Config::Parse(const std::string& conf_file) {
    libconfig::Config cfg;
    try {
        cfg.readFile(conf_file.c_str());
    } catch (const libconfig::FileIOException& fioex){
        std::cout << "I/O error while reading file." << std::endl;
        return false;
    } catch (const libconfig::ParseException& pex) {
        std::cout << "Parse error at " << pex.getFile() << ":" << pex.getLine()
                  << " - " << pex.getError() << std::endl;
        return false;
    }

    try {
        //server
        const libconfig::Setting& root = cfg.getRoot();
        const libconfig::Setting& imgsrv = root.lookup("imgsrv");
        const libconfig::Setting& server = imgsrv.lookup("server");
        LOOKUP_REQUIRED_VALUE(server, "thread_num", conf_.server_thread_num);
        LOOKUP_REQUIRED_VALUE(server, "port", conf_.server_port);
        server.lookupValue("backlog", conf_.backlog);
        LOOKUP_REQUIRED_VALUE(server, "default_time_out", conf_.default_time_out);

        LOOKUP_REQUIRED_VALUE(imgsrv, "out_file_path", conf_.out_file_path);
//        LOOKUP_REQUIRED_VALUE(imgsrv, "bin_path", conf_.bin_path);
//        LOOKUP_REQUIRED_VALUE(imgsrv, "font_path", conf_.font_path);
        LOOKUP_REQUIRED_VALUE(imgsrv, "third_path", conf_.third_path);

        //worker
        const libconfig::Setting& worker = imgsrv.lookup("worker");
        LOOKUP_REQUIRED_VALUE(worker, "thread_num", conf_.worker_thread_num);
        LOOKUP_REQUIRED_VALUE(worker, "process_num", conf_.worker_process_num);

        //logger
        const libconfig::Setting& logger = imgsrv.lookup("logger");
        LOOKUP_REQUIRED_VALUE(logger, "log_level", conf_.log_level);
        LOOKUP_REQUIRED_VALUE(logger, "log_path", conf_.log_path);
        LOOKUP_REQUIRED_VALUE(logger, "stat_path", conf_.stat_path);
        LOOKUP_REQUIRED_VALUE(logger, "gmstat_path", conf_.gmstat_path);
        LOOKUP_REQUIRED_VALUE(logger, "ce_log_path", conf_.ce_log_path);

        //afts
        const libconfig::Setting& afts = imgsrv.lookup("afts");
        LOOKUP_REQUIRED_VALUE(afts, "dns_refresh_interval", conf_.dns_refresh_interval);
        LOOKUP_REQUIRED_VALUE(afts, "resolve_retry", conf_.resolve_retry);

        const libconfig::Setting& host_names = afts.lookup("host_names");
        int count = 0;
        if (host_names.isArray()) {
            count = host_names.getLength();
            if (count <= 0) {
                return false;
            }
            for (int i = 0; i < count; ++i) {
                conf_.host_names.emplace_back(host_names[i].c_str());
            }
        } else {
            return false;
        }

        state_.antvip_enabled = false;
        if(afts.exists("antvip")) {
            const libconfig::Setting& antvip = afts.lookup("antvip");
            if(!(antvip.lookupValue("antvip_cloud_inc", conf_.antvip.antvip_cloud_inc)
                 && antvip.lookupValue("antvip_log", conf_.antvip.antvip_log)
                 && antvip.lookupValue("antvip_log_level", conf_.antvip.antvip_log_level)
                 && antvip.lookupValue("antvip_afts_domain_name", conf_.antvip.antvip_afts_domain_name))) {
                return false;
            }
            state_.antvip_enabled = true;
        }

        //processer
        const libconfig::Setting& processer = imgsrv.lookup("processer");
        LOOKUP_REQUIRED_VALUE(processer, "gm_zoom_type", conf_.gm_zoom_type);
        LOOKUP_REQUIRED_VALUE(processer, "hevc_thread_num", conf_.hevc_thread_num);

        //debug
        conf_.rm_tmp_dir = true;
        if (imgsrv.exists("debug")) {
            const libconfig::Setting& debug = imgsrv.lookup("debug");
            debug.lookupValue("rm_tmp_dir", conf_.rm_tmp_dir);
        }



    } catch (const libconfig::SettingNotFoundException& nfex) {
        std::cout << nfex.what() << std::endl;
        return false;
    }

    return true;
}

const imgsrv::Conf& imgsrv::Config::GetConf() {
    return conf_;
}

/*
imgsrv::Config* imgsrv::Config::GetInstance() {
    if (p_instance_ == nullptr) {
        WriteLock lock(mutex_);
        if (p_instance_ == nullptr) {
            p_instance_ = new Config();
        }
    }
    return p_instance_;
}
*/


