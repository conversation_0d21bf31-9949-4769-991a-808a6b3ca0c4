/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : request.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_REQUEST_H
#define IMGSRV_REQUEST_H

#include <string>
#include "url.h"
#include "header.h"
#include "reply.h"
#include "common.h"

namespace imgsrv {

/*enum ReqType {
    kGet = 0,
    kPost,
    kErrMethod
};

enum CallType {
    kBin = 0,
    kApi,
    kErrCallType
};

enum TargetType {
    kId = 0,
    kUrl,
    kErr
};*/

class Request {
public:
    friend class HttpRequestParser;
    ProcType proc_type_;

    /*enum UriType {
        kUrl = 0,
        kId = 1
    };*/

    Request();

    void Reset();

    std::string ToString();

    bool ValidateAudio(Reply::StatusType& status);
    bool ValidateImg(Reply::StatusType& status);
    bool ValidateVideo(Reply::StatusType& status);
    bool ValidateGet(Reply::StatusType& status);

    ReqType GetReqType() const;

    void SetReqType(ReqType req_type);

    ProcType GetProcType() const;

    void SetProcType(ProcType proc_type);

    const std::string& GetRemote() const;

    void SetRemote(const std::string& remote);

    const std::string& GetMethod() const;

    const std::string& GetUri() const;

    void SetUri(const std::string& uri);

    const Url& GetUrl() const;

    const std::map<std::string, ProcType>& GetApiType() const;

    const std::map<std::string, Header>& GetHeaders() const;

    /*UriType GetTargetType() const;

    void SetTargetType(UriType target_type);

    const std::string& GetTargetFileId() const;

    void SetTargetFileId(const std::string& target_file_id);

    const std::string& GetTargetUrl() const;

    void SetTargetUrl(const std::string& target_url);

    const std::string& GetTargetSuffix() const;

    void SetTargetSuffix(const std::string& target_suffix);*/

    const std::string& GetTid() const;

    void SetTid(const std::string& tid);

    const std::string& GetTraceId() const;

    void SetTraceId(const std::string& trace_id);

    const std::string& GetRpcId() const;

    void SetRpcId(const std::string& rpc_id);

private:
    std::string remote_;
    std::string method_;
    ReqType req_type_;
    std::string uri_;
//    UriType target_type_;
//    std::string target_file_id_;
//    std::string target_url_;
//    std::string target_suffix_;
    Url url_;
    int http_version_major_;
    int http_version_minor_;
    std::map<std::string, Header> headers_;
    std::string tid_;
    std::map<std::string, ProcType> api_type_;
    std::map<std::string, audio_type> audio_type_;
    //trace跟踪信息
    std::string trace_id_;
    std::string rpc_id_;
public:
    const std::map<std::string, audio_type>& GetAudioType() const;
};

class FetchRequest {
public:
    FetchRequest();

    const std::string& GetReqStr() const;

    void SetReqStr(const std::string& req_str);

    const std::string& GetHost() const;

    void SetHost(const std::string& host);

    const std::string& GetService() const;

    void SetService(const std::string& service);

private:
    std::string req_str_;
    std::string host_;
    /*A string identifying the requested service. This may be a
    * descriptive name or a numeric string corresponding to a port number, eg.(http, 80). May
    * be an empty string, in which case all resolved endpoints will have a port
    * number of 0.*/
    std::string service_;
};
}


#endif //IMGSRV_REQUEST_H
