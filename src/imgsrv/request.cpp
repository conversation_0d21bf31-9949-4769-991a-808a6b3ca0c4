/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : request.cpp
 * <AUTHOR> we<PERSON><PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include <sstream>
#include "request.h"
#include "log_adapter.h"

imgsrv::Request::Request() {
    Reset();
}

void imgsrv::Request::Reset() {
    remote_ = "";
    method_ = "";
    req_type_ = kErrMethod;
    uri_ = "";
    proc_type_ = kImgProc;
    url_.Reset();
    http_version_major_ = 0;
    http_version_minor_ = 0;
    headers_.clear();
    api_type_.clear();
    api_type_["/img/imgproc"] = kImgProc;
    api_type_["/img/imgwm"] = kImgWaterMark;
    api_type_["/img/textwm"] = kTextWaterMark;
    api_type_["/img/avif"] = kImgAvif;
    api_type_["/audio/convert"] = kAudioConvert;
    api_type_["/video/process"] = kVideoProc;

    audio_type_["pcm"] = kpcm;
    audio_type_["amr"] = kamr;
    audio_type_["silk"] = ksilk;
    tid_ = "";
    trace_id_ = "";
    rpc_id_ = "";
}

std::string imgsrv::Request::ToString() {
    std::stringstream ss;
    ss << "remote:" << remote_ << ";"
       << "method:" << method_ << ";"
       << "url:" << url_.GetOriginalUrl() << ";"
       << "req_type:" << req_type_ << ";"
       << "proc_type:" << proc_type_ << ";"
       << "decoded_uri:" << url_.GetDecodedUrl() << ";"
       << "action:" << url_.GetPath() << ";"
       << "params:" << "{";
    for (const auto & it : url_.GetParams()) {
        ss << it.first << ":" << it.second << ";";
    }
    ss << "}";
    ss << "http_version_major:" << http_version_major_ << ";";
    ss << "headers:" << "{";
    for (const auto & i : headers_) {
        ss << i.second.name << ": " << i.second.value << ";";
    }
    ss << "}";
    ss << "tid:" << tid_ << ";";
    ss << "traceid:" << trace_id_<< ";";
    ss << "rpcid:" << rpc_id_<< ";";
    return ss.str();
}

bool imgsrv::Request::ValidateAudio(Reply::StatusType& status) {
    std::string format;
    audio_type at;
    auto it_param = url_.GetParams().find("sf");
    if (it_param == url_.GetParams().end()) {
        SLOG_E << "sf is empty!";
        return false;
    } else {
        format = it_param->second;
        auto it = audio_type_.find(format);
        if (it == audio_type_.end()) {
            SLOG_E << "sf is wrong!";
            return false;
        } else {
            at = it->second;
        }
    }

    it_param = url_.GetParams().find("ssr");
    if (it_param != url_.GetParams().end()) {
        int ssr = std::stoi(it_param->second);
        switch (at) {
            case kpcm:
                if (ssr < 8000 || ssr > 48000) {
                    SLOG_E << "ssr: " << ssr << " of pcm is wrong, only support [8000-48000]Hz";
                    return false;
                }
                break;
            case kamr:
                if (ssr != 8000) {
                    SLOG_E << "ssr: " << ssr << " of amr is wrong, only support 8000 Hz";
                    return false;
                }
                break;
            case ksilk:
                if (ssr != 8000 && ssr != 12000 && ssr != 16000 && ssr != 24000) {
                    SLOG_E << "ssr: " << ssr << " of silk is wrong, only support [8000, 12000, 16000, 24000]Hz";
                    return false;
                }
                break;
            default:
                break;
        }
    }

    it_param = url_.GetParams().find("df");
    if (it_param == url_.GetParams().end()) {
        SLOG_E << "df is empty!";
        return false;
    } else {
        format = it_param->second;
        auto it = audio_type_.find(format);
        if (it == audio_type_.end()) {
            SLOG_E << "df is wrong!";
            return false;
        } else {
            at = it->second;
        }
    }

    it_param = url_.GetParams().find("dsr");
    if (it_param != url_.GetParams().end()) {
        int dsr = std::stoi(it_param->second);
        switch (at) {
            case kpcm:
                if (dsr < 8000 || dsr > 48000) {
                    SLOG_E << "dsr: " << dsr << " of pcm is wrong, only support [8000-48000]Hz";
                    return false;
                }
                break;
            case kamr:
                if (dsr != 8000) {
                    SLOG_E << "dsr: " << dsr << " of amr is wrong, only support 8000 Hz";
                    return false;
                }
                break;
            case ksilk:
                if (dsr != 8000 && dsr != 12000 && dsr != 16000 && dsr != 24000) {
                    SLOG_E << "dsr: " << dsr << " of silk is wrong, only support [8000, 12000, 16000, 24000]Hz";
                    return false;
                }
                break;
            default:
                break;
        }
    }
    return true;

}

bool imgsrv::Request::ValidateImg(Reply::StatusType& status) {
    auto it_param = url_.GetParams().find("uritype");
    if (it_param == url_.GetParams().end()) {
        SLOG_E << "uritype is empty!";
        return false;
    }

    auto it_header = headers_.find("ciphertype");
    if (it_header != headers_.end()) {
        int cipher_type = std::stoi(it_header->second.value);
        if(cipher_type != 0 && cipher_type != 1) {
            return false;
        }

        it_header = headers_.find("cipherkey");
        if (it_header == headers_.end()) {
            return false;
        }

        it_header = headers_.find("docrypt");
        if (it_header == headers_.end()) {
            return false;
        } else {
            if (it_header->second.value != "0" || it_header->second.value != "1"
                || it_header->second.value != "2") {
                return false;
            }
        }
    }

    switch (proc_type_) {
        case kImgProc: {
            it_param = url_.GetParams().find("zoom");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "zoom is empty!";
                return false;
            }

            /*it_param = url_.GetParams().find("urisuffix");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "urisuffix is empty!";
                return false;
            }*/
        }
            break;
        case kImgAvif: {
            it_param = url_.GetParams().find("zoom");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "zoom is empty!";
                return false;
            }

            /*it_param = url_.GetParams().find("urisuffix");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "urisuffix is empty!";
                return false;
            }*/
        }
            break;
        case kImgWaterMark: {

            it_param = url_.GetParams().find("wmid");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "wmid is empty!";
                return false;
            }

            /*it_param = url_.GetParams().find("wp");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "wp is empty!";
                return false;
            }*/

        }
            break;
        case kTextWaterMark: {

            /*it_param = url_.GetParams().find("wp");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "wp is empty!";
                return false;
            }*/

            it_header = headers_.find("text");
            if (it_header == headers_.end()) {
                SLOG_E << "text is empty!";
                return false;
            }

            it_param = url_.GetParams().find("s");
            if (it_param == url_.GetParams().end()) {
                SLOG_E << "s is empty!";
                return false;
            }
        }
            break;
        default:
            break;
    }

    return true;
}

bool imgsrv::Request::ValidateVideo(Reply::StatusType &status) {
    //todo 对视频请求进行校验
    return true;
}

bool imgsrv::Request::ValidateGet(Reply::StatusType& status) {
    req_type_ = kGet;
    status = Reply::kBadRequest;
    auto it = api_type_.find(url_.GetPath());
    if (it == api_type_.end()) {
        return false;
    } else {
        proc_type_ = it->second;
    }

    auto it_param = url_.GetParams().find("fileid");
    if (it_param == url_.GetParams().end()) {
        SLOG_E << "fileid is empty!";
        return false;
    }

    auto it_header = headers_.find("taskid");
    if (it_header == headers_.end()) {
        SLOG_E << "taskid is empty!";
        return false;
    } else {
        tid_ = it_header->second.value;
    }

    it_header = headers_.find("sofa-traceid");
    if (it_header != headers_.end()) {
        trace_id_ = it_header->second.value;
    }

    it_header = headers_.find("sofa-rpcid");
    if (it_header != headers_.end()) {
        std::string prev_rpc_id = it_header->second.value;
        rpc_id_ = prev_rpc_id + ".1";
    }

    if (proc_type_ == kImgProc || proc_type_ == kImgWaterMark || proc_type_ == kTextWaterMark || proc_type_ == kImgAvif) {
        return ValidateImg(status);
    } else if (proc_type_ == kAudioConvert) {
        return ValidateAudio(status);
    } else if (proc_type_ == kVideoProc){
        return ValidateVideo(status);
    }else {
        return false;
    }

    return false;
}

imgsrv::ReqType imgsrv::Request::GetReqType() const {
    return req_type_;
}

void imgsrv::Request::SetReqType(ReqType req_type) {
    req_type_ = req_type;
}

imgsrv::ProcType imgsrv::Request::GetProcType() const {
    return proc_type_;
}

void imgsrv::Request::SetProcType(imgsrv::ProcType proc_type) {
    proc_type_ = proc_type;
}

const std::string& imgsrv::Request::GetRemote() const {
    return remote_;
}

void imgsrv::Request::SetRemote(const std::string& remote) {
    remote_ = remote;
}

const std::string& imgsrv::Request::GetUri() const {
    return uri_;
}

void imgsrv::Request::SetUri(const std::string& uri) {
    uri_ = uri;
}

/*imgsrv::Request::UriType imgsrv::Request::GetTargetType() const {
    return target_type_;
}

void imgsrv::Request::SetTargetType(imgsrv::Request::UriType target_type) {
    target_type_ = target_type;
}

const std::string& imgsrv::Request::GetTargetFileId() const {
    return target_file_id_;
}

void imgsrv::Request::SetTargetFileId(const std::string& target_file_id) {
    target_file_id_ = target_file_id;
}

const std::string& imgsrv::Request::GetTargetUrl() const {
    return target_url_;
}

void imgsrv::Request::SetTargetUrl(const std::string& target_url) {
    target_url_ = target_url;
}

const std::string& imgsrv::Request::GetTargetSuffix() const {
    return target_suffix_;
}

void imgsrv::Request::SetTargetSuffix(const std::string& target_suffix) {
    target_suffix_ = target_suffix;
}*/

const std::string& imgsrv::Request::GetTid() const {
    return tid_;
}

void imgsrv::Request::SetTid(const std::string& tid) {
    tid_ = tid;
}

const std::string& imgsrv::Request::GetTraceId() const {
    return trace_id_;
}

void imgsrv::Request::SetTraceId(const std::string& trace_id) {
    trace_id_ = trace_id;
}

const std::string& imgsrv::Request::GetRpcId() const {
    return rpc_id_;
}

void imgsrv::Request::SetRpcId(const std::string& rpc_id) {
    rpc_id_ = rpc_id;
}

const imgsrv::Url& imgsrv::Request::GetUrl() const {
    return url_;
}

const std::map<std::string, imgsrv::ProcType>& imgsrv::Request::GetApiType() const {
    return api_type_;
}

const std::map<std::string, imgsrv::Header>& imgsrv::Request::GetHeaders() const {
    return headers_;
}

const std::string& imgsrv::Request::GetMethod() const {
    return method_;
}

const std::map<std::string, imgsrv::audio_type>& imgsrv::Request::GetAudioType() const {
    return audio_type_;
}

imgsrv::FetchRequest::FetchRequest() : req_str_(""), host_(""), service_(""){

}

const std::string& imgsrv::FetchRequest::GetReqStr() const {
    return req_str_;
}

void imgsrv::FetchRequest::SetReqStr(const std::string& req_str) {
    req_str_ = req_str;
}

const std::string& imgsrv::FetchRequest::GetHost() const {
    return host_;
}

void imgsrv::FetchRequest::SetHost(const std::string& host) {
    host_ = host;
}

const std::string& imgsrv::FetchRequest::GetService() const {
    return service_;
}

void imgsrv::FetchRequest::SetService(const std::string& service) {
    service_ = service;
}
