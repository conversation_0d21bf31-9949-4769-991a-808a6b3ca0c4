/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : resolver.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include "resolver.h"
tcp::resolver::iterator imgsrv::DnsCache::Get(const std::string &host, int64_t &ts) {
    RLock lock(mutex_);
    auto it = cache_.find(host);
    if (it != cache_.end()) {
        ts = it->second.ts_;
        return it->second.it_ep_;
    } else {
        return tcp::resolver::iterator();
    }
}

void imgsrv::DnsCache::Set(const std::string &host, const tcp::resolver::iterator &ep_it) {
    WLock lock(mutex_);
    cache_[host] = EpVal(ep_it);
}


imgsrv::DnsCache::DnsCache() {

}
