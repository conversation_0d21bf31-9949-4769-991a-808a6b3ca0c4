/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : img_dj_cmd_proc.cpp
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019/8/28
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include <unistd.h>
#include "img_dj_cmd_proc.h"
#include "log_adapter.h"
#include "task_data.h"
#include "container.h"
#include "config.h"
#include "command_executor.h"
#include "get_image_saliency.h"

imgsrv::ImgDjCmdProc::ImgDjCmdProc() : bin_path_(""), output_path_(""), third_path_(""), hevc_thread_num_(0){

}

bool imgsrv::ImgDjCmdProc::Init(
        const std::string& output_path,
        const std::string& third_path,
        int hevc_thread_num) {
    bin_path_ = third_path + "/bin";
    output_path_ = output_path;
    third_path_ = third_path;
    hevc_thread_num_ = hevc_thread_num;

    if (access(GetSmartCropBinPath().c_str(), 0)) {
        SLOG_E << GetSmartCropBinPath() << " is not accessible!";
        return false;
    }
    if (access(GetGmPath().c_str(), 0)) {
        SLOG_E << GetGmPath() << " is not accessible!";
        return false;
    }

    return true;
}

std::string getCurrentDate() {
    // 获取当前时间戳
    time_t now = time(nullptr);

    // 使用线程安全的 localtime_r 转换为本地时间
    tm timeStruct{};
    localtime_r(&now, &timeStruct);  // Linux下推荐使用localtime_r

    // 格式化为字符串
    char buffer[12];  // "YYYY-MM-DD\0" 最大需要11字符 + 1终止符
    strftime(buffer, sizeof(buffer), "%Y-%m-%d", &timeStruct);

    return std::string(buffer);
}

bool imgsrv::ImgDjCmdProc::Process(const std::string& task_id) {
    bool rst = false;
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        if (task_data_ptr->GetProcType() != kImgProc) {
            //TODO: what?
            return false;
        }
        ImgProcTaskPtr img_proc_task_ptr =
                boost::dynamic_pointer_cast<ImgProcTask>(task_data_ptr);
        rst = DoParam(task_id, task_data_ptr, img_proc_task_ptr->GetParam1());
        if(!rst) {
            std::string param2 = img_proc_task_ptr->GetParam2();
            SLOG_E << task_id << " : " << "downgrade to Param2. "
                   << img_proc_task_ptr->GetParam2();
            if (!param2.empty()) {
                rst = DoParam(task_id, task_data_ptr, img_proc_task_ptr->GetParam2());
            }
            rst = DoParam(task_id, task_data_ptr, img_proc_task_ptr->GetParam2());
        }

    } else {
        return false;
    }
    return rst;
}


bool imgsrv::ImgDjCmdProc::ProcessSelfAvif(const std::string& task_id) {
    bool rst = false;
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        if (task_data_ptr->GetProcType() != kImgAvif) {
            return false;
        }
        ImgProcTaskPtr img_proc_task_ptr =
                boost::dynamic_pointer_cast<ImgProcTask>(task_data_ptr);
        rst = DoSelfAvifParam(task_id, task_data_ptr, img_proc_task_ptr->GetParam1());
    } else {
        return false;
    }
    return rst;
}

/*
bool imgsrv::ImgDjCmdProc::Process(const std::string& task_id) {
    bool rst = false;
    TaskDataPtr task_data_ptr = CONTAINER.PoolFind(task_id);
    if (task_data_ptr) {
        if (task_data_ptr->GetProcType() != kImgProc) {
            //TODO: what?
            return false;
        }
        ImgProcTaskPtr img_proc_task_ptr =
                boost::dynamic_pointer_cast<ImgProcTask>(task_data_ptr);
        std::map<std::string, std::vector<int> > cmd_params;
        std::string suffix;
//        std::string gm_cmd;
        std::vector<std::string> gm_cmds;
        CmdType cmd_type = ParseDjCmd(task_id, img_proc_task_ptr->GetParam1(), cmd_params, suffix, gm_cmds);
        if (cmd_type == kErrCmd) {
            SLOG_E << task_id << " : " << "django command 1 parse failed. "
                   << img_proc_task_ptr->GetParam1();
            if (!img_proc_task_ptr->GetParam2().empty()) {
                cmd_params.clear();
                gm_cmds.clear();
                cmd_type = ParseDjCmd(task_id, img_proc_task_ptr->GetParam2(), cmd_params, suffix, gm_cmds);
                if (cmd_type == kErrCmd) {
                    SLOG_E << task_id << " : " << "django command 2 parse failed. "
                    << img_proc_task_ptr->GetParam2();
                    return false;
                }
            } else {
                return false;
            }
        }


        std::string input_path = img_proc_task_ptr->GetInputFile();
        switch (cmd_type) {
            case kGm:
                rst = GmCommand(input_path, task_data_ptr, gm_cmds, suffix);
                break;
            case kZn:
                rst = ZnCommand(input_path, task_data_ptr, cmd_params, suffix);
                break;
            case kZx:
                rst = ZxCommand(input_path, task_data_ptr, cmd_params, suffix);
                break;
            case kXz:
                rst = XzCommand(input_path, task_data_ptr, cmd_params, suffix);
                break;
            default:
                SLOG_E << task_id <<" : unknow CONVERT Command type. " << cmd_type;
                return false;
        }
    } else {
        return false;
    }
    return rst;
}
*/

imgsrv::CmdType imgsrv::ImgDjCmdProc::ParseDjCmd(
        const std::string& task_id,
        const std::string& cmds,
        std::map<std::string, std::vector<int> >& cmd_params,
        std::string& suffix,
        std::vector<std::string>& gm_cmds) {
    CmdType ret_type = kErrCmd;
    std::vector<std::string> cmd_v;
    Util::Split(cmds, "|/", &cmd_v);    //兼容.jpeg|300w_300h,for客户端异常请求
    if (cmd_v.empty()) {
        return kErrCmd;
    }

    for (const auto& i : cmd_v) {
        std::vector<std::string> cmd;
        Util::Split(i, '.', &cmd);
        if (cmd.empty()) {
            SLOG_E << task_id << " : " << "Parse Django suffix error.";
            return kErrCmd;
        }

        if (cmd.size() > 2) {
            suffix.clear();
            for (size_t k = 1; k < cmd.size(); ++k) {
                if (1 == k) {
                    suffix += cmd[k];
                } else {
                    suffix += "." + cmd[k];
                }
            }
        } else {
            suffix = ((cmd.size() == 2) && (!cmd[1].empty())) ? cmd[1] : "src";
        }

        if (suffix != "src") {
            Util::StringReplace(suffix, "`", "");
            Util::StringReplace(suffix, "'", "");
            Util::StringReplace(suffix, "\"", "");
            Util::StringReplace(suffix, ")", "");
            Util::StringReplace(suffix, "(", "");
            Util::StringReplace(suffix, ">", "");
            Util::StringReplace(suffix, "<", "");
            Util::StringReplace(suffix, ";", "");
            Util::StringReplace(suffix, "&", "");
            Util::StringReplace(suffix, "|", "");
            Util::StringReplace(suffix, ",", "");
        }

//        if (cmd[0].empty()) {
//            return kErrCmd;
//        }

        try {
            if (cmd[0].empty()) {
            } else if (cmd[0].find('_') == std::string::npos
                && cmd[0].find('-') == std::string::npos
                && cmd[0].find('x') != std::string::npos) {
                // Parse old tfs style
                std::vector<std::string> qv;
                Util::Split(cmd[0], 'q', &qv);
                int w, h, q = 90;
                if (qv.size() == 2) {
                    q = std::stoi(qv[1]);
                }
                std::vector<std::string> params;
                Util::Split(qv[0], 'x', &params);
                if (params.size() < 1) {
                    return kErrCmd;
                }
                w = std::stoi(params[0]);
                h = std::stoi(params[(params.size() > 1) ? 1 : 0]);
                if (params.size() == 3) {
                    if (params[2] == "z") {
                        cmd_params["w"] = {w};
                        cmd_params["h"] = {h};
                        cmd_params["q"] = {q};
                        cmd_params["e"] = {1};
                        cmd_params["c"] = {1};
                    } else if (params[2] == "c") {
                        cmd_params["rc"] = {w, h, 5};
                        cmd_params["q"] = {q};
                    } else {
                        cmd_params["w"] = {w};
                        cmd_params["h"] = {h};
                        cmd_params["q"] = {q};
                    }
                } else {
                    cmd_params["w"] = {w};
                    cmd_params["h"] = {h};
                    cmd_params["q"] = {q};
                }
            } else {
                // Parse new django style
                std::vector<std::string> cv;
                Util::Split(cmd[0], '_', &cv);
                if (cv.empty()) {
                    return kErrCmd;
                }
                for (const auto& ni : cv) {
                    std::string c;
                    int pos;
                    if (!Util::GetStringBack(ni, c, pos)) {
                        SLOG_E << task_id << " : " << "Django getStringBack error : " << ni;
                        return kErrCmd;
                    }
                    if (c == "bgc" || c == "a" || c == "bl" || c == "ci") {
                        std::vector<std::string> params;
                        params.clear();
                        Util::Split(ni, '-', &params);
                        cmd_params[c];
                        for (const auto& j : params) {
                            int param;
                            if (!Util::GetNumFront(j, param, pos)) {
                                SLOG_E << task_id << " : " << "Django getNumFront error : " << j;
                                return kErrCmd;
                            }
                            cmd_params[c].push_back(param);
                        }
                    } else if (c == "rc") {
                        std::vector<std::string> params;
                        Util::Split(ni, '-', &params);
                        if (params.size() != 2 && params.size() != 3) {
                            SLOG_E << task_id << " : " << "error: rc=" << ni;
                            return kErrCmd;
                        }
                        std::vector<std::string> lens;
                        Util::Split(params[0], 'x', &lens);
                        int w = 0, h = 0, p = 0;
                        if (lens.size() == 1) {
                            w = std::stoi(lens[0]);
                        } else if (lens.size() == 2) {
                            w = std::stoi(lens[0]);
                            h = std::stoi(lens[1]);
                        }
                        if (!Util::GetNumFront(params[1], p, pos)) {
                            SLOG_E << task_id << " : " << "Django getNumFront error : " << ni;
                            return kErrCmd;
                        }
                        cmd_params[c] = {w, h, p};
                    } else if ((ni.find('x') != string::npos) && (ni != "xz") && (ni != "zx")) {
                        //兼容tfs格式：6v_80q_150x150xz.JPEG_df.ahp2 --> 150x150xz, 150x150xc, 150x150
                        // Parse old tfs style
                        int w, h;
                        std::vector<std::string> params;
                        Util::Split(ni, 'x', &params);
                        if (params.size() < 1) {
                            return kErrCmd;
                        }
                        w = std::stoi(params[0]);
                        h = std::stoi(params[(params.size() > 1) ? 1 : 0]);
                        if (params.size() == 3) {
                            if (params[2] == "z") {
                                cmd_params["w"] = {w};
                                cmd_params["h"] = {h};
                                cmd_params["e"] = {1};
                                cmd_params["c"] = {1};
                            } else if (params[2] == "c") {
                                cmd_params["rc"] = {w, h, 5};
                            } else {
                                cmd_params["w"] = {w};
                                cmd_params["h"] = {h};
                            }
                        } else {
                            cmd_params["w"] = {w};
                            cmd_params["h"] = {h};
                        }
                    } else if (c == "zn") {
                        cmd_params[c] = {1};
                        ret_type = kZn;
                    } else if (c == "zx") {
                        cmd_params[c] = {1};
                        ret_type = kZx;
                    } else if (c == "xz") {
                        cmd_params[c] = {1};
                        ret_type = kXz;
                    } else if (c == "df") {
                        cmd_params[c] = {1};
                    } else if (c == "sf") {
                        cmd_params[c] = {1};
                    } else if (c == "mf") {
                        cmd_params[c] = {1};
                    } else if ((c == "Os") || (c == "os")) {
                        cmd_params["Os"] = {1};
                        cmd_params["os"] = {1};
                    } else {
                        int param;
                        if (!Util::GetNumFront(ni, param, pos)) {
                            SLOG_E << task_id << " : " << "Django getNumFront error : " << ni;
                            return kErrCmd;
                        }
                        cmd_params[c] = {param};
                    }
                }
            }
        } catch (const std::invalid_argument& e) {
            SLOG_E << task_id << " : " << "Invalid argument: " << e.what();
            return kErrCmd;
        } catch (const std::out_of_range& e) {
            SLOG_E << task_id << " : " << "Argument out of range: " << e.what();
            return kErrCmd;
        } catch (const std::exception& e) {
            SLOG_E << task_id << " : " << "Undefined error: " << e.what();
            return kErrCmd;
        }

        if (cmd_params.find("q") == cmd_params.end()) {
            cmd_params["q"] = {90};
        }
//        if (cmd_params.find("o") == cmd_params.end()
//            && cmd_params.find("r") == cmd_params.end()) {
//            cmd_params["o"] = {2};
//        }

        std::string gm_cmd;
        if (!ConvertDjangoToGM(task_id, cmd_params, gm_cmd, suffix)) {
            SLOG_E << task_id << ":" << "convertDjangoToGM error : " << gm_cmd;
            return kErrCmd;
        }

        SLOG_I << "CONVERT_PROC : " << gm_cmd;
       /* convert_cmd_->set_type(ImgConvertProcCmd::GM_COMMAND);
        ImgGMProcCommand* gc = convert_cmd_->add_gm_command();
        gc->set_command("convert");
        gc->set_opt(gm_cmd);*/

        gm_cmds.emplace_back(gm_cmd);
        ret_type = (ret_type != kErrCmd) ? ret_type : kGm;
        if ((ret_type == kZn) || (ret_type == kZx) || (ret_type == kXz)) {
            break;
        } else {
            cmd_params.clear(); //2v_80q_400w_300h_zn_df.ahp2|.png, pipe case need clear the params
        }
    }
    return ret_type;
}

bool
imgsrv::ImgDjCmdProc::ConvertDjangoToGM(
        const std::string& task_id,
        std::map<std::string, std::vector<int> >& cmd_params,
        std::string& gm_cmd,
        std::string& suffix) {

    std::stringstream ss;
//    gm_cmd = " +profile \"*\"";
    ss << " +profile \"*\"";
    int width = 0, height = 0;
    int red = 255, green = 255, blue = 255;
    bool auto_crop = false;

    if (cmd_params.find("w") != cmd_params.end()) {
        width = cmd_params["w"][0];
        if (width < 0 || width > 4096) {
            SLOG_E << task_id << " : error : w=" << width;
            return false;
        }
    }
    if (cmd_params.find("h") != cmd_params.end()) {
        height = cmd_params["h"][0];
        if (height < 0 || height > 4096) {
            SLOG_E << task_id << " : " << "error : h=" << height;
            return false;
        }
    }
    if (cmd_params.find("bgc") != cmd_params.end()) {
        std::vector<int>& rgb = cmd_params["bgc"];
        if (rgb.size() != 3) {
            SLOG_E << task_id << " : " << "error bgc_size=" << rgb.size();
            return false;
        }
        red = rgb[0];
        green = rgb[1];
        blue = rgb[2];
    }
    if (cmd_params.find("c") != cmd_params.end())
        auto_crop = (cmd_params["c"][0] == 1);

//  0o表示按原图默认方向，不进行自动旋转
//  1o表示根据图片的旋转参数，对图片进行自动旋转，如果存在缩略参数，是先进行缩略，再进行旋转。
//  2o表示根据图片的旋转参数，对图片进行自动旋转，如果存在缩略参数，先进行旋转，再进行缩略
    if (cmd_params.find("o") != cmd_params.end()){
        //有o时，2o先旋转
        int param = cmd_params["o"][0];
        if (param < 0 || param > 2) {
            SLOG_E << task_id << " : " << "error : o=" << param;
            return false;
        }
        if (param == 2)
            ss << " -auto-orient";
    }
    else{
        //没o时
        if (cmd_params.find("r") == cmd_params.end())
        {
            //没r时，自动旋转
            ss << " -auto-orient";
        }
    }

    std::string width_height;
    if (width != 0) width_height += std::to_string(width);
    width_height += "x";
    if (height != 0) width_height += std::to_string(height);

    if (width != 0 || height != 0) {
//        gm_cmd += " -resize \"" + width_height;
        ss << " -resize \"" << width_height;

        if (cmd_params.find("l") != cmd_params.end()) {
            int param = cmd_params["l"][0];
            if (param != 0 && param != 1) {
                SLOG_E << task_id << " : " << "error : l=" << param;
                return false;
            }
            if (param == 1)
//                gm_cmd += ">";
                ss << ">";
        }
        if (cmd_params.find("e") != cmd_params.end()) {
            int param = cmd_params["e"][0];
            if (param == 0)
//                gm_cmd += "\"";
                ss << "\"";
            else if (param == 1) {
//                gm_cmd += "^\"";
                ss << "^\"";
                if (auto_crop) {
//                    gm_cmd += " -gravity Center -crop " + width_height + "+0+0";
                    ss << " -gravity Center -crop " + width_height + "+0+0";
                }
            } else if (param == 2)
//                gm_cmd += "!\"";
                ss << "!\"";
            else if (param == 4) {
//                gm_cmd += "\" -background \"rgba(" + to_string(red) + "," + to_string(green) + "," + to_string(blue) + ",255)\" -gravity Center -extent " + width_height + "+0+0";
                ss << "\" -background \"rgba(" << red << "," << green << "," << blue
                   << ",255)\" -gravity Center -extent " << width_height << "+0+0";
            } else {
                SLOG_E << task_id << " : " << "error : e=" << param;
                return false;
            }
        } else {
//            gm_cmd += "\"";
            ss << "\"";
        }
    }

    if (cmd_params.find("rc") != cmd_params.end()) {
        int w = cmd_params["rc"][0];
        int h = cmd_params["rc"][1];
        int p = cmd_params["rc"][2];
        if (w < 0 || w > 4096 || h < 0 || h > 4096) {
            SLOG_E << task_id << " : " << "error : rc=" << w <<"x" << h << "-" << p;
            return false;
        }
//        gm_cmd += " -gravity ";
        ss << " -gravity ";
        switch (p) {
            case 1:
                gm_cmd += "NorthWest";
                ss << "NorthWest";
                break;
            case 2:
//                gm_cmd += "North";
                ss << "North";
                break;
            case 3:
                gm_cmd += "NorthEast";
                ss << "NorthEast";
                break;
            case 4:
//                gm_cmd += "West";
                ss << "West";
                break;
            case 5:
//                gm_cmd += "Center";
                ss << "Center";
                break;
            case 6:
//                gm_cmd += "East";
                ss << "East";
                break;
            case 7:
//                gm_cmd += "SouthWest";
                ss << "SouthWest";
                break;
            case 8:
//                gm_cmd += "South";
                ss << "South";
                break;
            case 9:
//                gm_cmd += "SouthEast";
                ss << "SouthEast";
                break;
            default:
                SLOG_E << task_id << " : " << "Unknow rc pos=" << p;
                return false;
                break;
        }
        if (w == 0 && h == 0)
//            gm_cmd += " -crop +0+0";
            ss << " -crop +0+0";
        else if (w == 0)
//            gm_cmd += " -crop x" + to_string(h) + "+0+0";
            ss << " -crop x" << h << "+0+0";
        else if (h == 0)
//            gm_cmd += " -crop " + to_string(w) + "+0+0";
            ss << " -crop " << w << "+0+0";
        else
//            gm_cmd += " -crop " + to_string(w) + "x" + to_string(h) + "+0+0";
            ss << " -crop " << w << "x" << h << "+0+0";
    }
    if (cmd_params.find("a") != cmd_params.end()) {
        std::vector<int>& params = cmd_params["a"];
        if (params.size() != 4) {
            SLOG_E << task_id << " : " << "error : a_size=" << params.size();
            return false;
        }
        int x = params[0];
        int y = params[1];
        int w = params[2];
        int h = params[3];
        if (w == 0 && h == 0)
//            gm_cmd += " -crop +" + to_string(x) + "+" + to_string(y);
            ss << " -crop +" << x << "+" << y;
        else if (w == 0)
//            gm_cmd += " -crop x" + to_string(h) + "+" + to_string(x) + "+" + to_string(y);
            ss << " -crop x" << h << "+" << x << "+" << y;
        else if (h == 0)
//            gm_cmd += " -crop " + to_string(w) + "x+" + to_string(x) + "+" + to_string(y);
            ss << " -crop " << w << "x+" << x << "+" << y;
        else
//            gm_cmd += " -crop " + to_string(w) + "x" + to_string(h)
//                      + "+" + to_string(x) + "+" + to_string(y);
            ss << " -crop " << w << "x" << h << "+" << x << "+" << y;
    }

    for (auto & cmd_param : cmd_params) {
        std::string c = cmd_param.first;
        if (c == "r") {
            int param = cmd_param.second[0];
            if (param < 0 || param > 360) {
//                LOG(ERROR) << task_key_ << " : " << "error : r=" << param;
                return false;
            }
//            gm_cmd += " -rotate " + to_string(param);
            ss << " -rotate " << param;
        } else if (c == "o") {
//            int param = cmd_param.second[0];
//            if (param < 0 || param > 2) {
//                SLOG_E << task_id << " : " << "error : o=" << param;
//                return false;
//            }
//            if (param != 0)
////                gm_cmd += " -auto-orient";
//                ss << " -auto-orient";
        } else if (c == "sh") {
            int param = cmd_param.second[0];
            if (param < 50 || param > 399) {
                SLOG_E << task_id << " : " << "error : sh=" << param;
                return false;
            }
//            gm_cmd += " -sharpen " + to_string(param);
            ss << " -sharpen " << param;
        } else if (c == "pr") {
            int param = cmd_param.second[0];
            if (param != 0 && param != 1) {
                SLOG_E << task_id << " : " << "error : pr=" << param;
                return false;
            }
            if (cmd_param.second[0] == 1) {
//                gm_cmd += " -interlace Line";
                ss << " -interlace Line";
            }
        } else if (c == "q") {
            int param = cmd_param.second[0];
            if (param < 1 || param > 100) {
                SLOG_E << task_id << " : " << "error : q=" << param;
                return false;
            }
//            gm_cmd += " -quality " + to_string(param);
            ss << " -quality " << param;
        } else if (c == "v") {
            int param = cmd_param.second[0];
            if (param < 0 || param > 10000) {
                SLOG_E << task_id << " : " << "warning : version=" << param << ", set to default=0\n";
                param = 0;
            }
            ss << " -zversion " + to_string(param); //zoom version
            SLOG_I << task_id << " : " << " zoom version=" << param;
        } else if (c == "b") {
            int param = cmd_param.second[0];
            if (param < -100 || param > 100) {
                SLOG_E << task_id << " : " << "error : b=" << param;
                return false;
            }
//            gm_cmd += " -modulate " + to_string(param);
            ss << " -modulate " << param;
        } else if (c == "d") {
//            gm_cmd += " -contrast";
            ss << " -contrast";
        } else if (c == "bl") {
            int size = cmd_param.second.size();
            if (size != 2) {
                SLOG_E << task_id << " : " << "error : bl_size=" << size;
                return false;
            }
//            std::string param = " -blur ";
            for (int i = 0; i < size; ++i) {
                int p = cmd_param.second[i];
                if (p < 1 || p > 50) {
                    SLOG_E << task_id << " : " << "error : bl=" << p;
                    return false;
                }
            }
//            gm_cmd += " -blur " + to_string(cmd_param.second[0])
//                      + "x" + to_string(cmd_param.second[1]);
            ss << " -blur " << cmd_param.second[0] << "x" << cmd_param.second[1];
        } else if (c == "p") {
            int param = cmd_param.second[0];
            if (param < 1 || param > 1000) {
                SLOG_E << task_id << " : " << "error : p=" << param;
                return false;
            }
//            gm_cmd += " -resize " + to_string(param) + "%";
            ss << " -resize " << param << "%";
        } else if (c == "wh") {
            int param = cmd_param.second[0];
            if (param < 0 || param > 1) {
                SLOG_E << task_id << " : error wh=" << param;
                return false;
            }
            if (param == 1)
//                gm_cmd += " -flatten";
                ss << " -flatten";
        } else if (c == "col") {
            int param = cmd_param.second[0];
//            gm_cmd += " -define info:bits=" + to_string(param);
            ss << " -define info:bits=" << param;
        } else if (c == "w" || c == "h" || c == "rc" || c == "a"
                   || c == "e" || c == "c" || c == "bgc" || c == "zn"
                   || c == "l" || c == "ci" || c == "zx" || c == "Q"
                   || c == "an" || c == "ow" || c == "oh") {
            SLOG_I << "do nothing. " << c;
        }
    }

    if (cmd_params.find("o") != cmd_params.end()){
        //有o时，1o后旋转
        int param = cmd_params["o"][0];
        if (param < 0 || param > 2) {
            SLOG_E << task_id << " : " << "error : o=" << param;
            return false;
        }
        if (param == 1)
            ss << " -auto-orient";
    }

    if (cmd_params.find("ci") != cmd_params.end()) {
        std::vector<int>& params = cmd_params["ci"];
        if (params.size() != 2) {
            SLOG_E << task_id << " : " << "error : ci_size=" << params.size();
            return false;
        }
        std::string gm_command = GetGmPath();
        std::string tmp_dir = GetTmpPath(task_id);
        suffix = "png";
//        gm_cmd += " " + tmp_dir + "tmp.src && ";
        ss << " " << tmp_dir << "/tmp.src && ";
        int radius = params[0];
        int type = params[1];
        switch (type) {
            case 0:
            case 1: {
                int w = radius << 1;
                int h = radius << 1;
//                gm_cmd += GetGmPath() + " convert -size " + to_string(w) + "x" + to_string(h)
//                          + " xc:none -fill white -draw \"circle " + to_string(radius) + "," + to_string(radius)
//                          + " " + radius + ",0\" " + tmp_dir + "tmp.png && " + gm_command
//                          + " composite -compose copyopacity " + tmp_dir_
//                          + "tmp.png " + tmp_dir + "tmp.src";
                ss << GetGmPath() << " convert -size " << w << "x" << h
                   << " xc:none -fill white -draw \"circle " << radius << "," << radius
                   << " " << radius << ",0\" " << tmp_dir << "/tmp.png && " << gm_command
                   << " composite -compose copyopacity " << tmp_dir
                   << "/tmp.png " << tmp_dir << "/tmp.src";
                break;
            }
            case 2:
//                gm_cmd += gm_command + " convert -size " + width_height
//                          + " xc:none -fill white -draw \"roundRectangle 0,0 " + to_string(width) + ","
//                          + to_string(height) + " " + to_string(radius) + " " + to_string(radius)
//                          + "\" " + tmp_dir + "tmp.png && " + gm_command + " composite -compose copyopacity " + tmp_dir
//                          + "tmp.png " + tmp_dir + "tmp.src";
                ss << gm_command << " convert -size " << width_height
                   << " xc:none -fill white -draw \"roundRectangle 0,0 " << width << ","
                   << height << " " << radius << " " << radius
                   << "\" " << tmp_dir << "/tmp.png && " << gm_command << " composite -compose copyopacity "
                   << tmp_dir << "/tmp.png " << tmp_dir << "/tmp.src";
                break;
            default:
                SLOG_E << task_id << " : " << "Unknow ci type : " << type;
                return false;
        }
    } else {
        if (cmd_params.find("df") != cmd_params.end() && cmd_params.find("sf") != cmd_params.end())
        {
//            gm_cmd += " -df sf." + suffix;
            ss << " -df sf." << suffix;
        }
        else if (cmd_params.find("df") != cmd_params.end() && cmd_params.find("mf") != cmd_params.end())
        {
//            gm_cmd += " -df mf." + suffix;
            ss << " -df mf." << suffix;
        }
        else if (cmd_params.find("df") != cmd_params.end())
        {
//            gm_cmd += " -df ." + suffix;
            ss << " -df ." << suffix;
        }

        if ((cmd_params.find("Os") != cmd_params.end()) || (cmd_params.find("os") != cmd_params.end())) {
            //TODO: MmppWorker::env_config_path
//            gm_cmd += " -optimize -bin_path " /*+ MmppWorker::env_config_path*/;
            //path for gifsicle and pngquant
            ss << " -optimize -bin_path " << bin_path_;
        }
    }
    gm_cmd = ss.str();
    return true;
}

std::string imgsrv::ImgDjCmdProc::GetGmPath() {
    return  bin_path_ + "/gm";
}

std::string imgsrv::ImgDjCmdProc::GetSmartCropBinPath() {
    return bin_path_ + "/FalconIntelligentCut.bin";
}

std::string imgsrv::ImgDjCmdProc::GetTmpPath(const std::string& task_id) {
    std::string tmp_path = output_path_ + "/" + task_id;
    return tmp_path;
}

bool imgsrv::ImgDjCmdProc::GmCommand(
        const std::string& input_path,
//        const std::string& task_id,
        TaskDataPtr& task_data_ptr,
        const std::vector<std::string>& gm_cmds,
        std::string& suffix_) {
    std::stringstream ss;
    std::string tmp_path = GetTmpPath(task_data_ptr->GetTaskId());
    if (access(tmp_path.c_str(), F_OK) != 0) {
        Util::MkDir(tmp_path + "/");
    }
    std::string tmp_input = tmp_path + "/0.src";
    bool rst = Util::CpFile(input_path, tmp_input);
    if (!rst) {
        SLOG_E << task_data_ptr->GetTaskId() << ", copy file failed: "
               << input_path << "->" << tmp_input;
        return false;
    }

    std::string opt;
    switch (GET_CONF.gm_zoom_type) {
        case 0:
            opt = " ";
            break;
        case 1:
            opt = " -define jpeg:jpegturbo ";
            break;
        case 2:
            opt = " -define jpeg:jpegturbo -define jpeg:autoq ";
            break;
        default:
            break;
    }

    int i = 0;
    std::string tmp_suffix= ".src";

    Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
    stat_info.call_start_time_ = Util::GetTimeStamp();
    for (; i < (int)gm_cmds.size(); ++i) {
        std::string suffix = "src";
        if (i == (int)gm_cmds.size() - 1) suffix = suffix_;
        auto command = gm_cmds[i];
        if((i > 0) && (gm_cmds[i - 1].find("df") != std::string::npos))
        {
            tmp_suffix = Util::GetSuffix(GetTmpPath(task_data_ptr->GetTaskId()), std::to_string(i));
            if("" != tmp_suffix)
            {
                tmp_suffix = "." + tmp_suffix + " ";
            }
            else
            {
                tmp_suffix = tmp_suffix + " ";
            }
        }

        if(command.find("df") != std::string::npos ) {
//            sys_cmd = gm_command + " " + command.command() + " " + tmp_dir_
//                      + to_string(i) + tmp_suffix + command.opt() + opt
//                      + " -define image:relative_qp -define hevc:numberOfThreads="
//                      + to_string(MmppMutableConfig::hevc_threads()) + " "
//                      + tmp_dir_ + to_string(i + 1);
            ss << GetGmPath() << " convert "
               << (GET_CONF.gmstat_path.empty() ? "" : ("-gm_stat_path " + GET_CONF.gmstat_path + " "))
               << GetTmpPath(task_data_ptr->GetTaskId())
               << "/" << i << tmp_suffix << command << opt
               << " -define image:relative_qp -define hevc:numberOfThreads="
               << hevc_thread_num_ << " " << GetTmpPath(task_data_ptr->GetTaskId())
               << "/" << (i + 1);

        } else {
//            sys_cmd = gm_command + " " + command.command() + " " + tmp_dir_
//                      + to_string(i) + tmp_suffix + " " + command.opt() + opt
//                      + " -define image:relative_qp -define hevc:numberOfThreads="
//                      + to_string(MmppMutableConfig::hevc_threads()) + " "
//                      + tmp_dir_ + to_string(i + 1) + "." + suffix;
            ss << GetGmPath() << " convert "
               << (GET_CONF.gmstat_path.empty() ? "" : ("-gm_stat_path " + GET_CONF.gmstat_path + " "))
               << GetTmpPath(task_data_ptr->GetTaskId()) << "/"
               << i << tmp_suffix << " " << command << opt
               << " -define image:relative_qp -define hevc:numberOfThreads="
               << hevc_thread_num_ << " " << GetTmpPath(task_data_ptr->GetTaskId())
               << "/" << (i + 1) << "." << suffix;
        }

//        if (!GET_CONF.ce_log_path.empty())
//        {
//            ss << " 2>>" << GET_CONF.ce_log_path;
//        }

        std::string sys_cmd = ss.str();
        SLOG_I << task_data_ptr->GetTaskId() << " : " << sys_cmd;
        int ret_code;
        if (!CE.RunCommand(sys_cmd, task_data_ptr->GetTaskId(), task_data_ptr->GetTimeOut(), ret_code)) {
            SLOG_E << task_data_ptr->GetTaskId() << " : system command failed. " << sys_cmd
                       << "; errno=" << errno << " " <<  strerror(errno) << ", exit code: " << ret_code;
            return false;
        }
        ss.str("");
    }
    stat_info.call_end_time_ = Util::GetTimeStamp();
    stat_info.call_total_time_ = stat_info.call_end_time_- stat_info.call_start_time_;

    suffix_ = Util::GetSuffix(GetTmpPath(task_data_ptr->GetTaskId()), std::to_string(i));
    std::string output = GetTmpPath(task_data_ptr->GetTaskId()) + "/"
            + std::to_string(i) + "." + suffix_;
    if (task_data_ptr->GetDoCrypt() == kEncryptResult
        || task_data_ptr->GetDoCrypt() == kDoBoth) {

        bool rst = Util::Aes256CbcEncryptFile(
                task_data_ptr->GetCipherKey(),
                output,
                output);
        if (!rst) {
            SLOG_E << task_data_ptr->GetTaskId() << " : decrypt file error.";
            return false;
        }
    }
    task_data_ptr->SetOutputFile(output);
    task_data_ptr->SetTargetSuffix(suffix_);
//    string object_key = app_id_ + "/" + task_key_ + "." + suffix_;
//    if (blob_->output().has_cipher()) {
//        if (!doEncryptFromFile(output, object_key))
//            return MessageTaskPkt::ERROR_PROCESS;
//    } else {
//        if (!oss_ptr_->putObjectFromFile(object_key, output)) {
//            LOG(ERROR) << task_key_ << " : put object from file failed. path="
//                       << output;
//            return MessageTaskPkt::ERROR_OSS;
//        }
//    }
    return true;
}

bool imgsrv::ImgDjCmdProc::ZnCommand(
        const std::string& input_path,
        TaskDataPtr& task_data_ptr,
        std::map<std::string, std::vector<int> >& cmd_params,
        std::string& suffix) {

    easyexif::EXIFInfo input_exif;
    unsigned short ori = 1;
    if (GetExif(input_path, input_exif)) {
        ori = input_exif.Orientation;
    }

    int w = 0,h = 0,q = -1;
    if (cmd_params.find("zn") != cmd_params.end() || cmd_params.find("zx") != cmd_params.end()) {
        if (cmd_params.find("w") != cmd_params.end()) {
            w = cmd_params["w"][0];
        }
        if (cmd_params.find("h") != cmd_params.end()) {
            h = cmd_params["h"][0];
        }
        if (cmd_params.find("q") != cmd_params.end()) {
            q = cmd_params["q"][0];
        } else {
            q = 90;
        }
    }

    if ((w < 1 || w > 4096)
        || (h < 1 || h > 4096)
        || (q < 1 || q > 100)) {
        SLOG_E << task_data_ptr->GetTaskId() << " : smart crop parameter is error; width="
                   << w << " height=" << h << " quality=" << q;
        return false;
    }

    std::string output = GetTmpPath(task_data_ptr->GetTaskId()) + "/zn.jpg";
    std::stringstream ss;
    ss << GetSmartCropBinPath() << " " << input_path << " " << output
       << " " << w << " " << h << " " << " 300 " << ori << " " << q;

    std::string sys_cmd = ss.str();
    SLOG_I << task_data_ptr->GetTaskId() << " : " << sys_cmd;

    Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
    stat_info.call_start_time_ = Util::GetTimeStamp();
    int ret_code;
    if (!CE.RunCommand(sys_cmd, task_data_ptr->GetTaskId(), task_data_ptr->GetTimeOut(), ret_code)) {
        SLOG_E << task_data_ptr->GetTaskId() << " : system command failed. " << sys_cmd
               << "; errno=" << errno << " " <<  strerror(errno) << ", exit code: " << ret_code;
        return false;
    }
    stat_info.call_end_time_ = Util::GetTimeStamp();
    stat_info.call_total_time_ = stat_info.call_end_time_- stat_info.call_start_time_;

    if (access(output.c_str(), 0)) {
        SLOG_W << task_data_ptr->GetTaskId() << " : no output. path=" << output;
        return false;
    }

    SLOG_E << task_data_ptr->GetTaskId() << " : zn with suffix: " << suffix;
    if (suffix != "jpg" && suffix != "src") {
        std::vector<std::string> gm_cmds;
        if (cmd_params.find("df") != cmd_params.end())
            gm_cmds.push_back(" -df _df." + suffix);//-df _df.webp.png
        else
            gm_cmds.push_back("");
        
        return GmCommand(output, task_data_ptr, gm_cmds, suffix);
    }

    if (task_data_ptr->GetDoCrypt() == kEncryptResult
        || task_data_ptr->GetDoCrypt() == kDoBoth) {

        bool rst = Util::Aes256CbcEncryptFile(
                task_data_ptr->GetCipherKey(),
                output,
                output);
        if (!rst) {
            SLOG_E << task_data_ptr->GetTaskId() << " : decrypt file error.";
            return false;
        }
    }
    task_data_ptr->SetOutputFile(output);
    task_data_ptr->SetTargetSuffix("jpg");
    return true;
}

bool imgsrv::ImgDjCmdProc::XzCommand(const std::string& input_path, TaskDataPtr& task_data_ptr,
                                     std::map<std::string, std::vector<int> >& cmd_params,
                                     std::string& suffix) {
    char* ret = NULL;
    int ret_length = 0;
    int w = 0,h = 0,q = -1;
    if (cmd_params.find("xz") != cmd_params.end() || cmd_params.find("zx") != cmd_params.end()) {
        if (cmd_params.find("w") != cmd_params.end()) {
            w = cmd_params["w"][0];
        }
        if (cmd_params.find("h") != cmd_params.end()) {
            h = cmd_params["h"][0];
        }
        if (cmd_params.find("q") != cmd_params.end()) {
            q = cmd_params["q"][0];
        } else {
            q = 90;
        }
    }

    if ((w < 1 || w > 4096)
        || (h < 1 || h > 4096)
        || (q < 1 || q > 100)) {
        SLOG_E << task_data_ptr->GetTaskId() << " : saliency crop parameter is error; width="
                   << w << " height=" << h << " quality=" << q;
        return false;
    }

    std::ifstream is(input_path, std::ios::in | std::ios::binary);
    if (!is) {
        return false;
    }
    is.seekg (0, is.end);
    int length = is.tellg();
    is.seekg (0, is.beg);
    std::vector<char> buffer;
    buffer.resize(length);
    is.read(&buffer[0], length);
    is.close();

    SLOG_I << task_data_ptr->GetTaskId() << " : cutBySal " << input_path << " xz.jpg " << w << " " << h << " format:" << suffix;

    try {
        //TODO: reserve the fetch_connction's buffer as a whole
        Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
        stat_info.call_start_time_ = Util::GetTimeStamp();
        ret = cutBySal((char*)&buffer[0],
                       buffer.size(),
                       w,
                       h,
                       300, 0.02, 0.8, LOCATE_REGION_GLOBAL,
                       q,
                       ret_length);
        stat_info.call_end_time_ = Util::GetTimeStamp();
        stat_info.call_total_time_ = stat_info.call_end_time_- stat_info.call_start_time_;
    } catch (...) {
        SLOG_E << task_data_ptr->GetTaskId() << " : SaliencyCut assert.";
        return false;
    }
    if (ret == NULL || ret_length == 0) {
        SLOG_W << task_data_ptr->GetTaskId() << " : SaliencyCut failed.";
        return false;
    }

    SLOG_E << task_data_ptr->GetTaskId() << " : xz with suffix: " << suffix;
    std::string tmp_file = GetTmpPath(task_data_ptr->GetTaskId()) + "/xz.jpg";
    if (suffix != "jpg" && suffix != "src") {
        std::ofstream outfile(tmp_file.c_str(), std::ofstream::binary);
        outfile.write(ret, ret_length);
        outfile.close();

        std::vector<std::string> gm_cmds;
        if (cmd_params.find("df") != cmd_params.end())
            gm_cmds.push_back(" -df _df." + suffix);
        else
            gm_cmds.push_back("");
        if (ret) {
            free(ret);
        }
        return GmCommand(tmp_file, task_data_ptr, gm_cmds, suffix);
    }

    if (task_data_ptr->GetDoCrypt() == kEncryptResult
        || task_data_ptr->GetDoCrypt() == kDoBoth) {

        std::string iv(16, 0);
        vector<unsigned char> in(ret, ret+ret_length);
        std::vector<char> out;
        bool rst = Util::Aes256CbcEncrypt(
                task_data_ptr->GetCipherKey(),
                iv,
                in,
                out);
        if (!rst) {
            if (ret) {
                free(ret);
            }
            SLOG_E << task_data_ptr->GetTaskId() << " : decrypt file error.";
            return false;
        }

        std::ofstream outfile(tmp_file.c_str(), std::ofstream::binary);
        outfile.write(&out[0], out.size());
        outfile.close();

    } else {
        std::ofstream outfile(tmp_file.c_str(), std::ofstream::binary);
        outfile.write(ret, ret_length);
        outfile.close();
    }
    task_data_ptr->SetOutputFile(tmp_file);
    task_data_ptr->SetTargetSuffix("jpg");
    if (ret) {
        free(ret);
    }
    return true;
}

bool imgsrv::ImgDjCmdProc::ZxCommand(const std::string& input_path, TaskDataPtr& task_data_ptr,
                                     std::map<std::string, std::vector<int> >& cmd_params,
                                     std::string& suffix) {

    if (ZnCommand(input_path, task_data_ptr, cmd_params, suffix)) {
        return true;
    } else {
        return XzCommand(input_path, task_data_ptr, cmd_params, suffix);
    }
}

bool imgsrv::ImgDjCmdProc::GetExif(const std::string& input_path, easyexif::EXIFInfo& input_exif) {
    std::ifstream is(input_path, std::ios::in | std::ios::binary);
    if (!is) {
        return false;
    }
    is.seekg (0, is.end);
    int length = is.tellg();
    is.seekg (0, is.beg);
    std::vector<char> buffer;
    buffer.resize(length);
    is.read(&buffer[0], length);
    is.close();

    try {
        if (PARSE_EXIF_SUCCESS
            != input_exif.parseFrom((unsigned char*)(&buffer[0]), buffer.size())) {
            SLOG_W << "get exif info failed.";
            return false;
        }
    } catch (...) {
        SLOG_E << "get exif crash catch.";
        return false;
    }
    return true;
}

bool imgsrv::ImgDjCmdProc::DoParam(const std::string& task_id, TaskDataPtr& task_data_ptr,
        const std::string& Param) {
//    ImgProcTaskPtr img_proc_task_ptr =
//            boost::dynamic_pointer_cast<ImgProcTask>(task_data_ptr);
    bool rst = false;
    std::map<std::string, std::vector<int> > cmd_params;
    std::string suffix;
    std::vector<std::string> gm_cmds;
    CmdType cmd_type = ParseDjCmd(task_id, Param, cmd_params, suffix, gm_cmds);
    if (cmd_type == kErrCmd) {
        SLOG_E << task_id << " : " << "django command parse failed. "
               << Param;
        return false;
    }

    std::string input_path = task_data_ptr->GetInputFile();
    switch (cmd_type) {
        case kGm:
            rst = GmCommand(input_path, task_data_ptr, gm_cmds, suffix);
            break;
        case kZn:
            rst = ZnCommand(input_path, task_data_ptr, cmd_params, suffix);
            break;
        case kZx:
            rst = ZxCommand(input_path, task_data_ptr, cmd_params, suffix);
            break;
        case kXz:
            rst = XzCommand(input_path, task_data_ptr, cmd_params, suffix);
            break;
        default:
            SLOG_E << task_id <<" : unknow CONVERT Command type. " << cmd_type;
            rst = false;
    }

    if (!rst) {
        SLOG_E << task_id << ": " << "django command execution failed. " << Param;
    }
    return rst;
}

bool imgsrv::ImgDjCmdProc::DoSelfAvifParam(const std::string& task_id, TaskDataPtr& task_data_ptr,
                                   const std::string& Param) {
//    ImgProcTaskPtr img_proc_task_ptr =
//            boost::dynamic_pointer_cast<ImgProcTask>(task_data_ptr);

    std::string tmp_path = GetTmpPath(task_data_ptr->GetTaskId());
    std::string input_path = task_data_ptr->GetInputFile();
    if (access(tmp_path.c_str(), F_OK) != 0) {
        Util::MkDir(tmp_path + "/");
    }
    std::string tmp_input = tmp_path + "/0.src";
    bool rst = Util::CpFile(input_path, tmp_input);
    if (!rst) {
        SLOG_E << task_data_ptr->GetTaskId() << ", copy file failed: "
               << input_path << "->" << tmp_input;
        return false;
    }
    std::string output = tmp_path + "/result.avif";
    std::string logpath = "/home/<USER>/logs/imgsrv/self_avif/"+ getCurrentDate() + "/" + task_id + "_result.log";
    std::stringstream ss;
    std::string pyPath = third_path_ + "/self_avif/transcode_avif.py";
    ss << "cd "<<third_path_<< "/self_avif &&";
    ImgProcTaskPtr img_proc_task_ptr =
            boost::dynamic_pointer_cast<ImgProcTask>(task_data_ptr);
    ss << " sudo python3 " <<pyPath <<" "<< input_path << " " << output << " " << logpath <<" " << Param <<" \""<<img_proc_task_ptr->getAlgParam()<<"\" 0";
    std::string sys_cmd = ss.str();
    SLOG_I << task_data_ptr->GetTaskId() << " : " << sys_cmd;

    Statistics& stat_info = task_data_ptr->GetServerConnectionPtr()->GetStatInfo();
    stat_info.call_start_time_ = Util::GetTimeStamp();
    int ret_code;
    if (!CE.RunCommand(sys_cmd, task_data_ptr->GetTaskId(), task_data_ptr->GetTimeOut(), ret_code)) {
        SLOG_E << task_data_ptr->GetTaskId() << " : system command failed. " << sys_cmd
               << "; errno=" << errno << " " <<  strerror(errno) << ", exit code: " << ret_code;
        return false;
    }
    task_data_ptr->SetOutputFile(output);
    task_data_ptr->SetTargetSuffix(".avif");
    ss.str("");
    stat_info.call_end_time_ = Util::GetTimeStamp();
    stat_info.call_total_time_ = stat_info.call_end_time_- stat_info.call_start_time_;
    return true;
}


