/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : worker.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-12
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_WORKER_H
#define IMGSRV_WORKER_H

//#include <boost/asio.hpp>
#include <boost/serialization/singleton.hpp>
#include <boost/thread/thread.hpp>
#include <boost/asio/io_service.hpp>
#include "connection.h"

using namespace boost::asio;

namespace imgsrv {
class Worker : public boost::serialization::singleton<Worker>{
public:
    Worker();

    bool Init(size_t thread_num);

    bool Run();

    bool Stop();

    void Join();

    bool ProcessNew();

    io_service &GetIoService();

    bool SendReplyHeader(ServerConnectionPtr& serv_conn, size_t data_size, const std::string& cont_type);

    bool SendReplyBody(ServerConnectionPtr serv_conn, const std::string& task_id);

private:
    void EndProcess(const std::string& task_id);
private:
    bool inited_;
    io_service ios_;
//    deadline_timer timer_;
    size_t thread_num_;
    std::vector<boost::shared_ptr<boost::thread> > threads_;
    io_service::work dummy_work_;
};
}

#define WORKER imgsrv::Worker::get_mutable_instance()
#endif //IMGSRV_WORKER_H
