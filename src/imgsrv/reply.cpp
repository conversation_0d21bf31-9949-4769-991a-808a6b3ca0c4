/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : reply.cpp
 * <AUTHOR> we<PERSON><PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include "reply.h"

static const std::string continue_ = "HTTP/1.0 100 Continue\r\n";
static const std::string ok = "HTTP/1.0 200 OK\r\n";
static const std::string created = "HTTP/1.0 201 Created\r\n";
static const std::string accepted = "HTTP/1.0 202 Accepted\r\n";
static const std::string no_content = "HTTP/1.0 204 No Content\r\n";
static const std::string partial_content = "HTTP/1.0 206 Partial Content\r\n";
static const std::string multiple_choices = "HTTP/1.0 300 Multiple Choices\r\n";
static const std::string moved_permanently = "HTTP/1.0 301 Moved Permanently\r\n";
static const std::string moved_temporarily = "HTTP/1.0 302 Moved Temporarily\r\n";
static const std::string not_modified = "HTTP/1.0 304 Not Modified\r\n";
static const std::string bad_request = "HTTP/1.0 400 Bad Request\r\n";
static const std::string unauthorized = "HTTP/1.0 401 Unauthorized\r\n";
static const std::string forbidden = "HTTP/1.0 403 Forbidden\r\n";
static const std::string not_found = "HTTP/1.0 404 Not Found\r\n";
static const std::string gone = "HTTP/1.0 410 Gone\r\n";
static const std::string payload_too_large = "HTTP/1.0 413 Payload Too Large\r\n";
static const std::string range_error = "HTTP/1.0 416 Requested Range Not Satisfiable\r\n";
static const std::string process_timeout = "HTTP/1.0 480 Process Time out\r\n";
static const std::string internal_server_error = "HTTP/1.0 500 Internal Server Error\r\n";
static const std::string not_implemented = "HTTP/1.0 501 Not Implemented\r\n";
static const std::string bad_gateway = "HTTP/1.0 502 Bad Gateway\r\n";
static const std::string service_unavailable = "HTTP/1.0 503 Service Unavailable\r\n";
static const std::string gateway_timeout = "HTTP/1.0 504 Gateway Timeout\r\n";

imgsrv::Reply::Reply() {
    Reset();
}

void imgsrv::Reply::Reset() {
    status_ = kDumbCode;
    headers_.clear();
    stock_content_ = "";
    content_length_ = 0;
    cur_length_ = 0;
    rp_phase = rp_start;
}

std::string imgsrv::Reply::GetRspStatusLine(StatusType status) {
    switch (status) {
        case Reply::kContinue:
            return continue_;
        case Reply::kOk:
            return ok;
        case Reply::kCreated:
            return created;
        case Reply::kAccepted:
            return accepted;
        case Reply::kNoContent:
            return no_content;
        case Reply::kPartialContent:
            return partial_content;
        case Reply::kMultipleChoices:
            return multiple_choices;
        case Reply::kMovedPermanently:
            return moved_permanently;
        case Reply::kMovedTemporarily:
            return moved_temporarily;
        case Reply::kNotModified:
            return not_modified;
        case Reply::kBadRequest:
            return bad_request;
        case Reply::kUnauthorized:
            return unauthorized;
        case Reply::kForbidden:
            return forbidden;
        case Reply::kNotFound:
            return not_found;
        case Reply::kGone:
            return gone;
        case Reply::kPayloadTooLarge:
            return payload_too_large;
        case Reply::kRangeError:
            return range_error;
        case Reply::kProcessTimeout:
            return process_timeout;
        case Reply::kInternalServerError:
            return internal_server_error;
        case Reply::kNotImplemented:
            return not_implemented;
        case Reply::kBadGateway:
            return bad_gateway;
        case Reply::kServiceUnavailable:
            return service_unavailable;
        case Reply::kGatewayTimeout:
            return gateway_timeout;
        default:
            return internal_server_error;
    }
}

int imgsrv::Reply::StockReply(imgsrv::Reply::StatusType st, char* out_buff, size_t out_buff_size) {
    if (out_buff == nullptr) {
        return -1;
    }

    status_ = st;
    stock_content_ = GetStockContent(status_);
    /*headers_.resize(4);
    headers_[0].name = "Server";
    headers_[0].value = "imgsrv";
    headers_[1].name = "Content-Length";
    headers_[1].value = std::to_string(stock_content_.size());
    headers_[2].name = "Content-Type";
    headers_[2].value = "text/html";
    headers_[3].name = "Connection";
    headers_[3].value = "close";*/
    headers_.clear();
    headers_.emplace_back(Header());
    headers_.back().name = "Server";
    headers_.back().value = "imgsrv";
    headers_.emplace_back(Header());
    headers_.back().name = "Content-Length";
    headers_.back().value = std::to_string(stock_content_.size());
    headers_.emplace_back(Header());
    headers_.back().name = "Content-Type";
    headers_.back().value = "text/html";
    headers_.emplace_back(Header());
    headers_.back().name = "Connection";
    headers_.back().value = "close";

    int index = 0;
    index += snprintf(out_buff + index, out_buff_size - index, "%s", GetRspStatusLine(status_)
    .c_str());
    for (std::size_t i = 0; i < headers_.size(); ++i) {
        Header& h = headers_[i];
        index += snprintf(out_buff + index, out_buff_size - index, "%s: ", h.name.c_str());
        index += snprintf(out_buff + index, out_buff_size - index, "%s\r\n", h.value.c_str());
    }
    index += snprintf(out_buff + index, out_buff_size - index, "\r\n");
    index += snprintf(out_buff + index, out_buff_size - index, "%s", stock_content_.c_str());

    return index;
}

std::string imgsrv::Reply::GetStockContent(imgsrv::Reply::StatusType status) {
    switch (status) {
        case kContinue:
            return continue_;
        case kOk:
            return ok;
        case kCreated:
            return created;
        case kAccepted:
            return accepted;
        case kNoContent:
            return no_content;
        case kPartialContent:
            return partial_content;
        case kMultipleChoices:
            return multiple_choices;
        case kMovedPermanently:
            return moved_permanently;
        case kMovedTemporarily:
            return moved_temporarily;
        case kNotModified:
            return not_modified;
        case kBadRequest:
            return bad_request;
        case kUnauthorized:
            return unauthorized;
        case kForbidden:
            return forbidden;
        case kNotFound:
            return not_found;
        case kGone:
            return gone;
        case kPayloadTooLarge:
            return payload_too_large;
        case kRangeError:
            return range_error;
        case kProcessTimeout:
            return process_timeout;
        case kInternalServerError:
            return internal_server_error;
        case kNotImplemented:
            return not_implemented;
        case kBadGateway:
            return bad_gateway;
        case kServiceUnavailable:
            return service_unavailable;
        case kGatewayTimeout:
            return gateway_timeout;
        default:
            return internal_server_error;
    }
}

size_t imgsrv::Reply::GetContentLength() const {
    return content_length_;
}

void imgsrv::Reply::SetContentLength(size_t content_length) {
    content_length_ = content_length;
}

size_t imgsrv::Reply::GetCurLength() const {
    return cur_length_;
}

void imgsrv::Reply::SetCurLength(size_t cur_length) {
    cur_length_ = cur_length;
}

imgsrv::Reply::StatusType imgsrv::Reply::GetStatus() const {
    return status_;
}

void imgsrv::Reply::SetStatus(imgsrv::Reply::StatusType status) {
    status_ = status;
}

std::vector<imgsrv::Header>& imgsrv::Reply::GetHeaders() {
    return headers_;
}

int imgsrv::Reply::GenHeaderToBuffer(char* data, size_t size) {
    if (data == NULL) {
        return -1;
    }
    int index = 0;
    index += snprintf(data + index, size - index, "%s", GetRspStatusLine(status_).c_str());
    for (std::size_t i = 0; i < headers_.size(); ++i) {
        Header& h = headers_[i];
        index += snprintf(data + index, size - index, "%s: ", h.name.c_str());
        index += snprintf(data + index, size - index, "%s\r\n", h.value.c_str());
    }
    index += snprintf(data + index, size - index, "\r\n");
    return index;
}

imgsrv::Response::Response() {

}

int imgsrv::Response::GetStatusCode() const {
    return status_code_;
}

void imgsrv::Response::SetStatusCode(int status_code) {
    status_code_ = status_code;
}

std::map<std::string, imgsrv::Header>& imgsrv::Response::GetHeaders() {
    return headers_;
}

void imgsrv::Response::SetHeaders(const std::map<std::string, Header>& headers) {
    headers_ = headers;
}

size_t imgsrv::Response::GetContentLength() const {
    return content_length_;
}

void imgsrv::Response::SetContentLength(size_t content_length) {
    content_length_ = content_length;
}

size_t imgsrv::Response::GetCurLength() const {
    return cur_length_;
}

void imgsrv::Response::SetCurLength(size_t cur_length) {
    cur_length_ = cur_length;
}
