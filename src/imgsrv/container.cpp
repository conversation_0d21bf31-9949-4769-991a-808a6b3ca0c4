/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : task_queue.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include <boost/asio/placeholders.hpp>
#include "container.h"
#include "log_adapter.h"

imgsrv::Container::Container() {
    process_queue_.clear();
    task_pool_.clear();
}

bool imgsrv::Container::PoolInsert(TaskDataPtr data_ptr) {
    WLock w_lock(p_mutex_);
    task_pool_[data_ptr->GetTaskId()] = std::move(data_ptr);
    return true;
}

bool imgsrv::Container::PoolErase(const std::string& task_id) {
    WLock w_lock(p_mutex_);
    task_pool_.erase(task_id);
    return true;
}

TaskDataPtr imgsrv::Container::PoolFind(const std::string& task_id) {
    RLock r_lock(p_mutex_);
    auto it = task_pool_.find(task_id);
    if (it != task_pool_.end()) {
        return it->second;
    }
    return TaskDataPtr();
}

bool imgsrv::Container::QueuePushBack(imgsrv::TaskId&& id) {
    SequenceQ& sequence = BySequence();
    WLock w_lock(q_mutex_);
    sequence.push_back(std::move(id));
    return true;
}

bool imgsrv::Container::QueuePushBack(imgsrv::TaskId& id) {
    SequenceQ& sequence = BySequence();
    WLock w_lock(q_mutex_);
    sequence.push_back(id);
    return true;
}

bool imgsrv::Container::QueuePopFront(imgsrv::TaskId& id) {
    WLock w_lock(q_mutex_);
    if (!process_queue_.empty()) {
        SequenceQ& sequence = BySequence();
        id = std::move(sequence.front());
        sequence.pop_front();
    } else {
        return false;
    }
    return true;
}

bool imgsrv::Container::QueueErase(const std::string& task_id) {
    WLock w_lock(q_mutex_);
    process_queue_.erase(task_id);
    return true;
}

size_t imgsrv::Container::QueueSize() {
    RLock r_lock(q_mutex_);
    return process_queue_.size();
}

bool imgsrv::Container::QueueEmpty() {
    RLock r_lock(q_mutex_);
    return process_queue_.empty();
}

bool imgsrv::Container::CancelTask(const std::string& task_id) {
    PoolErase(task_id);
    QueueErase(task_id);
    return true;
}

imgsrv::SequenceQ& imgsrv::Container::BySequence() {
    return process_queue_.get<1>();
}

/*
void imgsrv::Container::HandleTimeOut(const error_code &e, std::string task_id) {
    WLock w_lock(mutex_);
    auto it = process_queue_.find("task_id");
    if (it != process_queue_.end()) {
        SLOG_I << "Task: " << task_id << " times out after " << it->GetTimeOut() << " ms.";
    }
    process_queue_.erase(task_id);
}*/
