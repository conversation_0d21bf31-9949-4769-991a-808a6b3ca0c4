/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : task_queue.h
 * <AUTHOR> we<PERSON><PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_CONTAINER_H
#define IMGSRV_CONTAINER_H

#include <boost/serialization/singleton.hpp>
#include <boost/multi_index_container.hpp>
#include <boost/multi_index/ordered_index.hpp>
#include <boost/multi_index/sequenced_index.hpp>
#include <boost/multi_index/member.hpp>
#include <boost/asio/io_service.hpp>
#include <boost/asio/deadline_timer.hpp>
#include <boost/thread/thread.hpp>
#include <unordered_map>
#include "task_data.h"
#include "util.h"

//using namespace boost::system;
namespace bmi = boost::multi_index;

namespace imgsrv {

typedef bmi::multi_index_container<
        TaskId, bmi::indexed_by<
                        bmi::ordered_unique<
                            bmi::member<TaskId, std::string, &TaskId::id_> >,
                        bmi::sequenced<>
                  >
        > MultiIndicesQueue;
typedef MultiIndicesQueue::nth_index<1>::type SequenceQ;

class Container : public boost::serialization::singleton<Container>{
public:
    Container();

    bool PoolInsert(TaskDataPtr data_ptr);

    bool PoolErase(const std::string& task_id);

    TaskDataPtr PoolFind(const std::string& task_id);

    bool QueuePushBack(TaskId&& id);
    bool QueuePushBack(TaskId& id);
    bool QueuePopFront(TaskId& id);
    bool QueueErase(const std::string& task_id);
    size_t QueueSize();
    bool QueueEmpty();

    bool CancelTask(const std::string& task_id);

private:
    SequenceQ& BySequence();
//    void HandleTimeOut(const error_code &e, std::string task_id);

private:
    WrMutex q_mutex_;
    WrMutex p_mutex_;
    MultiIndicesQueue process_queue_;
    std::unordered_map<std::string, TaskDataPtr> task_pool_;
};
}

#define CONTAINER Container::get_mutable_instance()
#endif //IMGSRV_CONTAINER_H
