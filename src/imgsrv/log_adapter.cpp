/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : log_adapter.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-07-31
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/

#include "log_adapter.h"

sink_id g_sk_id;
sink_id g_stat_id;
sink_id g_ce_id;
bool LOG_INIT(const std::string& log_path, split_interval interval, log_level level) {
    if (!log_init()) {
        return false;
    }
    ::util::log::sinker sk;
    sk.log_file = log_path;
    sk.interval = interval;
    sk.level = level;
    return log_add_sinker(sk, g_sk_id);
}

bool STAT_INIT(const std::string& stat_path, split_interval interval) {
    if (!log_init()) {
        return false;
    }
    ::util::log::sinker sk;
    sk.log_file = stat_path;
    sk.interval = interval;
    sk.level = NO_LEVEL;
    return log_add_sinker(sk, g_stat_id);
}

bool CE_LOG_INIT(const std::string& ce_log_path, split_interval interval, log_level level) {
    if (!log_init()) {
        return false;
    }
    ::util::log::sinker sk;
    sk.log_file = ce_log_path;
    sk.interval = interval;
    sk.level = level;
    return log_add_sinker(sk, g_ce_id);
}

int LOG_CLOSE() {
    return log_close();
}

int STAT_CLOSE() {
    return log_close();
}

int CE_LOG_CLOSE() {
    return log_close();
}

