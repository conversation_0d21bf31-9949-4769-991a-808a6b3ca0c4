/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : command_executor.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019/8/23
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_COMMAND_EXECUTOR_H
#define IMGSRV_COMMAND_EXECUTOR_H


#include <boost/serialization/singleton.hpp>
#include <util.h>

namespace imgsrv {

enum PidStatus {
    kPidWait = 0,
    kPidRunning,
    kPidExit
};

struct PidNode {
    pid_t pid;
    int p2c_pipe[2];
    int c2p_pipe[2];
    PidStatus status;
};

const int kPipeBufSize = 4096;

class CommandExecutor : public boost::serialization::singleton<CommandExecutor>{
public:
    CommandExecutor();
//    ~CommandExecutor();
    bool Init(
            int process_num,
            const std::string& tool_dir,
            const std::string& process_name);
    bool Stop();
    bool DeleteChild(pid_t pid);
//    static void SigchldHandler(int sig);

    bool CreateChildProcess(const std::string& process_name);

    int GetTotalProcessNum();

    int GetWorkingProcessNum();

    bool RunCommand(const std::string& cmd, const std::string& task_id, int wait_time, int& ret_code);

private:
    std::string ReadFromPipe(int file, int timeout = -1);
    int WriteToPipe(int file, const std::string& s, int timeout = -1);
    bool SystemCmd(const std::string& cmd, int& ret_code);
private:
    int total_process_num_;
    int working_process_num_;
    std::string tool_dir_;
    WrMutex mutex_;
    boost::condition_variable_any cv_;
    std::map<pid_t, PidNode> standby_pool_;
    std::map<pid_t, PidNode> running_pool_;
};

}

#define CE imgsrv::CommandExecutor::get_mutable_instance()


#endif //IMGSRV_COMMAND_EXECUTOR_H
