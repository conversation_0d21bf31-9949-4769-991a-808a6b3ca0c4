/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : img_dj_cmd_proc.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019/8/28
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_IMG_DJ_CMD_PROC_H
#define IMGSRV_IMG_DJ_CMD_PROC_H


#include <boost/serialization/singleton.hpp>
#include <boost/algorithm/string.hpp>
#include <map>
#include "task_data.h"
#include "exif.h"

namespace imgsrv {

enum CmdType {
    kGm = 0,
    kZn,
    kZx,
    kXz,
    kErrCmd
};

class ImgDjCmdProc : public boost::serialization::singleton<ImgDjCmdProc>{
public:
    ImgDjCmdProc();
    bool Init(const std::string& output_path,
              const std::string& third_path,
              int hevc_thread_num);
    bool Process(const std::string& task_id);
    bool ProcessSelfAvif(const std::string& task_id);

private:
    CmdType ParseDjCmd(
            const std::string& task_id,
            const std::string& cmds,
            std::map<std::string, std::vector<int> >& cmd_params,
            std::string& suffix,
            std::vector<std::string>& gm_cmds);
    bool ConvertDjangoToGM(
            const std::string& task_id,
            std::map<std::string, std::vector<int> >& cmd_params,
            std::string& gm_cmd,
            std::string& suffix);
    std::string GetGmPath();
    std::string GetSmartCropBinPath();
    std::string GetTmpPath(const std::string& task_id);
    bool DoParam(const std::string& task_id, TaskDataPtr& task_data_ptr, const std::string& Param);
    bool DoSelfAvifParam(const std::string& task_id, TaskDataPtr& task_data_ptr, const std::string& Param);

    bool GmCommand(
            const std::string& input_path,
//            const std::string& task_id,
            TaskDataPtr& task_data_ptr,
            const std::vector<std::string>& gm_cmds,
            std::string& suffix_);

    bool ZnCommand(
            const std::string& input_path,
            TaskDataPtr& task_data_ptr,
            std::map<std::string, std::vector<int> >& cmd_params,
            std::string& suffix);
    bool XzCommand(
            const std::string& input_path,
            TaskDataPtr& task_data_ptr,
            std::map<std::string, std::vector<int> >& cmd_params,
            std::string& suffix);
    bool ZxCommand(
            const std::string& input_path,
            TaskDataPtr& task_data_ptr,
            std::map<std::string, std::vector<int> >& cmd_params,
            std::string& suffix);

    bool GetExif(const std::string& input_path, easyexif::EXIFInfo& input_exif);

private:
    std::string bin_path_;
    std::string third_path_;
    std::string output_path_;
    int hevc_thread_num_;
};
}

#define IMG_DJ_CMD_PROC imgsrv::ImgDjCmdProc::get_mutable_instance()
#endif //IMGSRV_IMG_DJ_CMD_PROC_H
