/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : server.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include <boost/lexical_cast.hpp>
#include <boost/asio/placeholders.hpp>
#include "server.h"
#include "log_adapter.h"
#include "connection.h"
#include "worker.h"
#include "command_executor.h"

bool imgsrv::Server::Init(unsigned int port, size_t thread_num, u_int back_log) {
    thread_num_ = thread_num;
    tcp::endpoint ep(tcp::v4(), boost::lexical_cast<unsigned short>(port));
    try {
        acceptor_.open(ep.protocol());
        acceptor_.set_option(tcp::acceptor::reuse_address(true));
        acceptor_.bind(ep);
        if (back_log == 0) {
            acceptor_.listen();
        } else {
            acceptor_.listen(back_log);
        }
    } catch (boost::system::system_error& error) {
        SLOG_E << "imgsrv listen on " << port << " failed, " << error.what();
        return false;
    }

//    signals_.add(SIGINT);
//    signals_.add(SIGTERM);
    signals_.add(SIGQUIT);
    signals_.async_wait(boost::bind(&imgsrv::Server::HandleStop, &SERVER));
//    signals_.async_wait(boost::bind(&imgsrv::Server::HandleStop, this));

    StartAccept();
    SLOG_I << "imgsrv listened on " << ":" << port
           << ", backlog "
           << ((back_log == 0) ? socket_base::max_connections : back_log);
    return true;
}

void imgsrv::Server::Run() {
    for (size_t i = 0; i < thread_num_; ++i) {
        boost::shared_ptr<boost::thread> t(
                new boost::thread(boost::bind(&io_service::run, &ios_)));
        threads_.push_back(t);
    }
    SLOG_I << "Network server started!";
}

void imgsrv::Server::Stop() {
    ios_.stop();
    SLOG_I << "Network server stopped!";
}

void imgsrv::Server::Join() {
    for (auto& thread : threads_) {
        thread->join();
    }
}

void imgsrv::Server::StartAccept() {
    connection_.reset(new ServerConnection(ios_));
    acceptor_.async_accept(connection_->socket(),
            bind(&Server::HandleAccept, this, boost::asio::placeholders::error));
}

void imgsrv::Server::HandleAccept(const boost::system::error_code& e) {
    if (!e) {
        connection_->Start();
    }
    StartAccept();
}

io_service& imgsrv::Server::GetIos() {
    return ios_;
}

imgsrv::Server::Server() : thread_num_(0), acceptor_(ios_), signals_(ios_), connection_() {

}

void imgsrv::Server::HandleStop() {
    Stop();
    WORKER.Stop();
    CE.Stop();
}
