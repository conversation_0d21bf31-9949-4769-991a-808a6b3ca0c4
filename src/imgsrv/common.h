/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : common.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-13
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_COMMON_H
#define IMGSRV_COMMON_H

namespace imgsrv {

enum ProcType {
    kTypeSafeguardBeg = -1,
    kImgProc = 0,
    kImgWaterMark = 1,
    kTextWaterMark = 2,
    kAudioConvert = 3,
    kTypeSafeguardEnd = 4,
    kVideoProc = 5,
    kImgAvif = 6
};

enum ReqType {
    kGet = 0,
    kPost,
    kErrMethod
};

enum CallType {
    kBin = 0,
    kApi,
    kErrCallType
};

enum TargetType {
    kId = 0,
    kUrl,
    kErr
};

enum audio_type {
    kpcm,
    kamr,
    ksilk,
    kerr
};

}
#endif //IMGSRV_COMMON_H
