//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/4/16.
//

#ifndef IMGSRV_BIN_DEVEL_AUDIO_CMD_PROC_H
#define IMGSRV_BIN_DEVEL_AUDIO_CMD_PROC_H
#include <string>
#include "reply.h"

namespace imgsrv {
class audio_cmd_proc {
public:
    static bool Process(const std::string& task_id);
    static std::string GenErrReply(imgsrv::Reply::StatusType st, int err_code);

private:
    static std::string GetTcPath();
    static std::string GetTmpPath(const std::string& task_id);
//    static std::string bin_path_;
//    static std::string output_path_;
};
}


#endif //IMGSRV_BIN_DEVEL_AUDIO_CMD_PROC_H
