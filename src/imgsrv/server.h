/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : server.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_SERVER_H
#define IMGSRV_SERVER_H

#include <string>
#include <vector>
#include <boost/thread/thread.hpp>
#include <boost/asio/io_service.hpp>
#include <boost/serialization/singleton.hpp>
#include "connection.h"

using namespace boost::asio;
using namespace boost::asio::ip;
namespace imgsrv {

class Server : public boost::serialization::singleton<Server>{
public:
    Server();
    bool Init(unsigned int port, size_t thread_num, u_int back_log);
    void Run();
    void Stop();
    void Join();
private:
    void StartAccept();
    void HandleAccept(const boost::system::error_code& e);
    void HandleStop();
public:
    io_service &GetIos();

private:
    io_service ios_;
    size_t thread_num_;
    tcp::acceptor acceptor_;
    signal_set signals_;
    ServerConnectionPtr connection_;
    std::vector<boost::shared_ptr<boost::thread> > threads_;
};
}

#define SERVER imgsrv::Server::get_mutable_instance()
#endif //IMGSRV_SERVER_H
