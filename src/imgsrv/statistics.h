/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : statistics.h
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019/9/19
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_STATISTICS_H
#define IMGSRV_STATISTICS_H


#include <string>
#include "request.h"
#include "log_adapter.h"

namespace imgsrv {
class Statistics {
public:
    Statistics()
            : task_id_(""),
              app_id_(""),
              biz_type_(""),
              req_type_(kErrMethod),
              proc_type_(kTypeSafeguardBeg),
              call_type_(kErrCallType),
              req_ip_(""),
              start_time_(0),
              end_time_(0),
              total_time_(0),
              time_out_(0),
              is_time_out_(false),
              status_type_(Reply::kDumbCode),
              rst_size_(0),
              req_url_(""),
//              req_header_(""),
              file_id_(""),
              target_type_(kErr),
              file_fetch_start_time_(0),
              file_fetch_end_time_(0),
              file_fetch_total_time_(0),
              file_fetch_rst_(false),
              file_fetch_size_(0),
              file_fetch_format_(""),
              logo_file_id_(""),
              logo_fetch_start_time_(0),
              logo_fetch_end_time_(0),
              logo_fetch_total_time_(0),
              logo_fetch_rst_(false),
              logo_fetch_size_(0),
              logo_fetch_format_(""),
              proc_start_time_(0),
              proc_end_time_(0),
              proc_total_time_(0),
              proc_rst_(false),
              call_start_time_(0),
              call_end_time_(0),
              call_total_time_(0),
              api_info_(""),
              trace_id_(""),
              rpc_id_(""){
        valid_ = true;
    }
    ~Statistics() {
        if (!valid_) {
            return;
        }
        S_STAT << task_id_ << ","
               << app_id_ << ","
               << biz_type_ << ","
               << req_type_ << ","
               << proc_type_ << ","
               << call_type_ << ","
               << req_ip_ << ","
               << start_time_ << ","
               << end_time_ << ","
               << total_time_ << ","
               << time_out_ << ","
               << is_time_out_ << ","
               << status_type_ << ","
               << rst_size_ << ","
               << req_url_ << ","
//               << req_header_ << ","
               << file_id_ << ","
               << target_type_ << ","
               << file_fetch_start_time_ << ","
               << file_fetch_end_time_ << ","
               << file_fetch_total_time_ << ","
               << file_fetch_rst_ << ","
               << file_fetch_size_ << ","
               << file_fetch_format_ << ","
               << logo_file_id_ << ","
               << logo_fetch_start_time_ << ","
               << logo_fetch_end_time_ << ","
               << logo_fetch_total_time_ << ","
               << logo_fetch_rst_ << ","
               << logo_fetch_size_ << ","
               << logo_fetch_format_ << ","
               << proc_start_time_ << ","
               << proc_end_time_ << ","
               << proc_total_time_ << ","
               << proc_rst_ << ","
               << call_start_time_ << ","
               << call_end_time_ << ","
               << call_total_time_ << ","
               << api_info_<<","
               << trace_id_ << ","
               << rpc_id_;
    }

    std::string task_id_;
    std::string app_id_;
    std::string biz_type_;
    ReqType req_type_;
    ProcType proc_type_;
    CallType call_type_;
    std::string req_ip_;
    int64_t start_time_;
    int64_t end_time_;
    int64_t total_time_;
    int time_out_;
    bool is_time_out_;
    Reply::StatusType status_type_;
    size_t rst_size_;
    std::string req_url_;
//    std::string req_header_;
    std::string file_id_;
    TargetType target_type_;
    int64_t file_fetch_start_time_;
    int64_t file_fetch_end_time_;
    int64_t file_fetch_total_time_;
    bool file_fetch_rst_;
    size_t file_fetch_size_;
    std::string file_fetch_format_;
    std::string logo_file_id_;
    int64_t logo_fetch_start_time_;
    int64_t logo_fetch_end_time_;
    int64_t logo_fetch_total_time_;
    bool logo_fetch_rst_;
    size_t logo_fetch_size_;
    std::string logo_fetch_format_;
    int64_t proc_start_time_;
    int64_t proc_end_time_;
    int64_t proc_total_time_;
    bool proc_rst_;
    int64_t call_start_time_;
    int64_t call_end_time_;
    int64_t call_total_time_;
    std::string api_info_;
    //traceid
    std::string trace_id_;
    // rpcid
    std::string rpc_id_;

public:
    void SetValid(bool valid) {
        valid_ = valid;
    }
private:
    bool valid_;
};
}


#endif //IMGSRV_STATISTICS_H
