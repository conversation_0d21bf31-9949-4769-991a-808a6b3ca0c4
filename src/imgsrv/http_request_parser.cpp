/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : http_request_parser.cpp
 * <AUTHOR> wenjun(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#include <algorithm>
#include "http_request_parser.h"

imgsrv::HttpRequestParser::HttpRequestParser() : state_(kMethodStart){

}

void imgsrv::HttpRequestParser::Reset() {
    state_ = kMethodStart;
}

imgsrv::HttpRequestParser::ParseResult imgsrv::HttpRequestParser::Consume(Request& req, char input) {
    switch (state_) {
        case kMethodStart:
            if (!IsChar(input) || IsCtl(input) || IsSpecial(input)) {
                return kError;
            } else {
                state_ = kMethod;
                req.method_.push_back(input);
                return kNotYet;
            }
        case kMethod:
            if (input == ' ') {
                if (!IsMethodSupported(req)) {
                    return kError;
                }
                state_ = kUri;
                return kNotYet;
            }
            else if (!IsChar(input) || IsCtl(input) || IsSpecial(input)) {
                return kError;
            } else {
                req.method_.push_back(input);
                return kNotYet;
            }
        case kUri:
            if (input == ' ') {
                state_ = kProtocol_H;
//            if (!url_decode(req.uri, req.decoded_uri)) {
//                return kError;
//            }
//            if(!parse_url(req)) {
//                return kError;
//            }
                req.url_.ParseUri(req.uri_);
                return kNotYet;
            } else if (IsCtl(input)) {
                return kError;
            } else {
                req.uri_.push_back(input);
                return kNotYet;
            }
        case kProtocol_H:
            if (input == 'H') {
                state_ = kProtocol_T1;
                return kNotYet;
            } else {
                return kError;
            }
        case kProtocol_T1:
            if (input == 'T') {
                state_ = kProtocol_T2;
                return kNotYet;
            } else {
                return kError;
            }
        case kProtocol_T2:
            if (input == 'T') {
                state_ = kProtocol_P;
                return kNotYet;
            } else {
                return kError;
            }
        case kProtocol_P:
            if (input == 'P') {
                state_ = kPvSlash;
                return kNotYet;
            } else {
                return kError;
            }
        case kPvSlash:
            if (input == '/') {
                state_ = kVersionMajorStart;
                req.http_version_major_ = 0;
                req.http_version_minor_ = 0;
                return kNotYet;
            } else {
                return kError;
            }
        case kVersionMajorStart:
            if (IsDigit(input)) {
                req.http_version_major_ = req.http_version_major_ * 10 + input - '0';
                state_ = kVersionMajor;
                return kNotYet;
            } else {
                return kError;
            }
        case kVersionMajor:
            if (input == '.') {
                state_ = kVersionMinorStart;
                return kNotYet;
            } else if (IsDigit(input)) {
                req.http_version_major_ = req.http_version_major_ * 10 + input - '0';
                return kNotYet;
            } else {
                return kError;
            }
        case kVersionMinorStart:
            if (IsDigit(input)) {
                state_ = kVersionMinor;
                req.http_version_minor_ = req.http_version_minor_ * 10 + input - '0';
                return kNotYet;
            } else {
                return kError;
            }
        case kVersionMinor:
            if (input == '\r') {
                state_ = kExpectingNewline1;
                return kNotYet;
            } else if (IsDigit(input)) {
                req.http_version_minor_ = req.http_version_minor_ * 10 + input - '0';
                return kNotYet;
            } else {
                return kError;
            }
        case kExpectingNewline1:
            if (input == '\n') {
                state_ = kHeaderLineStart;
                header_.name.clear();
                header_.value.clear();
                return kNotYet;
            } else {
                return kError;
            }
        case kHeaderLineStart:
            if (input == '\r') {
                state_ = kExpectingNewline2;
                return kNotYet;
            } else if (!IsChar(input) || IsCtl(input) || IsSpecial(input)) {
                return kError;
            } else {
                state_ = kHeaderName;
//            req.headers_.push_back(header());
//            req.headers_.back().name.push_back(input);
                header_.name.push_back(input);
                return kNotYet;
            }
        case kHeaderName:
            if (input == ':') {
                state_ = kSpaceBeforeHeaderValue;
                return kNotYet;
            } else if (!IsChar(input) || IsCtl(input) || IsSpecial(input)) {
                return kError;
            } else {
//            req.headers_.back().name.push_back(input);
                header_.name.push_back(input);
                return kNotYet;
            }
        case kSpaceBeforeHeaderValue:
            if (input == ' ') {
                state_ = kHeaderValue;
                return kNotYet;
            } else {
                return kError;
            }
        case kHeaderValue:
            if (input == '\r') {
                std::string lower_header;
                lower_header.resize(header_.name.size());
                std::transform(header_.name.begin(), header_.name.end(), lower_header.begin(), ::tolower);
//            req.headers_[header_.name] = header_.value;
                req.headers_[lower_header] = header_;
                state_ = kExpectingNewline1;
                return kNotYet;
            } else if (IsCtl(input)) {
                return kError;
            } else {
//            req.headers_.back().value.push_back(input);
                header_.value.push_back(input);
                return kNotYet;
            }
        case kExpectingNewline2:
            if (input == '\n') {
                return kDone;
            } else {
                return kError;
            }
        default:
            return kError;
    }
}

bool imgsrv::HttpRequestParser::IsChar(int c) {
    return c >= 0 && c <=127;
}

bool imgsrv::HttpRequestParser::IsCtl(int c) {
    return (c >=0 && c <=31) || (c == 127);
}

bool imgsrv::HttpRequestParser::IsSpecial(int c) {
    switch (c) {
        case '(': case ')': case '<': case '>': case '@':
        case ',': case ';': case ':': case '\\': case '"':
        case '/': case '[': case ']': case '?': case '=':
        case '{': case '}': case ' ': case '\t':
            return true;
        default:
            return false;
    }
}

bool imgsrv::HttpRequestParser::IsDigit(int c) {
    return c >= '0' && c <= '9';
}

bool imgsrv::HttpRequestParser::IsMethodSupported(imgsrv::Request& req) {
    if (req.method_.compare("GET") == 0) {
        req.req_type_ = kGet;
        return true;
    } else if (req.method_.compare("POST") == 0) {
        req.req_type_ = kPost;
        return true;
    }
    req.req_type_ = kErrMethod;
    return false;
}
