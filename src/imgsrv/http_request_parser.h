/**
 * ===============================================================
 * Copyright (C) 2019 All rights reserved.
 *
 * @file     : http_request_parser.h
 * <AUTHOR> wen<PERSON>(<EMAIL>)
 * @date     : 2019-08-06
 * @version  :
 * @brief    :
 *
 * @details  :
 *================================================================*/
#ifndef IMGSRV_HTTP_REQUEST_PARSER_H
#define IMGSRV_HTTP_REQUEST_PARSER_H

#include <cstdint>
#include "header.h"
#include "request.h"

namespace imgsrv {

class HttpRequestParser {
public:
    HttpRequestParser();
    void Reset();
    enum ParseResult {
        kDone = 0,
        kNotYet,
        kError
    };

    template<typename input_interator>
    HttpRequestParser::ParseResult ParseHeader(
            Request& req, input_interator begin, input_interator end, size_t& bytes_parsed) {
        bytes_parsed = 0;
        while (begin != end) {
            ParseResult rst = Consume(req, *begin++);
            bytes_parsed++;
            if (rst == kDone || rst == kError) {
                return rst;
            }
        }
        return kNotYet;
    }


private:
    enum ParseState {
        kMethodStart,
        kMethod,
        kUri,
        kProtocol_H,
        kProtocol_T1,
        kProtocol_T2,
        kProtocol_P,
        kPvSlash,
        kVersionMajorStart,
        kVersionMajor,
        kVersionMinorStart,
        kVersionMinor,
        kExpectingNewline1,
        kHeaderLineStart,
        kHeaderName,
        kSpaceBeforeHeaderValue,
        kHeaderValue,
        kExpectingNewline2/*,
        expecting_newline_3*/
    } state_;

    Header header_;

    ParseResult Consume(Request& req, char input);

    static bool IsChar(int c);

    static bool IsCtl(int c);

    static bool IsSpecial(int c);

    static bool IsDigit(int c);

//    static bool is_method_supported(std::string method);
    static bool IsMethodSupported(Request& req);
};


}


#endif //IMGSRV_HTTP_REQUEST_PARSER_H
