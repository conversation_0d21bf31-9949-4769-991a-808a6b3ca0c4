syntax = "proto2";

package mmpp;

// 用于client和master之间通信
message MessageClientReqPkt {
  optional string version = 1 [default = '1.0.0.1'];
  optional string biz_id = 11 [default = 'mmpp'];  // 请求业务id
  optional string app_id = 2 [default = 'afts'];  // 应用系统id
  optional string task_id = 3;  // 请求主任优化务id
  optional string mq_topic = 4;  // 请求MQtopic
  optional string mq_tags = 5;  // 请求MQtags
  optional RouteInfo route_info = 6;  // 路由信息
  enum ErrorCode {
    OK= 0;  // 请求处理成功
    FAILED_EXPECTATION = -1;  // 请求处理异常 
    ERROR_BUSY = -2;  // 处理集群忙，排队任务过多
    ERROR_INVALID_PARAMETER = -3;  // MessageClientReqPkt参数异常
    ERROR_LOG_FAILED = -4; // master写TAIR LOG异常
    ERROR_TASK_EXIST = -5;
    ERROR_TASK_TIME_OUT = -6;
    OTHER_ERROR = -7;
  }
  optional ErrorCode error_code = 7;  // 返回错误码
  optional string error_msg = 8;  // 返回错误信息
  enum RequestType {
    CMD_COMMIT_MISSION = 0;  // 提交任务
    CMD_RSP_RESULT = 1;  // 返回请求执行结果
  }
  optional RequestType type = 9;  // 消息类型
  repeated MessageBody body = 10;  // 请求任务信息，可以包含多个子任务
}

// 用于master和worker之间通信
message MessageTaskPkt {
  optional string version = 1 [default = '1.0.0.1'];
  optional string biz_id = 12 [default = 'mmpp'];
  optional string app_id = 2 [default = 'afts'];  // 请求业务id
  optional string task_id = 3;  // 请求主任务id
  optional string tid = 4;  // 子任务id
  optional RouteInfo route_info = 5;  // Message route info
  enum TaskStatus {
    WAIT = 0;
    RUNNING = 1;
    DONE = 2;
    ERROR = 3;
  }
  optional TaskStatus status = 6;  // 子任务执行状态
  enum ErrorCode {
    OK = 0;
    ERROR_INVALID_PARAMETER = -1;  // master发送的参数有误
    ERROR_BUSY = -2;  // 当前worker忙
    ERROR_UNKNOW_TYPE = -3;  // 错误的请求类型
    ERROR_PROCESS = -4;
    ERROR_GET_INPUT = -5;
    ERROR_TIME_OUT = -6;
    ERROR_OSS = -7;
    OTHER_ERROR = -8;
  }
  optional ErrorCode error_code = 7;  // 返回错误码
  optional string error_msg = 8;  // 返回错误信息
  optional bool need_retry = 9;  // 子任务执行失败，是否需要重试
  enum RequestType {
    TASK_DONE = 100;  // 子任务处理完毕
    TASK_UPDATE = 101;  // 更新子任务信息
    TASK_LOG = 102;  // LOG子任务信息

    TASK_SJOB = 153;  // 单一处理任务
    TASK_MAP = 154;  // MAP请求
    TASK_REDUCE = 155;  // Reduce请求
  }
  optional RequestType type = 10;  // 子任务请求类型
  optional MessageBody req_body = 11;  // 请求的Message body
  repeated MessageBody rsp_body = 13;  // 返回的mr Message body
}

message RouteInfo {
  optional int32 src_id = 1;  // 源id
  optional string src_srv_ip = 2;  // 源server ip
  optional int32 src_srv_port = 3;  // 源server port

  optional int32 dest_id = 4;  // 目标id
  optional string dest_srv_ip = 5;  // 目标server ip
  optional int32 dest_srv_port = 6; // 目标server port
}

message MessageBody {
  optional string tid = 1;  // 子任务id
  enum BodyType {
    IMG = 0;  // 图片处理请求
    VIDEO = 1;  // 视频处理请求（目前尚未支持）
    COMMAND = 2;
  }
  optional BodyType type = 2;  // 处理请求类型
  optional ImgProcBlob img_blob = 3;  // 图片处理请求实体，当type=IMG时必须填写
  optional VideoProcBlob video_blob = 4;  // 视频处理请求实体，当type=VIDEO时必须填写
  optional CommandProcBlob command_blob = 5;
}

message CommandProcBlob {
  enum Type {
    ModelZoo_Command = 0;
    ModelAssemble_Commnad = 1;
  }
  optional Type type = 1;
  optional ModelZooCommand mz_cmd = 2;
  optional ModelAssembleCommand ma_cmd = 3;
}

message ModelZooCommand {
  enum Type {
    Add = 0;
    Edit = 1;
    Del = 2;
  }
  optional Type type = 1;
  optional ModelConf model_conf = 2;
  optional int32 gray_percent = 3 [default = 100];
}

message ModelAssembleCommand {
  enum Type {
    Add = 0;
    Edit = 1;
    Del = 2;
    Query = 3;
  }
  optional Type type = 1;
  optional ModelAssemble model_assemble = 2;
}

message VideoProcBlob {
  optional InputParameter input = 1;
  optional OutputParameter output = 2;
  enum Type {
    Video_Capture = 0;  // 视频截帧
    Audio_TRANS = 1;
    Audio_APM_Offline = 2;
    Audio_APM_Station = 3;
    Audio_Recognition = 4;
  }
  optional Type type = 3;
  optional VideoCaptureCmd video_capture = 4;
  optional AudioTransCmd audio_trans = 5;
  optional AudioApmOfflineCmd audio_apm_offline = 6;
  optional AudioApmStationCmd audio_apm_station = 7;
  optional AudioRecogCmd audio_recog = 8;
}

message ImgProcBlob {
  optional InputParameter input = 1;  // 输入图片参数
  optional OutputParameter output = 2;  // 输出图片参数
  enum Type {
    CONVERT_PROC_CMD = 0;  // GM处理指令
    Django_CMD = 1;  // Django处理命令
    SMART_CROP_CMD = 2;  // 智能裁
    WATER_MARK_CMD = 3;  // 水印指令
    IMG_RETRIEVAL_CMD = 4;  // 图片检索指令
    IMG_RECOGNITION_CMD = 5;  // 图片识别
    IMG_MONTAGE_CMD = 6;  // 图片拼接
    QR_DETECT_CMD = 7;  // 二维码检测
    POSTER_RECOG_CMD = 8;  // 物料审核
    FACE_FUZZY_CMD = 9;
    NEW_IMG_MONTAGE_CMD = 10;
    SALIENCY_CROP_CMD = 11;
    IMG_ANALYSIS_CMD = 12;
    AUTO_QR_DETECT_CMD = 13;
    AUTO_SVG_CREATE_CMD = 14;
  }剪
  optional Type type = 3;  // 处理请求类型
  optional ImgConvertProcCmd convert_proc = 4;  // 转码处理实体，当type=GM_PROC_CMD时必须填写
  optional ImgDjangoCmd django = 5;  // Django处理命令实体，当type=Django_CMD时必须填写
  optional ImgSmartCropCmd smart_crop = 6;  // 智能裁剪处理实体，当type=SMART_CROP_CMD时必须填写
  optional ImgWaterMarkCmd water_mark = 7;  // 水印指令，当type=WATER_MARK_CMD时必须填写
  optional ImgRetrievalCmd img_retrieval = 8;  
  optional ImgRecognitionCmd img_recognition = 9;
  optional ImgMontageCmd img_montage = 10;
  optional ImgQRDetectCmd qr_detect = 11;
  optional ImgPosterRecogCmd poster_recog = 12;
  optional NewImgMontageCmd new_img_montage = 13;
  optional ImgSaliencyCropCmd saliency_crop = 14;
  optional ImgAnalysisCmd img_analysis = 15;
  optional ImgAutoQRDetectCmd auto_qr_detect = 16;
  optional ImgAutoSVGCreateCmd auto_svg_create = 17;
}

message InputParameter {
  enum InputType {
    URL = 0;  // 远端URL输入
    FILE = 1;  // 本地或者HDFS文件输入
    PKT = 2;  // 直接采用protobuf封装输入
    ID = 3;  // 存储系统id
    OSS_KEY = 4;
  }
  optional InputType type = 1;  // 输入文件类型
  enum OSSType {
    AFTS = 0;
    MMPP = 1;
    AFTS_CIF_BOX = 2;
  }
  optional OSSType oss_type = 12;
  repeated bytes data = 2;  // 二进制流，如果type=PKT，则必须填写
  optional string file_path = 3;  // 本地或者HDFS路径，如果type=FILE，则必须填写
  optional string url = 4;  // URL地址，如果type=URL，则必须填写
  repeated string key = 5;  // key，如果type=oss_key，则必须填写
  optional string id = 6;   // 输入id，type=ID填写
  optional int32 len = 7;  // 输入图片大小
  optional string file_id = 8;  // 原图文件id
  optional string suffix = 9;
  optional string sha = 10;
  optional string md5 = 11;
  optional CipherInfo cipher = 13;
}

message OutputParameter {
  enum OutputType {
    URL = 0;  // URL
    FILE = 1;  // 文件系统
    OSS_KEY = 2;  // OSS KEY
  }
  optional OutputType type = 1;  // 输出类型
  optional string suffix = 2 [default = 'src'];  // 输出格式 
  optional string file_path = 3;  // 输出文件路径，type=FILE必须填写
  optional string url = 4;  // 输出url路径，type=URL必须填写
  repeated string key = 5;  // 输出oss key， 如果type=OSS_KEY必须填写
  optional CipherInfo cipher = 6;
  repeated OutputMap outs = 7;
}

message OutputMap {
  optional string key = 1;
  optional string val = 2;
  optional string suffix = 3;
}

///////////////////////////////image parameter//////////////////////////////
message ImgAutoSVGCreateCmd {
  optional string model_id = 1;
  optional string scene_id = 2;
  repeated SVGImgInfo infos = 3;
  repeated string img_ids = 4;
}

message SVGImgInfo {
  optional string url = 1;
  optional string name = 2;
  optional int32 r = 3;
  optional int32 g = 4;
  optional int32 b = 5;
  optional string view_point = 6;
  optional int32 height = 7;
  optional int32 width = 8;
}

message ImgAnalysisCmd {
  enum Type {
    EDGE_DETECT = 0;
  }
  optional Type type = 1;
  optional UrlImgInfo img_info = 2;
  optional float res = 3;
}

message ImgPosterRecogCmd {
  optional UrlImgInfo img_info = 1;
  repeated string key_words = 2;
  optional bool has_poster = 3;
  repeated string names = 4;
}

message ImgQRDetectCmd {
  optional UrlImgInfo img_info = 1;
  optional bool has_qr = 2;
  repeated string url = 3;
}

message ImgAutoQRDetectCmd {
  optional UrlImgInfo img_info = 1;
  optional bool has_qr = 2;
  repeated string url = 3;
  repeated Point in_box = 4;
  optional int32 in_box_dim = 5;
  repeated Point out_box = 6;
  optional int32 out_box_dim = 7;
  optional bool ssd_detect_good = 8;
}

message ImgMontageCmd {
  optional string quality = 1;
  optional string templ = 2;
  repeated ImgMontageImgInfo imgs = 3;
  repeated ImgMontageTextInfo text = 4;
}

message ImgRecognitionCmd {
  enum RequestType {
    NiuPiXuan = 0;
    ImageQuality = 1;
    FoodRecog = 2;
    FlowerRecog = 3;
    SocialLabel = 4;
    SignboardRecog = 5; //门头照识别
    KoubeiLabel = 6;
    PornDetect = 7;
    ContentLabel = 8;
    Odometer = 9;
    TextQuality = 10;
    LicenseRecog = 11;
    ScreenShotRecog = 12;
    WSMobileNet = 13;
    MASK_RCNN = 14;
    Image_Clarity = 15;
    Insurance_Category = 16;
    OCR = 17;
    Koubei_Glcxyz = 18;
    Kbalgo_Language = 19;
    Common_Detect = 20;
    Common_Classify = 21;
    OCR_NEW = 22;
    XNN_MASK_RCNN = 23;
  }
  optional RequestType type = 1; 
  repeated ImgRecognitionInfo imgs = 2;  // 需要识别的图片列表
  optional RecogReduceInfo reduce = 3;  // 多图片识别结果融合后信息
  optional string version = 4;
  repeated string models_id = 5;
  optional string json_in = 6;
  optional string json_out = 7;
}

message ImgRetrievalCmd {
  enum RequestType {
    IMG_FEATURE = 0;
    IMG_RANK = 1;
  }
  optional RequestType type = 1;  // 请求类型，0：获取图片feature；1：对图片进行排序
  optional ImgInfo img_query = 2;  // 细排请求基准图片，type=1时必须填写 
  repeated ImgInfo imgs = 3;  // 特征提取请求实体，type=0时必须填写
  optional bool comp_res = 4;  // 是否匹配到相似图片
}

message ImgDjangoCmd {
  optional string opt = 1;
  optional string opt2 = 2;
}

message ImgSmartCropCmd {
  optional int32 width = 1;
  optional int32 height = 2;
  optional int32 quality = 3;
  optional bool progressive = 4; 
}

message ImgSaliencyCropCmd {
  optional int32 width = 1;
  optional int32 height = 2;
  optional int32 quality = 3;
}

message ImgConvertProcCmd {
  enum Type {
    GM_COMMAND = 0;  // 命令行方式
    GM_API = 1;  // API方式
    ZN = 2;
    XZ = 3;
    ZX = 4;
  }
  optional Type type = 1;
  repeated ImgGMProcCommand gm_command = 2;  // 命令行方式处理参数
  optional ImgGMProcParameter gm_parameter = 3;  // API方式处理参数
  optional ImgSmartCropCmd smart_crop = 4;
  optional ImgSaliencyCropCmd saliency_crop = 5;
}

message ImgWaterMarkCmd {
  enum Type {
    IMG = 1;
    TEXT = 2;
    MIX = 3;
  }
  optional Type type = 1;  // 1：图片水印 2：文字水印 3：文图混合水印
  optional int32 voffset = 2 [default = 0];  // 中线垂直偏移，取值范围：[-1000, 1000] 
  optional int32 t = 3 [default = 80];  // 透明度，默认值：100，表示 100%（不透明） 取值范围: [0-100]
  optional int32 p = 4 [default = 5];  // 位置，取值范围：[1-9]
  optional int32 x = 5 [default = 5];  // 水平边距，取值范围：[0 – 4096] 
  optional int32 y = 6 [default = 10];  // 垂直边距, 取值范围：[0 – 4096] 
  optional WaterMarkImg img = 7;  // 图片水印参数
  optional WaterMarkText text = 8;  // 文字水印参数
  optional WaterMarkMix mix = 9;  // 混合水印参数
}

message NewImgMontageCmd {
  enum Type {
    PREVIEW = 1;
    DO_JOB = 2;
    DO_DIY_JOB = 3;
    DO_SVG_JOB = 4;
  }
  optional Type type = 1;
  optional string quality = 2;
  optional string templ = 3;
  optional string templ_oss_object = 4;
  optional UrlImgInfo background = 5;
  repeated ImgsMap imgs = 6;
  repeated TextsMap texts = 7;
  optional string out_suffix = 8 [default = "jpg"];
  optional string content = 9 [default = ""];
  optional int32 dpi = 10 [default = 0];
}

message ImgsMap {
  optional string key = 1;
  optional UrlImgInfo val = 2;
}

message TextsMap {
  optional string key = 1;
  optional string val = 2;
}

message CipherInfo {
  enum Type {
    NONE = 0;
    AES_256_CBC = 1;
  }
  optional Type type = 1;
  optional bytes key = 2;
}

message UrlImgInfo {
  enum ImgType {
    ID = 0;
    URL = 1;
  }
  optional ImgType type = 1;  // 请求图片类型
  optional string key = 2;
  optional LBSInfo lbs = 3;
  optional int32 row = 4;
  optional int32 col = 5;
  optional string time = 6;
}

message ImgMontageImgInfo {
  enum ImgType {
    QR = 0;  // 二维码
    BAR = 1;  // 条形码
    HEADER = 2;  // 头像
  }
  optional ImgType type = 1;
  optional string url = 2;
}

message ImgMontageTextInfo {
  enum TextType {
    NAME = 0;  // 商户名称
    CODE = 1;  // 商户编号
    POST = 2;  // 邮编
    ADDRESS = 3;  // 地址
    CONTACT_PERSON = 4;  // 联系人
    PHONE = 5;  // 手机
    BAR = 6;  // 条形码ID
  }
  optional TextType type = 1;
  optional string value = 2;
}

message LBSInfo {
  optional double longitude = 1;  // 经度
  optional double latitude = 2;  // 纬度
}

message ImgInfo {
  enum ImgType {
    ID = 0;
    URL = 1;
  }
  optional ImgType type = 1;  // 请求图片访问类型
  optional string key = 2;  // 图片文件key，type=0表示存储id，type=1表示url
  optional double score = 3;  // 图片排序分数
  repeated Feature feature = 4;  // 图片特征
  optional LBSInfo lbs = 5;  // LBS信息
}

message Feature {
  enum FeatureType {
    LSH = 0;
    BOW = 1;
  }
  optional FeatureType ftype = 1;  // feature类型
  optional uint32 norm = 2;  // ftype=BOW的时候返回
  optional bytes cdata = 3;  // 特征为uchar的实体
  repeated uint32 idata = 4;  // 特征为int的实体
  repeated float fdata = 5;  // 特征为float的实体
  repeated double ddata = 6;  // 特征为double的实体
  optional float l2_norm = 7;  // L2归一化参数，ftype=BOW的时候返回
}

message WaterMarkImg {
  optional string object = 1;  // 水印图片的object名字
  optional int32 p = 2;
}

message WaterMarkText {
  optional string text = 1;  // 文字水印的文字内容
  enum Type { 
    WQY_ZENHEI = 1;
    WQY_MICROHEI = 2;
    FANGZHENGSHUSONG = 3;
    FANGZHENGKAITI = 4;
    FANGZHENGHEITI = 5;
    FANGZHENGFANGSONG = 6;
    DROIDSANSFALLBACK = 7;
  }
  optional Type type = 2 [default = FANGZHENGHEITI];  // 文字水印的文字类型
  optional string color = 3 [default = '#FFFFFF'];  // 文字水印文字的颜色，参数的构成必须是：# + 六个十六进制数 如：#000000表示黑色。 #是表示前缀，000000每两位构成RGB颜色， #FFFFFF表示的是白色 
  optional int32 size = 4 [default = 40];  // 文字水印文字大小，取值范围：(0，1000] 
  optional int32 s = 5;  // 文字水印的阴影透明度，取值范围：(0,100]
  optional int32 rotate = 6 [default = 0]; 
  optional bool fill = 7 [default = false];
}

message WaterMarkMix {
  optional int32 order = 1;  // 文字，图片水印前后顺序 [0, 1] order = 0 图片在前(默认值)； order = 1 文字在前。
  optional int32 align = 2;  // 文字、图片对齐方式 [0, 1, 2] align = 0 上对齐(默认值) align = 1 中对齐 align = 2 下对齐
  optional int32 interval = 3;  // 文字和图片间的间距 取值范围: [0, 1000]
}

message ImgGMProcCommand {
  optional string command = 1;  // 处理命令
  optional string opt = 2;  // 处理参数
}

message ImgGMProcParameter {
  optional int32 quality = 1;  // 转码质量
  optional bool progressive = 2;  // 是否采用渐显
  optional bool gray = 3;  // 是否灰度
  optional ImgCrop crop = 4;  // 裁剪
  enum Flip {
    Horizontal = 0;
    Vertical = 1;
  }
  optional Flip flip = 5;  // 反转
  enum Rotate {
    R_DEFAULT = 0;
    R_90 = 1;
    R_180 = 2;
    R_270 = 3;
  }
  optional Rotate rotate = 6;  // 旋转
  optional ImgScale scale = 7;  // 缩放
}

message Point {
  optional int32 X = 1;
  optional int32 Y = 2;
}

message fPoint {
  optional float X = 1;
  optional float Y = 2;
}

message ImgScale {
  optional int32 width = 1;  // 宽
  optional int32 height = 2;  // 高
}

message ImgCrop {
  optional Point start = 1;  // 裁剪起点
  optional Point end = 2;  // 裁剪终点
}

message Colors {
  optional int32 red = 1;
  optional int32 green = 2;
  optional int32 blue = 3;
}

///////////////////////////////video parameter//////////////////////////////
message VideoCaptureCmd {
  enum Strategy {
    Avg_Frame = 0;
    Key_Frame = 1;
    Time_Interval_Frame = 2;
    Gif_Header = 3;
    Video_Info = 4;
  }
  optional Strategy type = 1;
  optional int32 count = 2;  // 平均抽帧数量
  optional int32 time_interval = 3; // 单位ms，时间区间抽帧间隔
  optional int32 max_shot_frames = 4;  // 最大抽帧数量
  optional VideoHeaderInfo info = 5;
  optional string info_content = 6;
}

message VideoHeaderInfo {
  optional int32 retcode = 1;
  optional string format_name = 2;
  optional string format_long_name = 3;
  optional float duration = 4;
  optional int32 width = 5;
  optional int32 height = 6;
  optional float fps = 7;
  optional int32 gif_width = 8;
  optional int32 gif_height = 9;
}

message AudioTransCmd {
  optional string suffix = 1 [default = 'pcm'];
  optional int32 bitrate = 2 [default = 128000];
  optional int32 ar = 3 [default = 24000];
  optional int32 ac = 4 [default = 2];
}

message AudioApmOfflineCmd {
  optional int32 bAgc = 1 [default = 1];
  optional int32 bAnr = 2 [default = 1];
}

message AudioApmStationCmd {
  optional string words = 1;
  optional string stations = 2;
}

message AudioRecogCmd {
  optional string words = 1;
  optional int32 ar = 2 [default = 16000];
}

///////////////////////////////task info////////////////////////////////////
message TaskInfoPkt {
  optional string biz_id = 9;
  optional string app_id = 1;
  optional string task_id = 2;
  optional string key = 3;
  optional string create_time = 4;
  optional string start_time = 5;
  optional string end_time = 6;
  enum TaskStatus {
    WAIT = 0;
    RUNNING = 1;
    PAUSE = 2;
    FINISH = 3;
    CANCLE = 4;
    MAP = 5;
    REDUCE = 6;
    SUCESS = 7;
    ERROR = 8;
  }
  optional TaskStatus status = 7;
  repeated SubTaskInfoPkt sub_task = 8;
}

message SubTaskInfoPkt {
  optional string tid = 1;
  optional string key = 2;
  optional int32 retry_times = 3;
  enum SubTaskStatus {
    IDLE = 0;
    WAIT = 1;
    RUNNING = 2;
    PAUSE = 3;
    DONE = 4;
    CANCLE = 5;
    ERROR = 6;
  }
  optional SubTaskStatus status = 4;
  optional string start_time = 5;
  optional string end_time = 6;
  optional MessageBody body = 7;
}

message MmppMatrix {
  optional int32 channels = 1;
  optional int32 height = 2;
  optional int32 width = 3;

  optional bytes cdata = 4;  // char
  repeated int32 idata = 5;  // int
  repeated float fdata = 6;  // float
  repeated double ddata = 7;  // double
}

///////////////////////////////zk config////////////////////////////////////

message ZkConfig {
  optional string biz_weight_map = 1;
  optional int32 max_task_queue = 2;
  optional int32 task_time_out = 3;
  optional string id_header = 4;
  optional int32 hevc_threads = 5;
  optional int32 zoom_type = 6;
  optional int32 bow_word_limit = 7;
  optional int32 bow_featype = 8;
  optional int32 rank_topN = 9;
  optional int32 img_resize = 10;
}

message TaskStatMap {
  optional string biz_id = 1;
  optional string app_id = 2;
  optional int32 num = 3;
  optional int32 count = 4;
  optional float avg_time = 5;
}

message ZkNodeStat {
  optional string version = 1;
  optional float cpu = 2;
  optional float mem = 3;
  optional float dist = 4;
  optional int32 wait_task_num = 5;
  optional int32 running_task_num = 6;
  optional int32 total_task_num = 7;
  repeated TaskStatMap task_map = 8;
}

///////////////////////////////sdk config///////////////////////////////
message RecogReduceInfo {
  repeated ImgRecognitionResult res = 1;
}

message NewXnnInter {
  optional string input = 1;
  optional string output = 2;
}

message ImgRecognitionInfo {
  enum ImgType {
    ID = 0;
    URL = 1;
    FILE = 2;
    DATA = 3;
    OSS = 4;
  }
  optional ImgType type = 1;  // 请求图片类型
  optional string key = 2;  // 请求文件key，type=0表示存储id，type=1表示url
  repeated float data = 11; 
  repeated string buffer = 13;
  optional string extern_param = 14 [default = "{}"];
  repeated ImgRecognitionResult res = 3;  // 识别结果
  repeated ImgRecognitionResult res_1 = 10;  // 识别结果1
  optional bool flag = 4;  // 身份证识别：true 正面，false 反面
  optional int32 width = 5;
  optional int32 height = 6;
  repeated Point points = 7;  // 普通定义：图片ROI坐标点；货柜定义：x=层数，y=1标识开门前，y=2标识开门后，4,5表示IP, 6表示I, 7表示PP
  repeated Bound bunds = 8;
  repeated RectWithFeature rectFea = 9; // 目标区域位置和提取的feature
  optional int32 channels = 12;
}

message ImgRecognitionResult {
  optional string type = 1;
  optional double score = 2;
  repeated fPoint points = 3;
  optional bool flag = 4;
  optional string content = 5;
  repeated Colors colors = 6;
}

message RectWithFeature{
  optional ImgRecognitionResult boundInfo = 1;
  repeated BlobData data = 2;
}

message BlobData {
  optional string blob_Name = 1;
  optional int32 num = 2;
  optional int32 channels = 3;
  optional int32 height = 4;
  optional int32 width = 5;
  repeated float data = 6;
}

message Bound {
  repeated fPoint points = 1;
}

message OssConf {
  optional string endpoint = 1;
  optional string id = 2;
  optional string secret = 3;
  optional string bucket = 4;
}

message AftsConf {
  optional string upload = 1;
  optional string download = 2;
  optional string token = 3;
  optional string secret = 4;
}

message HandlerConf {
  optional string type = 1;
  repeated ImgRecognitionInfo infos = 2;
  optional string id_header = 3;
  enum ErrorCode {
    OK = 0;
    ERROR_INVALID_PARAMETER = -1;
    ERROR_BUSY = -2;
    ERROR_UNKNOW_TYPE = -3;
    ERROR_PROCESS = -4;
    ERROR_GET_INPUT = -5;
    ERROR_TIME_OUT = -6;
    ERROR_OSS = -7;
    OTHER_ERROR = -8;
  }
  optional ErrorCode error_code = 4;
  optional string zoom = 5; // _1e_1l.jpg&bz=MMPP
  optional int32 crop_width = 6;
  optional float threshold = 7 [default = 0];
  optional string blobname = 8;
  optional int32 download_time = 9 [default = 0];
  optional string biz_id = 10;
  optional string app_id = 11;
  optional string version = 12 [default = "1.0.0.0"];
  repeated string models_id = 13;
  optional RecogReduceInfo reduce = 14;
  enum RequestType {
    TASK_DONE = 100;  // 子任务处理完毕
    TASK_UPDATE = 101;  // 更新子任务信息
    TASK_LOG = 102;  // LOG子任务信息

    TASK_SJOB = 153;  // 单一处理任务
    TASK_MAP = 154;  // MAP请求
    TASK_REDUCE = 155;  // Reduce请求
  }
  optional RequestType req_type = 15 [default = TASK_SJOB]; 
  optional RequestType rsp_type = 16 [default = TASK_DONE]; 
  optional OssConf oss_conf = 17;
  optional int32 batch_size = 18 [default = 32];
  optional string task_key = 19;
  optional int32 topn = 20 [default = 10];
  optional string json_in = 21;
  optional string json_out = 22;
  optional AftsConf afts_conf = 23;
}

message ModelAssemble {
  optional string name = 1;
  optional string url = 2;
  optional string md5 = 3;
  optional string version = 4;
  repeated ModelConf model_conf = 5;
}

message ModelConf {
  optional string model_name = 1;
  optional string model_file = 2;
  optional string deploy_file = 3;
  optional string label_file = 4;
  optional string mean_file = 5 [default = ""];
  optional string mean_value = 6 [default = ""]; // sub mean value
  optional string device = 7 [default = "CPU"];
  optional string device_id = 8 [default = "1"];
  optional string input_image_layer = 9;
  repeated string input_layers = 10;
  repeated string output_layers = 11;
  optional double crop_ratio = 12 [default = 0.]; // 裁剪比例
  optional bool stretch = 13 [default = true]; // 缩放时是否拉伸
  optional double normalize = 14 [default = 0.007843]; // 归一化参数，乘数
  enum PadType {
    Center = 1;
    Left = 2;
    Right = 3;
    Top_Left = 4;
  }
  optional PadType pad_type = 15 [default = Center]; // 填充类型
  enum ChannelType {
    RGB = 1;
    BGR = 2;
  }
  optional ChannelType chann_type = 16 [default = RGB]; // 图片通道顺序
  enum Interpolation {
    INTER_NEAREST = 1;
    INTER_LINEAR = 2; 
    INTER_AREA = 3;
    INTER_CUBIC = 4;
    INTER_LANCZOS4 = 5;
  }
  optional Interpolation resize_type = 17 [default = INTER_LINEAR]; // 采样算法
  optional int32 instance_cnt = 18 [default = 1]; // xnn实例数
  enum ModelType {
    TensorFlow = 1;
    Caffe = 2;
    XNN = 3;
  }
  optional ModelType model_type = 19 [default = XNN];
  enum UpdateType {
    Add = 1;
    Edit = 2;
    Del = 3;
    Query = 4;
  }
  optional UpdateType update_type = 20;
  enum ModelFetchType {
    URL = 0;
    FILE = 1;
  }
  optional string model_file_md5 = 21;
  optional ModelFetchType model_fetch_type = 22 [default = URL];
  optional string label_file_md5 = 23;
  repeated string labels = 24;
}
