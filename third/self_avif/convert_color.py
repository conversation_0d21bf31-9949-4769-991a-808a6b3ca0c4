import os
import numpy as np
from PIL import Image

def rgb_to_yuv709(rgb_img, tv_range=True, gamma=2.2):
    # 归一化
#    print(f"in rgb_to_yuv709", rgb_img, tv_range, gamma)
    rgb = (np.asarray(rgb_img).astype(np.float32) / 255.0) ** (gamma / 2.2)
    # BT.709转换矩阵
    yuv = np.dot(rgb, [
        [0.2126, -0.1146, 0.5000],   # Y分量矩阵
        [0.7152, -0.3854, -0.4542],  # U分量矩阵
        [0.0722, 0.5000, -0.0458]    # V分量矩阵
    ])

    # 调整色度分量范围
    yuv[:, :, 1:] += 0.5  # UV分量偏移
    if tv_range:
        yuv[:, :, 0] = yuv[:, :, 0] * 219 + 16  # Y分量：0-255映射到16-235
        yuv[:, :, 1] = yuv[:, :, 1] * 224 + 16  # U分量：0-255映射到16-240
        yuv[:, :, 2] = yuv[:, :, 2] * 224 + 16  # V分量：0-255映射到16-240
    else:
        yuv = yuv * 255  # PC range
    yuv = np.clip(yuv, 0, 255).astype(np.uint8)
    return yuv

def write_yuv420(yuv, filename):
    # 分离YUV分量
    y = yuv[:, :, 0]
    # 计算下采样尺寸
    new_width = y.shape[1] // 2  # 宽度减半
    new_height = y.shape[0] // 2 # 高度减半
    u = yuv[::2, ::2, 1]  # 对U分量进行下采样
    v = yuv[::2, ::2, 2]  # 对V分量进行下采样
    # 调整下采样后的尺寸
    u_resized = np.array(Image.fromarray(u).resize((new_width, new_height), Image.BILINEAR))
    v_resized = np.array(Image.fromarray(v).resize((new_width, new_height), Image.BILINEAR))
    # 写入YUV420文件
    with open(filename, 'wb') as f:
        y.tofile(f)  # Y平面
        u_resized.tofile(f)  # U分量
        v_resized.tofile(f)  # V分量

def process_folder(input_folder, output_folder):
    # 遍历输入文件夹中的所有文件
    for filename in os.listdir(input_folder):
        if filename.lower().endswith('.png'):
            # 构造完整的文件路径
            input_path = os.path.join(input_folder, filename)
            # 读取PNG文件
            with Image.open(input_path) as img:
                # 确保图像以RGB模式打开
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                # 手动转换YUV
                yuv709 = rgb_to_yuv709(img)
                # 构造输出文件路径
                base_name = os.path.splitext(filename)[0]
                output_path = os.path.join(output_folder, f"{base_name}.yuv")
                # 写入YUV420文件
                write_yuv420(yuv709, output_path)
                #print(f"Converted {filename} to {output_path}")

if __name__ == "__main__":
    # 指定输入和输出文件夹
    input_folder = 'test'  # 替换为你的输入文件夹路径
    output_folder = 'ouput'  # 替换为你的输出文件夹路径

    # 创建输出文件夹（如果不存在）
    os.makedirs(output_folder, exist_ok=True)

    # 处理文件夹中的所有PNG文件
    process_folder(input_folder, output_folder)
