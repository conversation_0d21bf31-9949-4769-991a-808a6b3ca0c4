import sys
import os
import json
import subprocess
import re
import convert_color
from PIL import Image
import logging
import traceback

av1ffmpeg_path="./ffmpeg_cto"
ffprobe_path="./ffprobe_cto"
ffmpeg_path="./ffmpeg_cto"
ffmpeg_old="./ffmpeg5.0.1"
version= "v0.25.9"
new_path = os.path.join(os.getcwd(), "build/lib")
env = os.environ.copy()
env["LD_LIBRARY_PATH"] = env.get("LD_LIBRARY_PATH", "") + os.pathsep + new_path

def setup_logging(log_file):
    """配置双输出日志系统"""
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)  # 设置全局日志级别
    # 文件处理器（输出所有DEBUG及以上级别）
    file_handler = logging.FileHandler(log_file, mode='w')
    file_formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.DEBUG)  # 文件记录所有调试信息
    # 控制台处理器（仅输出结果）
    console_handler = logging.StreamHandler()
    #console_formatter = logging.Formatter('%(message)s')
    #console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.INFO)  # 控制台只显示重要信息
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

def check_error(result):
    # 判断 error_code 是否为 0
    if result!=None and result["error_code"] == 0:
        return False
    else:
        return True

def get_image_format(image_path):
    # 调用 ffprobe 获取图片的格式信息
    command = [
        ffprobe_path,
        "-v", "quiet",
        "-print_format", "json",
        "-show_format",
        "-show_streams",
        image_path
    ]
    
    try:
        result = subprocess.run(command, stdout=subprocess.PIPE,    # 替代 capture_output=True
            stderr=subprocess.PIPE,
            universal_newlines=True,   # 替代 text=True
            check=True,
            env=env)
        output = result.stdout
    except subprocess.CalledProcessError as e:
        logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
        logging.debug(f"[STDOUT] {result.stdout}")    # 记录到文件
        return "unknown"
    
    # 解析 JSON 输出
    try:
        data = json.loads(output)
    except json.JSONDecodeError as e:
        logging.debug("图片格式 json 解析失败")  # 记录到文件
        return "unknown"
    
    # 提取 codec_name
    if 'streams' in data and len(data['streams']) > 0:
        codec_name = data['streams'][0].get('codec_name', '').lower()
        color_range = data['streams'][0].get('color_range', '')
        if codec_name == 'png':
            return 'png', color_range
        elif codec_name in ['mjpeg', 'jpg']:
            return 'jpg', color_range

    logging.debug("图片格式非png和jpg")  # 记录到文件
    return "unknown"



def parse_psnr_log(log_file):
    """解析PSNR日志文件"""
    with open(log_file, "r") as f:
        for line in f:
            if "psnr_y:" in line and "psnr_u:" in line and "psnr_v:" in line:
                # 按空格分割并过滤空字符串
                parts = [p for p in line.replace(':', ' ').split() if p]
                # 构建键值对字典
                metrics = {parts[i]: float(parts[i+1]) for i in range(0, len(parts), 2)}
                
                # 检查并替换 inf 值
                psnr_y = metrics.get('psnr_y', None)
                psnr_u = metrics.get('psnr_u', None)
                psnr_v = metrics.get('psnr_v', None)
                
                if psnr_y == float('inf'):
                    psnr_y = 99.99
                if psnr_u == float('inf'):
                    psnr_u = 99.99
                if psnr_v == float('inf'):
                    psnr_v = 99.99
                
                return (psnr_y, psnr_u, psnr_v)
    logging.debug("psnr解析失败")  # 记录到文件
    return (None, None, None)

def process_png(picture_path, resolution, param, colorrange="tv", ivf_path="test.ivf"):
    errcode = 0x00

    gamma = 2.2
    image = Image.open(picture_path)
    info = image.info
    if 'gamma' in info:
        gamma = 1.0 / float(info['gamma'])
    color = "bt709"
    av1_pc = 0
    if colorrange == "pc":
        av1_pc = 1

    param = param + f":color-primaries={color}:matrix-coefficients={color}:transfer-characteristics={color}:color-range={av1_pc}"
    with Image.open(picture_path) as img:
        yuv709 = convert_color.rgb_to_yuv709(img, colorrange=="tv", gamma)
        # 构造输出文件路径
        base_name = os.path.splitext(picture_path)[0]
        yuv_path = base_name+'.yuv'
        convert_color.write_yuv420(yuv709, yuv_path)
        y = yuv709[:, :, 0]
        # 计算下采样尺寸
        yuv_width = y.shape[1]  # yuv宽度
        yuv_height = y.shape[0] # yuv高度
        command = [
            av1ffmpeg_path,
            "-f", "rawvideo",
            "-s", f"{yuv_width}x{yuv_height}",
#            "-pix_fmt", "yuv420p",
            "-i", yuv_path,
            "-vf", f"scale=w='if(gte({resolution},min(iw,ih)),0,if(gte(iw,ih),-2,{resolution}))':h='if(gte({resolution},min(iw,ih)),0,if(gte(iw,ih),{resolution},-2))', format=yuv420p, scale=ceil(iw/2)*2:ceil(ih/2)*2",
            "-frames:v", "1",
            "-vcodec", "libsvtav1",
            "-preset", "3",
            "-svtav1-params", param,
            "-y", ivf_path
        ]


        try:
            # 执行 FFmpeg 命令
            subprocess.run(command, check=True, 
                stdout=subprocess.DEVNULL,
                stderr=subprocess.PIPE, env=env)
        except subprocess.CalledProcessError as e:
            logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
            logging.debug("av1 转码失败") #记录到文件
            print(command)
            errcode = 0x05
            return {
                "picture_output": None,
                "psnr_output": None,
                "error_code": errcode,
                "version": version
            }
        
        return {
                "picture_output": None,
                "psnr_output": None,
                "error_code": errcode,
                "version": version
            }

def process_jpg(picture_path, resolution, param, ivf_path="test.ivf"):
    errcode = 0
    command = [
        av1ffmpeg_path,
        "-i", picture_path,
        "-vf", f"scale=w='if(gte({resolution},min(iw,ih)),0,if(gte(iw,ih),-2,{resolution}))':h='if(gte({resolution},min(iw,ih)),0,if(gte(iw,ih),{resolution},-2))', format=yuv420p, scale=ceil(iw/2)*2:ceil(ih/2)*2",
        "-pix_fmt","yuv420p",
        "-frames:v", "1",
#        "-vcodec", "libantav1",
        "-vcodec", "libsvtav1",
        "-preset", "3",
        "-svtav1-params", param,
        "-y", ivf_path
    ]

    try:
        # 执行 FFmpeg 命令
        subprocess.run(command, check=True, 
            stdout=subprocess.DEVNULL,
            stderr=subprocess.PIPE, env=env)
    except subprocess.CalledProcessError as e:
        logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
        logging.debug("av1 转码失败") #记录到文件
        errcode = 0x05
        return {
            "picture_output": None,
            "psnr_output": None,
            "error_code": errcode,
            "version": version
        }
    return {
            "picture_output": None,
            "psnr_output": None,
            "error_code": errcode,
            "version": version
        }


def get_avif_resolution(avif_path):
    # 调用 ffprobe 获取图片的格式信息
    command = [
        ffprobe_path,
        "-v", "quiet",
        "-show_streams",
        avif_path
    ]
    try:
        result = subprocess.run(command, stdout=subprocess.PIPE,    # 替代 capture_output=True
            stderr=subprocess.PIPE,
            universal_newlines=True,   # 替代 text=True
            check=True,
            env=env)
        output = result.stdout
    except subprocess.CalledProcessError as e:
        logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
        logging.debug(f"[STDOUT] {result.stdout}")    # 记录到文件
        return "unknown"

    # 解析 JSON 输出
    match = re.search(r'width=(\d+)', output)
    width = int(match.group(1)) if match else None

    match = re.search(r'height=(\d+)', output)
    height = int(match.group(1)) if match else None
    return width, height


def get_avif_dimensions(avif_path):
    # 调用 ffmpeg 获取 AVIF 文件的尺寸信息
    command = [
        ffprobe_path,
        "-i", avif_path,
        "-f", "null",
        "-"
    ]
    result = subprocess.run(command, stdout=subprocess.PIPE,    # 替代 capture_output=True
        stderr=subprocess.PIPE,
        universal_newlines=True,   # 替代 text=True
        check=True,
        env=env)
    output = result.stderr
    
    # 使用正则表达式提取宽度和高度
    match = re.search(r"Stream #0:0.*? (\d+)x(\d+)", output)
    if match:
        width = int(match.group(1))
        height = int(match.group(2))
        return width, height
    else:
        logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
        logging.debug("get_avif_dimensions 失败") #记录到文件

def picture_convert_to_avif(picture_path, resolution, param, avif_path):
    """将图片转换为AVIF"""
    errcode = 0x00
    psnr_log = "psnr.log"

    if os.path.exists("test.ivf"):
        os.remove("test.ivf")
    image_format, colorrange = get_image_format(picture_path)

    if image_format == "png":
        result = process_png(picture_path, resolution, param, colorrange, avif_path)                
        #result = process_png(picture_path, resolution, param, colorrange)
        if check_error(result):
            return result
    elif image_format == "jpg":
        result = process_jpg(picture_path, resolution, param, avif_path)
        #result = process_jpg(picture_path, resolution, param)
        if check_error(result):
            return result
    else:
        errcode = 0x04
        logging.debug("图片非png或jpg") #记录到文件
        return {
            "picture_output": None,
            "psnr_output": None,
            "error_code": errcode,
            "version": version
        }

    avif_tmp_path = 'tmp.avif'
    command = ["cp", "-f", avif_path, avif_tmp_path]
    try:
        # 执行 FFmpeg 命令
        subprocess.run(command, check=True, 
            stdout=subprocess.DEVNULL,
            stderr=subprocess.PIPE, env=env)
    except subprocess.CalledProcessError as e:
        logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
        logging.debug("avif 封装失败") #记录到文件
        errcode = 0x06
        return {
            "picture_output": None,
            "psnr_output": None,
            "error_code": errcode,
            "version": version
        }
    
    command = [
        ffmpeg_old,
        "-i", avif_tmp_path,
        "-c", "copy",
        "-y", avif_path
    ]
    try:
        # 执行 FFmpeg 命令
        subprocess.run(command, check=True, 
            stdout=subprocess.DEVNULL,
            stderr=subprocess.PIPE, env=env)
    except subprocess.CalledProcessError as e:
        logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
        logging.debug("avif 封装失败") #记录到文件
        errcode = 0x06
        return {
            "picture_output": None,
            "psnr_output": None,
            "error_code": errcode,
            "version": version
        }
    
    # 获取 AVIF 文件的尺寸
    try:
        avif_width, avif_height = get_avif_resolution(avif_path)
    except ValueError as e:
        logging.debug("获取avif长宽失败") #记录到文件
        errcode = 0x0a
        return {
            "picture_output": None,
            "psnr_output": None,
            "error_code": errcode,
            "version": version
        }

    command = [
        ffmpeg_path,
        "-i", picture_path,
        "-i", avif_path,
        "-filter_complex", f"[0:v]scale=w={avif_width}:h={avif_height}[ref]; [ref][1:v]psnr=stats_file={psnr_log}",
        "-f", "null",
        "-"
    ]
    try:
        # 执行 FFmpeg 命令
        subprocess.run(command, check=True, 
            stdout=subprocess.DEVNULL,
            stderr=subprocess.PIPE, env=env)
    except subprocess.CalledProcessError as e:
        logging.debug(f"[CMD] {' '.join(command)}")  # 记录到文件
        logging.debug("psnr计算失败") #记录到文件
        errcode = 0x07
        return {
            "picture_output": None,
            "psnr_output": None,
            "error_code": errcode,
            "version": version
        }

    psnr_y, psnr_u, psnr_v = parse_psnr_log(psnr_log)
    os.remove(psnr_log)
    return {
            "picture_output": avif_path,
            "psnr_output": psnr_y,
            "error_code": errcode,
            "version": version
        }

def main():
    #print("Version is: ",version)
    unvalid_param = False
    
    if not os.path.exists(av1ffmpeg_path) or not os.path.exists(ffmpeg_path) or not os.path.exists(ffmpeg_old):
        command = "sh download.sh"
        os.system(command)
    #new_path = os.path.join(os.getcwd(), "build/lib")  # 替代 $PWD
    #os.environ["LD_LIBRARY_PATH"] = os.environ.get("LD_LIBRARY_PATH", "") + os.pathsep + new_path
    #os.environ["LD_LIBRARY_PATH"] += ':$PWD'+ '/build/lib'
    #cmd = 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$PWD'+ '/build/lib'
    #print(cmd)
    #os.system(cmd)

    # 读取输入参数
    if len(sys.argv) != 7:
        #print(len(sys.argv))
        #print(sys.argv)
        print(json.dumps({"picture_output": None, "psnr_output": None, "error_code": 0x01, "version": version}))
        #logging.debug("输入不足, 输入格式为picture_input  	绝对路径 picture_output  绝对路径 log_output		绝对路径 picture_res	   	分辨率格式为 WxH param		   	云控下发  cae_open	   	0/1是否开启cae") #记录到文件
        sys.exit(1)

    picture_input = sys.argv[1]
    picture_output = sys.argv[2]
    log_output = sys.argv[3]
    picture_res = sys.argv[4]
    param = sys.argv[5]
    cae_open = sys.argv[6]


    if not os.path.exists(log_output):
        log_dir = os.path.dirname(log_output)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    if not os.path.exists(picture_output):
        pic_dir = os.path.dirname(picture_output)
        if not os.path.exists(pic_dir):
            os.makedirs(pic_dir, exist_ok=True)

    setup_logging(log_output)

    # 验证输入参数
    if not os.path.isfile(picture_input):
        logging.info(json.dumps({"picture_output": None, "psnr_output": None, "error_code": 0x01, "version": version}))
        logging.debug("输入图片不存在")
        sys.exit(1)
        return

    if 'x' not in picture_res:
        logging.info(json.dumps({"picture_output": None, "psnr_output": None, "error_code": 0x02, "version": version}))
        logging.debug("输入分辨率格式错误")
        sys.exit(1)
        return

    try:
        width, height = map(int, picture_res.split('x'))
    except ValueError:
        logging.info(json.dumps({"picture_output": None, "psnr_output": None, "error_code": 0x02, "version": version}))
        logging.debug("输入分辨率格式错误")
        sys.exit(1)
        return

    if cae_open not in ['0', '1']:
        logging.info(json.dumps({"picture_output": None, "psnr_output": None, "error_code": 0x09, "version": version}))
        logging.debug("输入cae_open格式错误")
        sys.exit(1)
        return
    if param is None or ('qp' not in param) or (not re.fullmatch(r"^[\w:-]+=[\w\d-]+(?::[\w:-]+=[\w\d-]+)*$", param)):
        logging.debug("unvalid param: %s", param)
        unvalid_param = True
        # param = "rc=0:keyint=256:tune=1:hierarchical-levels=5:qp=15"
        param = "rc=0:qp=15:keyint=256:tune=1:hierarchical-levels=5:enable-qm=1:ant-avif-p3-p4=1"
        # rest = ":color-primaries={colorprim}:matrix-coefficients={av1_matrix}:transfer-characteristics={trc}:color-range={pc_av1}"
    else:
        logging.debug("param is: %s", param)
    
    try:
        # 调用其他脚本进行图片处理
        # 接受输入图片路径和分辨率作为参数
        # 并返回处理后的图片路径和PSNR值及错误码
        #basename = os.path.splitext(picture_input)[0]
        avif_path = picture_output#basename+'.avif'
        result = picture_convert_to_avif(picture_input, max(width,height), param, avif_path)
        if unvalid_param:
            result['error_code'] = 0x03
        # 验证结果

        result_json = result
        if 'picture_output' not in result_json or 'psnr_output' not in result_json or 'error_code' not in result_json:
            logging.info(json.dumps({"picture_output": None, "psnr_output": None, "error_code": 0x0b, "version": version}))
            logging.debug("Invalid result format")
            sys.exit(1)
        logging.info(json.dumps(result_json))
        sys.exit(0)

    except Exception as e:
        # 捕获其他异常
        logging.debug("其他异常")
        logging.error(f"异常详情：{traceback.format_exc()}")
        logging.info(json.dumps({"picture_output": None, "psnr_output": None, "error_code": 0x0b, "version": version}))
        sys.exit(1)

if __name__ == "__main__":
    main()
