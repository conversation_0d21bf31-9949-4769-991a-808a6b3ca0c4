//
// Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/24.
//

#ifndef SALIENCYDETECTION_GET_IMAGE_SALIENCY_H
#define SALIENCYDETECTION_GET_IMAGE_SALIENCY_H

#include <cv.h>
//#include <highgui.h>

#define LOCATE_REGION_LOCAL  0
#define LOCATE_REGION_GLOBAL 1

using namespace std;
using namespace cv;


/*
 * pimageData         原始图片二进制数据；内部不拷贝
 * imageLength        原始图片二进制数据长度
 * outputImageWidth   输出图片宽度
 * outputImageHeight  输出图片高度
 * maxLength          图片处理resize大小（300）
 * minArea            显著性目标最小占比（0.1）
 * maxArea            显著性目标最大占比（0.8）
 * searchMethod       显著性区域搜索算法（LOCATE_REGION_GLOBAL）
 * quality            输出图片的jpg压缩质量 (95)
 * retImageLength     输出图片二进制数据长度
 *
 * return             剪裁后jpg图片二进制数据，未作剪裁返回NULL；注意外部delete；
 * */

char* cutBySal(char* pimageData, int imageLength, int outputImageWidth, int outputImageHeight, int maxLength, \
               double minArea, double maxArea, int searchMethod, int quality, int &retImageLength);

/*
 * image              原始图片
 * maxLength          图片处理resize大小（300）
 * minArea            显著性目标最小占比（0.1）
 * maxArea            显著性目标最大占比（0.8）
 * subWidth           输出图片宽度
 * subHeight          输出图片高度
 * searchMethod       显著性区域搜索算法（LOCATE_REGION_GLOBAL）
 *
 * return             剪裁后jpg图片二进制数据，未作剪裁返回NULL
 * */
//Mat cutBySal(const Mat &image, int maxLength, double minArea, double maxArea, int subWidth, int subHeight, int searchMethod);


#endif //SALIENCYDETECTION_GET_IMAGE_SALIENCY_H
