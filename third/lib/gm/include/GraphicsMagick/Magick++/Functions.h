// This may look like C code, but it is really -*- C++ -*-
//
// Copyright <PERSON>, 1999, 2000, 2001, 2003
//
// Simple C++ function wrappers for often used or otherwise
// inconvenient ImageMagick equivalents
//

#if !defined(Magick_Functions_header)
#define Magick_Functions_header

#include "Magick++/Include.h"
#include <string>
#include <vector>

namespace Magick
{
  //string process
  void MagickDLLDecl CloneString( char **destination_, const std::string &source_ );
  void MagickDLLDecl TimeToString(std::string &strDateStr,const time_t &timeData);
  void MagickDLLDecl StringSplit(const std::string& str, char sep, std::vector<std::string>* pieces);
  void MagickDLLDecl StringSplit(const std::string& str, const std::string& sep, std::vector<std::string>* pieces);
  bool MagickDLLDecl GetStringBack(const std::string& str, std::string& s, int& pos);
  void MagickDLLDecl StringReplace(std::string& s1, const std::string s2, const std::string s3);
  std::string MagickDLLDecl String2hex(const std::string& input);
  bool MagickDLLDecl GetNumFront(const std::string& str, int& result, int& pos);

  //time
  long MagickDLLDecl ComputeTimeDiff(const struct timeval &startTime,
                                           const struct timeval &endTime);

#ifdef HAS_CURL
  //network & file
  bool MagickDLLDecl GetUrlImg(const std::string& url, std::vector<unsigned char>& data, long timeout=5);
  bool MagickDLLDecl DownloadUrl(const std::string& url, std::string& output);
#endif
  std::string MagickDLLDecl GetSuffix(const std::string &dir, const std::string &name);
  size_t MagickDLLDecl GetFileSize(std::string& path);
  int MagickDLLDecl LoadFile(const std::string& file, char **pbuffer);
  int MagickDLLDecl DumpFile(const std::string& file, const void *buffer, int length);
  int MagickDLLDecl DumpFile(const std::string& file, const void *buffer, int length, const char *func, const int line);
    
    //crc & md5
  int MagickDLLDecl crc(const std::string& file);
  int MagickDLLDecl crc(const unsigned char* buf, int size);
}
#endif // Magick_Functions_header
