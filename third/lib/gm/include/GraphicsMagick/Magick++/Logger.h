
/***********************************************************************
 * Software License Agreement (BSD License)
 *
 * Copyright 2008-2009  <PERSON> (ma<PERSON><PERSON>@cs.ubc.ca). All rights reserved.
 * Copyright 2008-2009  <PERSON> (<EMAIL>). All rights reserved.
 *
 * THE BSD LICENSE
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *************************************************************************/

#ifndef MMCODEC_LOGGER_H
#define MMCODEC_LOGGER_H

#include <stdio.h>
#include <stdarg.h>
#include <iostream>
#include <iomanip>
#include <fstream>
#include <string>
#include <cstdlib>
#include <stdint.h>


///
/// \brief 日志文件的类型
///
typedef enum log_rank {
    REPORT,
    TRACE,
    INFO,
    WARNING,
    ERROR,
    FATAL
}log_rank_t;

///
/// \brief 初始化日志文件
/// \param report_log_filename report的埋点日志
/// \param trace_log_filename 调试文件的名字/dev/null
/// \param info_log_filename 信息文件的名字
/// \param warn_log_filename 警告文件的名字
/// \param error_log_filename 错误文件的名字
void initLogger(const std::string& report_log_filename,
                const std::string& trace_log_filename,
                const std::string& info_log_filename,
                const std::string& warn_log_filename,
                const std::string& error_log_filename);

void uninitLogger();
///
/// \brief 日志系统类
///
class Logger {
    friend void initLogger(const std::string& report_log_filename,
                           const std::string& trace_log_filename,
                           const std::string& info_log_filename,
                           const std::string& warn_log_filename,
                           const std::string& erro_log_filename);
    
    friend void uninitLogger();
    
public:
    //构造函数
    Logger(log_rank_t log_rank) : m_log_rank(log_rank) {};
    
    ~Logger();
    ///
    /// \brief 写入日志信息之前先写入的源代码文件名, 行号, 函数名
    /// \param log_rank 日志的等级
    /// \param line 日志发生的行号
    /// \param function 日志发生的函数
    static std::ostream& start(log_rank_t log_rank,
                               const int line,
                               const std::string& function);
    
private:
    ///
    /// \brief 根据等级获取相应的日志输出流
    ///
    static std::ostream& getStream(log_rank_t log_rank);
    
    static std::ofstream m_report_log_file;                  ///< 埋点信息的输出流
    static std::ofstream m_trace_log_file;                  ///< Debug信息的输出流
    static std::ofstream m_info_log_file;                   ///< 信息日子的输出流
    static std::ofstream m_warn_log_file;                  ///< 警告信息的输出流
    static std::ofstream m_error_log_file;                  ///< 错误信息的输出流
    log_rank_t m_log_rank;                             ///< 日志的信息的等级
};


///
/// \brief 根据不同等级进行用不同的输出流进行读写
///
#define LOG(log_rank)   \
    Logger(log_rank).start(log_rank, __LINE__,__FUNCTION__)

#endif //OPENCV_FLANN_LOGGER_H
