/*
  Copyright (C) 2012-2014 GraphicsMagick Group
 
  This program is covered by multiple licenses, which are described in
  Copyright.txt. You should have received a copy of Copyright.txt with this
  package; otherwise see http://www.graphicsmagick.org/www/Copyright.html.

  Library symbol name-scoping support.     

  Obtained via:                                          

  nm -p wand/.libs/libGraphicsMagickWand.a | grep ' T ' | egrep -vi '(Gm)|(lt_)' | \
  awk '{ printf("#define %s Gm%s\n", $3, $3); }' | sort > wand/wand_symbols.h

*/

#if !defined(_MAGICK_WAND_SYMBOLS_H)
#define _MAGICK_WAND_SYMBOLS_H

#if defined(PREFIX_MAGICK_SYMBOLS)

#define CloneMagickWand GmCloneMagickWand
#define ClonePixelWand GmClonePixelWand
#define ClonePixelWands GmClonePixelWands
#define CopyMagickString GmCopyMagickString
#define DestroyMagickWand GmDestroyMagickWand
#define DestroyPixelWand GmDestroyPixelWand
#define FormatMagickString GmFormatMagickString
#define FormatMagickStringList GmFormatMagickStringList
#define MagickAdaptiveThresholdImage GmMagickAdaptiveThresholdImage
#define MagickAddImage GmMagickAddImage
#define MagickAddNoiseImage GmMagickAddNoiseImage
#define MagickAffineTransformImage GmMagickAffineTransformImage
#define MagickAnimateImages GmMagickAnimateImages
#define MagickAnnotateImage GmMagickAnnotateImage
#define MagickAppendImages GmMagickAppendImages
#define MagickAverageImages GmMagickAverageImages
#define MagickBlackThresholdImage GmMagickBlackThresholdImage
#define MagickBlurImage GmMagickBlurImage
#define MagickBorderImage GmMagickBorderImage
#define MagickCdlImage GmMagickCdlImage
#define MagickCharcoalImage GmMagickCharcoalImage
#define MagickChopImage GmMagickChopImage
#define MagickClipImage GmMagickClipImage
#define MagickClipPathImage GmMagickClipPathImage
#define MagickCloneDrawingWand GmMagickCloneDrawingWand
#define MagickCoalesceImages GmMagickCoalesceImages
#define MagickColorFloodfillImage GmMagickColorFloodfillImage
#define MagickColorizeImage GmMagickColorizeImage
#define MagickCommentImage GmMagickCommentImage
#define MagickCompareImageChannels GmMagickCompareImageChannels
#define MagickCompareImages GmMagickCompareImages
#define MagickCompositeImage GmMagickCompositeImage
#define MagickContrastImage GmMagickContrastImage
#define MagickConvolveImage GmMagickConvolveImage
#define MagickCropImage GmMagickCropImage
#define MagickCycleColormapImage GmMagickCycleColormapImage
#define MagickDeconstructImages GmMagickDeconstructImages
#define MagickDescribeImage GmMagickDescribeImage
#define MagickDespeckleImage GmMagickDespeckleImage
#define MagickDestroyDrawingWand GmMagickDestroyDrawingWand
#define MagickDisplayImage GmMagickDisplayImage
#define MagickDisplayImages GmMagickDisplayImages
#define MagickDrawAffine GmMagickDrawAffine
#define MagickDrawAllocateWand GmMagickDrawAllocateWand
#define MagickDrawAnnotation GmMagickDrawAnnotation
#define MagickDrawArc GmMagickDrawArc
#define MagickDrawBezier GmMagickDrawBezier
#define MagickDrawCircle GmMagickDrawCircle
#define MagickDrawClearException GmMagickDrawClearException
#define MagickDrawColor GmMagickDrawColor
#define MagickDrawComment GmMagickDrawComment
#define MagickDrawComposite GmMagickDrawComposite
#define MagickDrawEllipse GmMagickDrawEllipse
#define MagickDrawGetClipPath GmMagickDrawGetClipPath
#define MagickDrawGetClipRule GmMagickDrawGetClipRule
#define MagickDrawGetClipUnits GmMagickDrawGetClipUnits
#define MagickDrawGetException GmMagickDrawGetException
#define MagickDrawGetFillColor GmMagickDrawGetFillColor
#define MagickDrawGetFillOpacity GmMagickDrawGetFillOpacity
#define MagickDrawGetFillRule GmMagickDrawGetFillRule
#define MagickDrawGetFontFamily GmMagickDrawGetFontFamily
#define MagickDrawGetFont GmMagickDrawGetFont
#define MagickDrawGetFontSize GmMagickDrawGetFontSize
#define MagickDrawGetFontStretch GmMagickDrawGetFontStretch
#define MagickDrawGetFontStyle GmMagickDrawGetFontStyle
#define MagickDrawGetFontWeight GmMagickDrawGetFontWeight
#define MagickDrawGetGravity GmMagickDrawGetGravity
#define MagickDrawGetStrokeAntialias GmMagickDrawGetStrokeAntialias
#define MagickDrawGetStrokeColor GmMagickDrawGetStrokeColor
#define MagickDrawGetStrokeDashArray GmMagickDrawGetStrokeDashArray
#define MagickDrawGetStrokeDashOffset GmMagickDrawGetStrokeDashOffset
#define MagickDrawGetStrokeLineCap GmMagickDrawGetStrokeLineCap
#define MagickDrawGetStrokeLineJoin GmMagickDrawGetStrokeLineJoin
#define MagickDrawGetStrokeMiterLimit GmMagickDrawGetStrokeMiterLimit
#define MagickDrawGetStrokeOpacity GmMagickDrawGetStrokeOpacity
#define MagickDrawGetStrokeWidth GmMagickDrawGetStrokeWidth
#define MagickDrawGetTextAntialias GmMagickDrawGetTextAntialias
#define MagickDrawGetTextDecoration GmMagickDrawGetTextDecoration
#define MagickDrawGetTextEncoding GmMagickDrawGetTextEncoding
#define MagickDrawGetTextUnderColor GmMagickDrawGetTextUnderColor
#define MagickDrawImage GmMagickDrawImage
#define MagickDrawLine GmMagickDrawLine
#define MagickDrawMatte GmMagickDrawMatte
#define MagickDrawPathClose GmMagickDrawPathClose
#define MagickDrawPathCurveToAbsolute GmMagickDrawPathCurveToAbsolute
#define MagickDrawPathCurveToQuadraticBezierAbsolute GmMagickDrawPathCurveToQuadraticBezierAbsolute
#define MagickDrawPathCurveToQuadraticBezierRelative GmMagickDrawPathCurveToQuadraticBezierRelative
#define MagickDrawPathCurveToQuadraticBezierSmoothAbsolute GmMagickDrawPathCurveToQuadraticBezierSmoothAbsolute
#define MagickDrawPathCurveToQuadraticBezierSmoothRelative GmMagickDrawPathCurveToQuadraticBezierSmoothRelative
#define MagickDrawPathCurveToRelative GmMagickDrawPathCurveToRelative
#define MagickDrawPathCurveToSmoothAbsolute GmMagickDrawPathCurveToSmoothAbsolute
#define MagickDrawPathCurveToSmoothRelative GmMagickDrawPathCurveToSmoothRelative
#define MagickDrawPathEllipticArcAbsolute GmMagickDrawPathEllipticArcAbsolute
#define MagickDrawPathEllipticArcRelative GmMagickDrawPathEllipticArcRelative
#define MagickDrawPathFinish GmMagickDrawPathFinish
#define MagickDrawPathLineToAbsolute GmMagickDrawPathLineToAbsolute
#define MagickDrawPathLineToHorizontalAbsolute GmMagickDrawPathLineToHorizontalAbsolute
#define MagickDrawPathLineToHorizontalRelative GmMagickDrawPathLineToHorizontalRelative
#define MagickDrawPathLineToRelative GmMagickDrawPathLineToRelative
#define MagickDrawPathLineToVerticalAbsolute GmMagickDrawPathLineToVerticalAbsolute
#define MagickDrawPathLineToVerticalRelative GmMagickDrawPathLineToVerticalRelative
#define MagickDrawPathMoveToAbsolute GmMagickDrawPathMoveToAbsolute
#define MagickDrawPathMoveToRelative GmMagickDrawPathMoveToRelative
#define MagickDrawPathStart GmMagickDrawPathStart
#define MagickDrawPeekGraphicContext GmMagickDrawPeekGraphicContext
#define MagickDrawPoint GmMagickDrawPoint
#define MagickDrawPolygon GmMagickDrawPolygon
#define MagickDrawPolyline GmMagickDrawPolyline
#define MagickDrawPopClipPath GmMagickDrawPopClipPath
#define MagickDrawPopDefs GmMagickDrawPopDefs
#define MagickDrawPopGraphicContext GmMagickDrawPopGraphicContext
#define MagickDrawPopPattern GmMagickDrawPopPattern
#define MagickDrawPushClipPath GmMagickDrawPushClipPath
#define MagickDrawPushDefs GmMagickDrawPushDefs
#define MagickDrawPushGraphicContext GmMagickDrawPushGraphicContext
#define MagickDrawPushPattern GmMagickDrawPushPattern
#define MagickDrawRectangle GmMagickDrawRectangle
#define MagickDrawRender GmMagickDrawRender
#define MagickDrawRotate GmMagickDrawRotate
#define MagickDrawRoundRectangle GmMagickDrawRoundRectangle
#define MagickDrawScale GmMagickDrawScale
#define MagickDrawSetClipPath GmMagickDrawSetClipPath
#define MagickDrawSetClipRule GmMagickDrawSetClipRule
#define MagickDrawSetClipUnits GmMagickDrawSetClipUnits
#define MagickDrawSetFillColor GmMagickDrawSetFillColor
#define MagickDrawSetFillOpacity GmMagickDrawSetFillOpacity
#define MagickDrawSetFillPatternURL GmMagickDrawSetFillPatternURL
#define MagickDrawSetFillRule GmMagickDrawSetFillRule
#define MagickDrawSetFontFamily GmMagickDrawSetFontFamily
#define MagickDrawSetFont GmMagickDrawSetFont
#define MagickDrawSetFontSize GmMagickDrawSetFontSize
#define MagickDrawSetFontStretch GmMagickDrawSetFontStretch
#define MagickDrawSetFontStyle GmMagickDrawSetFontStyle
#define MagickDrawSetFontWeight GmMagickDrawSetFontWeight
#define MagickDrawSetGravity GmMagickDrawSetGravity
#define MagickDrawSetStrokeAntialias GmMagickDrawSetStrokeAntialias
#define MagickDrawSetStrokeColor GmMagickDrawSetStrokeColor
#define MagickDrawSetStrokeDashArray GmMagickDrawSetStrokeDashArray
#define MagickDrawSetStrokeDashOffset GmMagickDrawSetStrokeDashOffset
#define MagickDrawSetStrokeLineCap GmMagickDrawSetStrokeLineCap
#define MagickDrawSetStrokeLineJoin GmMagickDrawSetStrokeLineJoin
#define MagickDrawSetStrokeMiterLimit GmMagickDrawSetStrokeMiterLimit
#define MagickDrawSetStrokeOpacity GmMagickDrawSetStrokeOpacity
#define MagickDrawSetStrokePatternURL GmMagickDrawSetStrokePatternURL
#define MagickDrawSetStrokeWidth GmMagickDrawSetStrokeWidth
#define MagickDrawSetTextAntialias GmMagickDrawSetTextAntialias
#define MagickDrawSetTextDecoration GmMagickDrawSetTextDecoration
#define MagickDrawSetTextEncoding GmMagickDrawSetTextEncoding
#define MagickDrawSetTextUnderColor GmMagickDrawSetTextUnderColor
#define MagickDrawSetViewbox GmMagickDrawSetViewbox
#define MagickDrawSkewX GmMagickDrawSkewX
#define MagickDrawSkewY GmMagickDrawSkewY
#define MagickDrawTranslate GmMagickDrawTranslate
#define MagickEdgeImage GmMagickEdgeImage
#define MagickEmbossImage GmMagickEmbossImage
#define MagickEnhanceImage GmMagickEnhanceImage
#define MagickEqualizeImage GmMagickEqualizeImage
#define MagickExtentImage GmMagickExtentImage
#define MagickFlattenImages GmMagickFlattenImages
#define MagickFlipImage GmMagickFlipImage
#define MagickFlopImage GmMagickFlopImage
#define MagickFrameImage GmMagickFrameImage
#define MagickFxImageChannel GmMagickFxImageChannel
#define MagickFxImage GmMagickFxImage
#define MagickGammaImageChannel GmMagickGammaImageChannel
#define MagickGammaImage GmMagickGammaImage
#define MagickGetConfigureInfo GmMagickGetConfigureInfo
#define MagickGetCopyright GmMagickGetCopyright
#define MagickGetException GmMagickGetException
#define MagickGetFilename GmMagickGetFilename
#define MagickGetHomeURL GmMagickGetHomeURL
#define MagickGetImageAttribute GmMagickGetImageAttribute
#define MagickGetImageBackgroundColor GmMagickGetImageBackgroundColor
#define MagickGetImageBluePrimary GmMagickGetImageBluePrimary
#define MagickGetImageBorderColor GmMagickGetImageBorderColor
#define MagickGetImageBoundingBox GmMagickGetImageBoundingBox
#define MagickGetImageChannelDepth GmMagickGetImageChannelDepth
#define MagickGetImageChannelExtrema GmMagickGetImageChannelExtrema
#define MagickGetImageChannelMean GmMagickGetImageChannelMean
#define MagickGetImageColormapColor GmMagickGetImageColormapColor
#define MagickGetImageColors GmMagickGetImageColors
#define MagickGetImageColorspace GmMagickGetImageColorspace
#define MagickGetImageCompose GmMagickGetImageCompose
#define MagickGetImageCompression GmMagickGetImageCompression
#define MagickGetImageDelay GmMagickGetImageDelay
#define MagickGetImageDepth GmMagickGetImageDepth
#define MagickGetImageDispose GmMagickGetImageDispose
#define MagickGetImageExtrema GmMagickGetImageExtrema
#define MagickGetImageFilename GmMagickGetImageFilename
#define MagickGetImageFormat GmMagickGetImageFormat
#define MagickGetImageFuzz GmMagickGetImageFuzz
#define MagickGetImageGamma GmMagickGetImageGamma
#define MagickGetImageGeometry GmMagickGetImageGeometry
#define MagickGetImageGravity GmMagickGetImageGravity
#define MagickGetImage GmMagickGetImage
#define MagickGetImageGreenPrimary GmMagickGetImageGreenPrimary
#define MagickGetImageHeight GmMagickGetImageHeight
#define MagickGetImageHistogram GmMagickGetImageHistogram
#define MagickGetImageIndex GmMagickGetImageIndex
#define MagickGetImageInterlaceScheme GmMagickGetImageInterlaceScheme
#define MagickGetImageIterations GmMagickGetImageIterations
#define MagickGetImageMatte GmMagickGetImageMatte
#define MagickGetImageMatteColor GmMagickGetImageMatteColor
#define MagickGetImagePage GmMagickGetImagePage
#define MagickGetImagePixels GmMagickGetImagePixels
#define MagickGetImageProfile GmMagickGetImageProfile
#define MagickGetImageRedPrimary GmMagickGetImageRedPrimary
#define MagickGetImageRenderingIntent GmMagickGetImageRenderingIntent
#define MagickGetImageResolution GmMagickGetImageResolution
#define MagickGetImageSavedType GmMagickGetImageSavedType
#define MagickGetImageScene GmMagickGetImageScene
#define MagickGetImageSignature GmMagickGetImageSignature
#define MagickGetImageSize GmMagickGetImageSize
#define MagickGetImageType GmMagickGetImageType
#define MagickGetImageUnits GmMagickGetImageUnits
#define MagickGetImageVirtualPixelMethod GmMagickGetImageVirtualPixelMethod
#define MagickGetImageWhitePoint GmMagickGetImageWhitePoint
#define MagickGetImageWidth GmMagickGetImageWidth
#define MagickGetNumberImages GmMagickGetNumberImages
#define MagickGetPackageName GmMagickGetPackageName
#define MagickGetQuantumDepth GmMagickGetQuantumDepth
#define MagickGetReleaseDate GmMagickGetReleaseDate
#define MagickGetResourceLimit GmMagickGetResourceLimit
#define MagickGetSamplingFactors GmMagickGetSamplingFactors
#define MagickGetSize GmMagickGetSize
#define MagickGetVersion GmMagickGetVersion
#define MagickHaldClutImage GmMagickHaldClutImage
#define MagickHasNextImage GmMagickHasNextImage
#define MagickHasPreviousImage GmMagickHasPreviousImage
#define MagickImplodeImage GmMagickImplodeImage
#define MagickLabelImage GmMagickLabelImage
#define MagickLevelImageChannel GmMagickLevelImageChannel
#define MagickLevelImage GmMagickLevelImage
#define MagickMagnifyImage GmMagickMagnifyImage
#define MagickMapImage GmMagickMapImage
#define MagickMatteFloodfillImage GmMagickMatteFloodfillImage
#define MagickMedianFilterImage GmMagickMedianFilterImage
#define MagickMinifyImage GmMagickMinifyImage
#define MagickModulateImage GmMagickModulateImage
#define MagickMontageImage GmMagickMontageImage
#define MagickMorphImages GmMagickMorphImages
#define MagickMosaicImages GmMagickMosaicImages
#define MagickMotionBlurImage GmMagickMotionBlurImage
#define MagickNegateImageChannel GmMagickNegateImageChannel
#define MagickNegateImage GmMagickNegateImage
#define MagickNewDrawingWand GmMagickNewDrawingWand
#define MagickNextImage GmMagickNextImage
#define MagickNormalizeImage GmMagickNormalizeImage
#define MagickOilPaintImage GmMagickOilPaintImage
#define MagickOpaqueImage GmMagickOpaqueImage
#define MagickPingImage GmMagickPingImage
#define MagickPreviewImages GmMagickPreviewImages
#define MagickPreviousImage GmMagickPreviousImage
#define MagickProfileImage GmMagickProfileImage
#define MagickQuantizeImage GmMagickQuantizeImage
#define MagickQuantizeImages GmMagickQuantizeImages
#define MagickQueryFontMetrics GmMagickQueryFontMetrics
#define MagickQueryFonts GmMagickQueryFonts
#define MagickQueryFormats GmMagickQueryFormats
#define MagickRadialBlurImage GmMagickRadialBlurImage
#define MagickRaiseImage GmMagickRaiseImage
#define MagickReadImageBlob GmMagickReadImageBlob
#define MagickReadImageFile GmMagickReadImageFile
#define MagickReadImage GmMagickReadImage
#define MagickReduceNoiseImage GmMagickReduceNoiseImage
#define MagickRelinquishMemory GmMagickRelinquishMemory
#define MagickRemoveImage GmMagickRemoveImage
#define MagickRemoveImageProfile GmMagickRemoveImageProfile
#define MagickResampleImage GmMagickResampleImage
#define MagickResetIterator GmMagickResetIterator
#define MagickResizeImage GmMagickResizeImage
#define MagickRollImage GmMagickRollImage
#define MagickRotateImage GmMagickRotateImage
#define MagickSampleImage GmMagickSampleImage
#define MagickScaleImage GmMagickScaleImage
#define MagickSeparateImageChannel GmMagickSeparateImageChannel
#define MagickSetCompressionQuality GmMagickSetCompressionQuality
#define MagickSetDepth GmMagickSetDepth
#define MagickSetFilename GmMagickSetFilename
#define MagickSetFormat GmMagickSetFormat
#define MagickSetImageAttribute GmMagickSetImageAttribute
#define MagickSetImageBackgroundColor GmMagickSetImageBackgroundColor
#define MagickSetImageBluePrimary GmMagickSetImageBluePrimary
#define MagickSetImageBorderColor GmMagickSetImageBorderColor
#define MagickSetImageChannelDepth GmMagickSetImageChannelDepth
#define MagickSetImageColormapColor GmMagickSetImageColormapColor
#define MagickSetImageColorspace GmMagickSetImageColorspace
#define MagickSetImageCompose GmMagickSetImageCompose
#define MagickSetImageCompression GmMagickSetImageCompression
#define MagickSetImageDelay GmMagickSetImageDelay
#define MagickSetImageDepth GmMagickSetImageDepth
#define MagickSetImageDispose GmMagickSetImageDispose
#define MagickSetImageFilename GmMagickSetImageFilename
#define MagickSetImageFormat GmMagickSetImageFormat
#define MagickSetImageFuzz GmMagickSetImageFuzz
#define MagickSetImageGamma GmMagickSetImageGamma
#define MagickSetImageGravity GmMagickSetImageGravity
#define MagickSetImage GmMagickSetImage
#define MagickSetImageGreenPrimary GmMagickSetImageGreenPrimary
#define MagickSetImageIndex GmMagickSetImageIndex
#define MagickSetImageInterlaceScheme GmMagickSetImageInterlaceScheme
#define MagickSetImageIterations GmMagickSetImageIterations
#define MagickSetImageMatte GmMagickSetImageMatte
#define MagickSetImageMatteColor GmMagickSetImageMatteColor
#define MagickSetImageOption GmMagickSetImageOption
#define MagickSetImagePage GmMagickSetImagePage
#define MagickSetImagePixels GmMagickSetImagePixels
#define MagickSetImageProfile GmMagickSetImageProfile
#define MagickSetImageRedPrimary GmMagickSetImageRedPrimary
#define MagickSetImageRenderingIntent GmMagickSetImageRenderingIntent
#define MagickSetImageResolution GmMagickSetImageResolution
#define MagickSetImageSavedType GmMagickSetImageSavedType
#define MagickSetImageScene GmMagickSetImageScene
#define MagickSetImageType GmMagickSetImageType
#define MagickSetImageUnits GmMagickSetImageUnits
#define MagickSetImageVirtualPixelMethod GmMagickSetImageVirtualPixelMethod
#define MagickSetImageWhitePoint GmMagickSetImageWhitePoint
#define MagickSetInterlaceScheme GmMagickSetInterlaceScheme
#define MagickSetPassphrase GmMagickSetPassphrase
#define MagickSetResolution GmMagickSetResolution
#define MagickSetResolutionUnits GmMagickSetResolutionUnits
#define MagickSetResourceLimit GmMagickSetResourceLimit
#define MagickSetSamplingFactors GmMagickSetSamplingFactors
#define MagickSetSize GmMagickSetSize
#define MagickSharpenImage GmMagickSharpenImage
#define MagickShaveImage GmMagickShaveImage
#define MagickShearImage GmMagickShearImage
#define MagickSolarizeImage GmMagickSolarizeImage
#define MagickSpreadImage GmMagickSpreadImage
#define MagickSteganoImage GmMagickSteganoImage
#define MagickStereoImage GmMagickStereoImage
#define MagickStripImage GmMagickStripImage
#define MagickSwirlImage GmMagickSwirlImage
#define MagickTextureImage GmMagickTextureImage
#define MagickThresholdImageChannel GmMagickThresholdImageChannel
#define MagickThresholdImage GmMagickThresholdImage
#define MagickTintImage GmMagickTintImage
#define MagickTransformImage GmMagickTransformImage
#define MagickTransparentImage GmMagickTransparentImage
#define MagickTrimImage GmMagickTrimImage
#define MagickUnsharpMaskImage GmMagickUnsharpMaskImage
#define MagickWaveImage GmMagickWaveImage
#define MagickWhiteThresholdImage GmMagickWhiteThresholdImage
#define MagickWriteImageBlob GmMagickWriteImageBlob
#define MagickWriteImageFile GmMagickWriteImageFile
#define MagickWriteImage GmMagickWriteImage
#define MagickWriteImagesFile GmMagickWriteImagesFile
#define MagickWriteImages GmMagickWriteImages
#define NewMagickWand GmNewMagickWand
#define NewPixelWand GmNewPixelWand
#define NewPixelWands GmNewPixelWands
#define PixelGetBlack GmPixelGetBlack
#define PixelGetBlackQuantum GmPixelGetBlackQuantum
#define PixelGetBlue GmPixelGetBlue
#define PixelGetBlueQuantum GmPixelGetBlueQuantum
#define PixelGetColorAsString GmPixelGetColorAsString
#define PixelGetColorCount GmPixelGetColorCount
#define PixelGetCyan GmPixelGetCyan
#define PixelGetCyanQuantum GmPixelGetCyanQuantum
#define PixelGetException GmPixelGetException
#define PixelGetGreen GmPixelGetGreen
#define PixelGetGreenQuantum GmPixelGetGreenQuantum
#define PixelGetMagenta GmPixelGetMagenta
#define PixelGetMagentaQuantum GmPixelGetMagentaQuantum
#define PixelGetOpacity GmPixelGetOpacity
#define PixelGetOpacityQuantum GmPixelGetOpacityQuantum
#define PixelGetQuantumColor GmPixelGetQuantumColor
#define PixelGetRed GmPixelGetRed
#define PixelGetRedQuantum GmPixelGetRedQuantum
#define PixelGetYellow GmPixelGetYellow
#define PixelGetYellowQuantum GmPixelGetYellowQuantum
#define PixelSetBlack GmPixelSetBlack
#define PixelSetBlackQuantum GmPixelSetBlackQuantum
#define PixelSetBlue GmPixelSetBlue
#define PixelSetBlueQuantum GmPixelSetBlueQuantum
#define PixelSetColorCount GmPixelSetColorCount
#define PixelSetColor GmPixelSetColor
#define PixelSetCyan GmPixelSetCyan
#define PixelSetCyanQuantum GmPixelSetCyanQuantum
#define PixelSetGreen GmPixelSetGreen
#define PixelSetGreenQuantum GmPixelSetGreenQuantum
#define PixelSetMagenta GmPixelSetMagenta
#define PixelSetMagentaQuantum GmPixelSetMagentaQuantum
#define PixelSetOpacity GmPixelSetOpacity
#define PixelSetOpacityQuantum GmPixelSetOpacityQuantum
#define PixelSetQuantumColor GmPixelSetQuantumColor
#define PixelSetRed GmPixelSetRed
#define PixelSetRedQuantum GmPixelSetRedQuantum
#define PixelSetYellow GmPixelSetYellow
#define PixelSetYellowQuantum GmPixelSetYellowQuantum
#define QueryMagickColor GmQueryMagickColor

#endif /* defined(PREFIX_MAGICK_SYMBOLS) */
#endif /* defined(_MAGICK_WAND_SYMBOLS_H) */
