/*
  Copyright (C) 2003 - 2015 GraphicsMagick Group
 
  This program is covered by multiple licenses, which are described in
  Copyright.txt. You should have received a copy of Copyright.txt with this
  package; otherwise see http://www.graphicsmagick.org/www/Copyright.html.

  Library symbol name-scoping support.

  Obtained via:

  nm -p magick/.libs/libGraphicsMagick.a | grep ' T ' | egrep -vi '(Gm)|(lt_)' | \
     egrep -v '(MagickError)|(MagickFatalError)|(MagickWarning)|(ThrowException)' | \
     awk '{ printf("#define %s Gm%s\n", $3, $3); }' | sort

*/

#if !defined(_MAGICK_SYMBOLS_H)
#define _MAGICK_SYMBOLS_H

#if defined(PREFIX_MAGICK_SYMBOLS)

#define AccessCacheViewPixels GmAccessCacheViewPixels
#define AccessDefaultCacheView GmAccessDefaultCacheView
#define AccessDefinition GmAccessDefinition
#define AccessImmutableIndexes GmAccessImmutableIndexes
#define AccessMutableIndexes GmAccessMutableIndexes
#define AccessMutablePixels GmAccessMutablePixels
#define AccessThreadViewData GmAccessThreadViewData
#define AccessThreadViewDataById GmAccessThreadViewDataById
#define AcquireCacheView GmAcquireCacheView
#define AcquireCacheViewIndexes GmAcquireCacheViewIndexes
#define AcquireCacheViewPixels GmAcquireCacheViewPixels
#define AcquireImagePixels GmAcquireImagePixels
#define AcquireMagickRandomKernel GmAcquireMagickRandomKernel
#define AcquireMagickResource GmAcquireMagickResource
#define AcquireMemory GmAcquireMemory
#define AcquireOneCacheViewPixel GmAcquireOneCacheViewPixel
#define AcquireOnePixel GmAcquireOnePixel
#define AcquireOnePixelByReference GmAcquireOnePixelByReference
#define AcquireSemaphoreInfo GmAcquireSemaphoreInfo
#define AcquireString GmAcquireString
#define AcquireTemporaryFileDescriptor GmAcquireTemporaryFileDescriptor
#define AcquireTemporaryFileName GmAcquireTemporaryFileName
#define AcquireTemporaryFileStream GmAcquireTemporaryFileStream
#define AdaptiveThresholdImage GmAdaptiveThresholdImage
#define AddDefinition GmAddDefinition
#define AddDefinitions GmAddDefinitions
#define AddNoiseImage GmAddNoiseImage
#define AddNoiseImageChannel GmAddNoiseImageChannel
#define AffineTransformImage GmAffineTransformImage
#define AllocateImage GmAllocateImage
#define AllocateImageColormap GmAllocateImageColormap
#define AllocateImageProfileIterator GmAllocateImageProfileIterator
#define AllocateNextImage GmAllocateNextImage
#define AllocateSemaphoreInfo GmAllocateSemaphoreInfo
#define AllocateString GmAllocateString
#define AllocateThreadViewDataArray GmAllocateThreadViewDataArray
#define AllocateThreadViewDataSet GmAllocateThreadViewDataSet
#define AllocateThreadViewSet GmAllocateThreadViewSet
#define AnalyzeImage GmAnalyzeImage
#define AnimateImageCommand GmAnimateImageCommand
#define AnimateImages GmAnimateImages
#define AnnotateImage GmAnnotateImage
#define AppendImageFormat GmAppendImageFormat
#define AppendImageProfile GmAppendImageProfile
#define AppendImageToList GmAppendImageToList
#define AppendImages GmAppendImages
#define Ascii85Encode GmAscii85Encode
#define Ascii85Flush GmAscii85Flush
#define Ascii85Initialize GmAscii85Initialize
#define Ascii85WriteByteHook GmAscii85WriteByteHook
#define AssignThreadViewData GmAssignThreadViewData
#define AttachBlob GmAttachBlob
#define AutoOrientImage GmAutoOrientImage
#define AverageImages GmAverageImages
#define BackgroundColor GmBackgroundColor
#define Base64Decode GmBase64Decode
#define Base64Encode GmBase64Encode
#define BenchmarkImageCommand GmBenchmarkImageCommand
#define BlackThresholdImage GmBlackThresholdImage
#define BlobIsSeekable GmBlobIsSeekable
#define BlobModeToString GmBlobModeToString
#define BlobReserveSize GmBlobReserveSize
#define BlobToFile GmBlobToFile
#define BlobToImage GmBlobToImage
#define BlobWriteByteHook GmBlobWriteByteHook
#define BlurImage GmBlurImage
#define BlurImageChannel GmBlurImageChannel
#define BorderColor GmBorderColor
#define BorderImage GmBorderImage
#define CatchException GmCatchException
#define CatchImageException GmCatchImageException
#define CdlImage GmCdlImage
#define ChannelImage GmChannelImage
#define ChannelThresholdImage GmChannelThresholdImage
#define ChannelTypeToString GmChannelTypeToString
#define CharcoalImage GmCharcoalImage
#define CheckImagePixelLimits GmCheckImagePixelLimits
#define ChopImage GmChopImage
#define ClassTypeToString GmClassTypeToString
#define ClipImage GmClipImage
#define ClipPathImage GmClipPathImage
#define CloneBlobInfo GmCloneBlobInfo
#define CloneDrawInfo GmCloneDrawInfo
#define CloneImage GmCloneImage
#define CloneImageAttributes GmCloneImageAttributes
#define CloneImageInfo GmCloneImageInfo
#define CloneImageList GmCloneImageList
#define CloneMemory GmCloneMemory
#define CloneMontageInfo GmCloneMontageInfo
#define CloneQuantizeInfo GmCloneQuantizeInfo
#define CloneString GmCloneString
#define CloseBlob GmCloseBlob
#define CloseCacheView GmCloseCacheView
#define CoalesceImages GmCoalesceImages
#define ColorFloodfillImage GmColorFloodfillImage
#define ColorMatrixImage GmColorMatrixImage
#define ColorizeImage GmColorizeImage
#define ColorspaceTypeToString GmColorspaceTypeToString
#define CompareImageCommand GmCompareImageCommand
#define CompositeImage GmCompositeImage
#define CompositeImageCommand GmCompositeImageCommand
#define CompositeImageRegion GmCompositeImageRegion
#define CompositeOperatorToString GmCompositeOperatorToString
#define CompressImageColormap GmCompressImageColormap
#define CompressionTypeToString GmCompressionTypeToString
#define ConcatenateString GmConcatenateString
#define ConfirmAccessModeToString GmConfirmAccessModeToString
#define ConjureImageCommand GmConjureImageCommand
#define ConstituteImage GmConstituteImage
#define ConstituteTextureImage GmConstituteTextureImage
#define ContinueTimer GmContinueTimer
#define Contrast GmContrast
#define ContrastImage GmContrastImage
#define ConvertImageCommand GmConvertImageCommand
#define ConvolveImage GmConvolveImage
#define CopyException GmCopyException
#define CropImage GmCropImage
#define CycleColormapImage GmCycleColormapImage
#define DCM_SetRescaling GmDCM_SetRescaling
#define DeallocateImageProfileIterator GmDeallocateImageProfileIterator
#define DeconstructImages GmDeconstructImages
#define DefaultTileFrame GmDefaultTileFrame
#define DefaultTileGeometry GmDefaultTileGeometry
#define DefaultTileLabel GmDefaultTileLabel
#define DefineClientName GmDefineClientName
#define DefineClientPathAndName GmDefineClientPathAndName
#define DeleteImageFromList GmDeleteImageFromList
#define DeleteImageProfile GmDeleteImageProfile
#define DeleteMagickRegistry GmDeleteMagickRegistry
#define DescribeImage GmDescribeImage
#define DespeckleImage GmDespeckleImage
#define DestroyBlob GmDestroyBlob
#define DestroyBlobInfo GmDestroyBlobInfo
#define DestroyCacheInfo GmDestroyCacheInfo
#define DestroyColorInfo GmDestroyColorInfo
#define DestroyConstitute GmDestroyConstitute
#define DestroyDelegateInfo GmDestroyDelegateInfo
#define DestroyDrawInfo GmDestroyDrawInfo
#define DestroyExceptionInfo GmDestroyExceptionInfo
#define DestroyImage GmDestroyImage
#define DestroyImageAttributes GmDestroyImageAttributes
#define DestroyImageInfo GmDestroyImageInfo
#define DestroyImageList GmDestroyImageList
#define DestroyImagePixels GmDestroyImagePixels
#define DestroyLogInfo GmDestroyLogInfo
#define DestroyMagicInfo GmDestroyMagicInfo
#define DestroyMagick GmDestroyMagick
#define DestroyMagickRandomGenerator GmDestroyMagickRandomGenerator
#define DestroyMagickRegistry GmDestroyMagickRegistry
#define DestroyMagickResources GmDestroyMagickResources
#define DestroyMontageInfo GmDestroyMontageInfo
#define DestroyQuantizeInfo GmDestroyQuantizeInfo
#define DestroySemaphore GmDestroySemaphore
#define DestroySemaphoreInfo GmDestroySemaphoreInfo
#define DestroyTemporaryFiles GmDestroyTemporaryFiles
#define DestroyThreadViewDataSet GmDestroyThreadViewDataSet
#define DestroyThreadViewSet GmDestroyThreadViewSet
#define DestroyTypeInfo GmDestroyTypeInfo
#define DetachBlob GmDetachBlob
#define DifferenceImage GmDifferenceImage
#define DispatchImage GmDispatchImage
#define DisplayImageCommand GmDisplayImageCommand
#define DisplayImages GmDisplayImages
#define DrawAffine GmDrawAffine
#define DrawAffineImage GmDrawAffineImage
#define DrawAllocateContext GmDrawAllocateContext
#define DrawAnnotation GmDrawAnnotation
#define DrawArc GmDrawArc
#define DrawBezier GmDrawBezier
#define DrawCircle GmDrawCircle
#define DrawClipPath GmDrawClipPath
#define DrawColor GmDrawColor
#define DrawComment GmDrawComment
#define DrawComposite GmDrawComposite
#define DrawDestroyContext GmDrawDestroyContext
#define DrawEllipse GmDrawEllipse
#define DrawGetClipPath GmDrawGetClipPath
#define DrawGetClipRule GmDrawGetClipRule
#define DrawGetClipUnits GmDrawGetClipUnits
#define DrawGetFillColor GmDrawGetFillColor
#define DrawGetFillOpacity GmDrawGetFillOpacity
#define DrawGetFillRule GmDrawGetFillRule
#define DrawGetFont GmDrawGetFont
#define DrawGetFontFamily GmDrawGetFontFamily
#define DrawGetFontSize GmDrawGetFontSize
#define DrawGetFontStretch GmDrawGetFontStretch
#define DrawGetFontStyle GmDrawGetFontStyle
#define DrawGetFontWeight GmDrawGetFontWeight
#define DrawGetGravity GmDrawGetGravity
#define DrawGetStrokeAntialias GmDrawGetStrokeAntialias
#define DrawGetStrokeColor GmDrawGetStrokeColor
#define DrawGetStrokeDashArray GmDrawGetStrokeDashArray
#define DrawGetStrokeDashOffset GmDrawGetStrokeDashOffset
#define DrawGetStrokeLineCap GmDrawGetStrokeLineCap
#define DrawGetStrokeLineJoin GmDrawGetStrokeLineJoin
#define DrawGetStrokeMiterLimit GmDrawGetStrokeMiterLimit
#define DrawGetStrokeOpacity GmDrawGetStrokeOpacity
#define DrawGetStrokeWidth GmDrawGetStrokeWidth
#define DrawGetTextAntialias GmDrawGetTextAntialias
#define DrawGetTextDecoration GmDrawGetTextDecoration
#define DrawGetTextEncoding GmDrawGetTextEncoding
#define DrawGetTextUnderColor GmDrawGetTextUnderColor
#define DrawImage GmDrawImage
#define DrawLine GmDrawLine
#define DrawMatte GmDrawMatte
#define DrawPathClose GmDrawPathClose
#define DrawPathCurveToAbsolute GmDrawPathCurveToAbsolute
#define DrawPathCurveToQuadraticBezierAbsolute GmDrawPathCurveToQuadraticBezierAbsolute
#define DrawPathCurveToQuadraticBezierRelative GmDrawPathCurveToQuadraticBezierRelative
#define DrawPathCurveToQuadraticBezierSmoothAbsolute GmDrawPathCurveToQuadraticBezierSmoothAbsolute
#define DrawPathCurveToQuadraticBezierSmoothRelative GmDrawPathCurveToQuadraticBezierSmoothRelative
#define DrawPathCurveToRelative GmDrawPathCurveToRelative
#define DrawPathCurveToSmoothAbsolute GmDrawPathCurveToSmoothAbsolute
#define DrawPathCurveToSmoothRelative GmDrawPathCurveToSmoothRelative
#define DrawPathEllipticArcAbsolute GmDrawPathEllipticArcAbsolute
#define DrawPathEllipticArcRelative GmDrawPathEllipticArcRelative
#define DrawPathFinish GmDrawPathFinish
#define DrawPathLineToAbsolute GmDrawPathLineToAbsolute
#define DrawPathLineToHorizontalAbsolute GmDrawPathLineToHorizontalAbsolute
#define DrawPathLineToHorizontalRelative GmDrawPathLineToHorizontalRelative
#define DrawPathLineToRelative GmDrawPathLineToRelative
#define DrawPathLineToVerticalAbsolute GmDrawPathLineToVerticalAbsolute
#define DrawPathLineToVerticalRelative GmDrawPathLineToVerticalRelative
#define DrawPathMoveToAbsolute GmDrawPathMoveToAbsolute
#define DrawPathMoveToRelative GmDrawPathMoveToRelative
#define DrawPathStart GmDrawPathStart
#define DrawPatternPath GmDrawPatternPath
#define DrawPeekGraphicContext GmDrawPeekGraphicContext
#define DrawPoint GmDrawPoint
#define DrawPolygon GmDrawPolygon
#define DrawPolyline GmDrawPolyline
#define DrawPopClipPath GmDrawPopClipPath
#define DrawPopDefs GmDrawPopDefs
#define DrawPopGraphicContext GmDrawPopGraphicContext
#define DrawPopPattern GmDrawPopPattern
#define DrawPushClipPath GmDrawPushClipPath
#define DrawPushDefs GmDrawPushDefs
#define DrawPushGraphicContext GmDrawPushGraphicContext
#define DrawPushPattern GmDrawPushPattern
#define DrawRectangle GmDrawRectangle
#define DrawRender GmDrawRender
#define DrawRotate GmDrawRotate
#define DrawRoundRectangle GmDrawRoundRectangle
#define DrawScale GmDrawScale
#define DrawSetClipPath GmDrawSetClipPath
#define DrawSetClipRule GmDrawSetClipRule
#define DrawSetClipUnits GmDrawSetClipUnits
#define DrawSetFillColor GmDrawSetFillColor
#define DrawSetFillColorString GmDrawSetFillColorString
#define DrawSetFillOpacity GmDrawSetFillOpacity
#define DrawSetFillPatternURL GmDrawSetFillPatternURL
#define DrawSetFillRule GmDrawSetFillRule
#define DrawSetFont GmDrawSetFont
#define DrawSetFontFamily GmDrawSetFontFamily
#define DrawSetFontSize GmDrawSetFontSize
#define DrawSetFontStretch GmDrawSetFontStretch
#define DrawSetFontStyle GmDrawSetFontStyle
#define DrawSetFontWeight GmDrawSetFontWeight
#define DrawSetGravity GmDrawSetGravity
#define DrawSetStrokeAntialias GmDrawSetStrokeAntialias
#define DrawSetStrokeColor GmDrawSetStrokeColor
#define DrawSetStrokeColorString GmDrawSetStrokeColorString
#define DrawSetStrokeDashArray GmDrawSetStrokeDashArray
#define DrawSetStrokeDashOffset GmDrawSetStrokeDashOffset
#define DrawSetStrokeLineCap GmDrawSetStrokeLineCap
#define DrawSetStrokeLineJoin GmDrawSetStrokeLineJoin
#define DrawSetStrokeMiterLimit GmDrawSetStrokeMiterLimit
#define DrawSetStrokeOpacity GmDrawSetStrokeOpacity
#define DrawSetStrokePatternURL GmDrawSetStrokePatternURL
#define DrawSetStrokeWidth GmDrawSetStrokeWidth
#define DrawSetTextAntialias GmDrawSetTextAntialias
#define DrawSetTextDecoration GmDrawSetTextDecoration
#define DrawSetTextEncoding GmDrawSetTextEncoding
#define DrawSetTextUnderColor GmDrawSetTextUnderColor
#define DrawSetTextUnderColorString GmDrawSetTextUnderColorString
#define DrawSetViewbox GmDrawSetViewbox
#define DrawSkewX GmDrawSkewX
#define DrawSkewY GmDrawSkewY
#define DrawTranslate GmDrawTranslate
#define EOFBlob GmEOFBlob
#define EdgeImage GmEdgeImage
#define EmbossImage GmEmbossImage
#define EndianTypeToString GmEndianTypeToString
#define EnhanceImage GmEnhanceImage
#define EqualizeImage GmEqualizeImage
#define EscapeString GmEscapeString
#define ExecuteModuleProcess GmExecuteModuleProcess
#define ExpandAffine GmExpandAffine
#define ExpandFilename GmExpandFilename
#define ExpandFilenames GmExpandFilenames
#define ExportImageChannel GmExportImageChannel
#define ExportImagePixelArea GmExportImagePixelArea
#define ExportPixelAreaOptionsInit GmExportPixelAreaOptionsInit
#define ExportViewPixelArea GmExportViewPixelArea
#define ExtentImage GmExtentImage
#define FileToBlob GmFileToBlob
#define FinalizeSignature GmFinalizeSignature
#define FlattenImages GmFlattenImages
#define FlipImage GmFlipImage
#define FlopImage GmFlopImage
#define ForegroundColor GmForegroundColor
#define FormatSize GmFormatSize
#define FormatString GmFormatString
#define FormatStringList GmFormatStringList
#define FrameImage GmFrameImage
#define FuzzyColorMatch GmFuzzyColorMatch
#define GammaImage GmGammaImage
#define GaussianBlurImage GmGaussianBlurImage
#define GaussianBlurImageChannel GmGaussianBlurImageChannel
#define GenerateDifferentialNoise GmGenerateDifferentialNoise
#define GenerateNoise GmGenerateNoise
#define GetBlobFileHandle GmGetBlobFileHandle
#define GetBlobInfo GmGetBlobInfo
#define GetBlobIsOpen GmGetBlobIsOpen
#define GetBlobSize GmGetBlobSize
#define GetBlobStatus GmGetBlobStatus
#define GetBlobStreamData GmGetBlobStreamData
#define GetBlobTemporary GmGetBlobTemporary
#define GetCacheInfo GmGetCacheInfo
#define GetCacheView GmGetCacheView
#define GetCacheViewArea GmGetCacheViewArea
#define GetCacheViewImage GmGetCacheViewImage
#define GetCacheViewIndexes GmGetCacheViewIndexes
#define GetCacheViewPixels GmGetCacheViewPixels
#define GetCacheViewRegion GmGetCacheViewRegion
#define GetClientFilename GmGetClientFilename
#define GetClientName GmGetClientName
#define GetClientPath GmGetClientPath
#define GetColorHistogram GmGetColorHistogram
#define GetColorInfo GmGetColorInfo
#define GetColorInfoArray GmGetColorInfoArray
#define GetColorList GmGetColorList
#define GetColorTuple GmGetColorTuple
#define GetConfigureBlob GmGetConfigureBlob
#define GetDelegateCommand GmGetDelegateCommand
#define GetDelegateInfo GmGetDelegateInfo
#define GetDrawInfo GmGetDrawInfo
#define GetElapsedTime GmGetElapsedTime
#define GetExceptionInfo GmGetExceptionInfo
#define GetExecutionPath GmGetExecutionPath
#define GetExecutionPathUsingName GmGetExecutionPathUsingName
#define GetFirstImageInList GmGetFirstImageInList
#define GetGeometry GmGetGeometry
#define GetImageAttribute GmGetImageAttribute
#define GetImageBoundingBox GmGetImageBoundingBox
#define GetImageChannelDepth GmGetImageChannelDepth
#define GetImageChannelDifference GmGetImageChannelDifference
#define GetImageChannelDistortion GmGetImageChannelDistortion
#define GetImageCharacteristics GmGetImageCharacteristics
#define GetImageClipMask GmGetImageClipMask
#define GetImageClippingPathAttribute GmGetImageClippingPathAttribute
#define GetImageDepth GmGetImageDepth
#define GetImageDistortion GmGetImageDistortion
#define GetImageException GmGetImageException
#define GetImageFromList GmGetImageFromList
#define GetImageFromMagickRegistry GmGetImageFromMagickRegistry
#define GetImageGeometry GmGetImageGeometry
#define GetImageIndexInList GmGetImageIndexInList
#define GetImageInfo GmGetImageInfo
#define GetImageInfoAttribute GmGetImageInfoAttribute
#define GetImageListLength GmGetImageListLength
#define GetImageMagick GmGetImageMagick
#define GetImagePixels GmGetImagePixels
#define GetImagePixelsEx GmGetImagePixelsEx
#define GetImageProfile GmGetImageProfile
#define GetImageQuantizeError GmGetImageQuantizeError
#define GetImageStatistics GmGetImageStatistics
#define GetImageType GmGetImageType
#define GetImageVirtualPixelMethod GmGetImageVirtualPixelMethod
#define GetIndexes GmGetIndexes
#define GetLastImageInList GmGetLastImageInList
#define GetLocaleExceptionMessage GmGetLocaleExceptionMessage
#define GetLocaleMessage GmGetLocaleMessage
#define GetLocaleMessageFromID GmGetLocaleMessageFromID
#define GetMagickCopyright GmGetMagickCopyright
#define GetMagickDimension GmGetMagickDimension
#define GetMagickFileFormat GmGetMagickFileFormat
#define GetMagickGeometry GmGetMagickGeometry
#define GetMagickInfo GmGetMagickInfo
#define GetMagickInfoArray GmGetMagickInfoArray
#define GetMagickRegistry GmGetMagickRegistry
#define GetMagickResource GmGetMagickResource
#define GetMagickResourceLimit GmGetMagickResourceLimit
#define GetMagickVersion GmGetMagickVersion
#define GetMagickWebSite GmGetMagickWebSite
#define GetMontageInfo GmGetMontageInfo
#define GetNextImageInList GmGetNextImageInList
#define GetNumberColors GmGetNumberColors
#define GetOnePixel GmGetOnePixel
#define GetOptimalKernelWidth GmGetOptimalKernelWidth
#define GetOptimalKernelWidth1D GmGetOptimalKernelWidth1D
#define GetOptimalKernelWidth2D GmGetOptimalKernelWidth2D
#define GetPageGeometry GmGetPageGeometry
#define GetPathComponent GmGetPathComponent
#define GetPixelCacheArea GmGetPixelCacheArea
#define GetPixelCacheInCore GmGetPixelCacheInCore
#define GetPixelCachePresent GmGetPixelCachePresent
#define GetPixels GmGetPixels
#define GetPostscriptDelegateInfo GmGetPostscriptDelegateInfo
#define GetPreviousImageInList GmGetPreviousImageInList
#define GetQuantizeInfo GmGetQuantizeInfo
#define GetSignatureInfo GmGetSignatureInfo
#define GetThreadViewDataSetAllocatedViews GmGetThreadViewDataSetAllocatedViews
#define GetTimerInfo GmGetTimerInfo
#define GetTimerResolution GmGetTimerResolution
#define GetToken GmGetToken
#define GetTypeInfo GmGetTypeInfo
#define GetTypeInfoByFamily GmGetTypeInfoByFamily
#define GetTypeList GmGetTypeList
#define GetTypeMetrics GmGetTypeMetrics
#define GetUserTime GmGetUserTime
#define GlobExpression GmGlobExpression
#define GradientImage GmGradientImage
#define GrayscalePseudoClassImage GmGrayscalePseudoClassImage
#define HSLTransform GmHSLTransform
#define HWBTransform GmHWBTransform
#define HaldClutImage GmHaldClutImage
#define HighlightStyleToString GmHighlightStyleToString
#define HuffmanDecodeImage GmHuffmanDecodeImage
#define HuffmanEncode2Image GmHuffmanEncode2Image
#define HuffmanEncodeImage GmHuffmanEncodeImage
#define Hull GmHull
#define IdentifyImageCommand GmIdentifyImageCommand
#define IdentityAffine GmIdentityAffine
#define ImageListToArray GmImageListToArray
#define ImageToBlob GmImageToBlob
#define ImageToFile GmImageToFile
#define ImageToHuffman2DBlob GmImageToHuffman2DBlob
#define ImageToJPEGBlob GmImageToJPEGBlob
#define ImageTypeToString GmImageTypeToString
#define ImplodeImage GmImplodeImage
#define ImportImageChannel GmImportImageChannel
#define ImportImageChannelsMasked GmImportImageChannelsMasked
#define ImportImageCommand GmImportImageCommand
#define ImportImagePixelArea GmImportImagePixelArea
#define ImportPixelAreaOptionsInit GmImportPixelAreaOptionsInit
#define ImportViewPixelArea GmImportViewPixelArea
#define InitializeColorInfo GmInitializeColorInfo
#define InitializeConstitute GmInitializeConstitute
#define InitializeDelegateInfo GmInitializeDelegateInfo
#define InitializeDifferenceImageOptions GmInitializeDifferenceImageOptions
#define InitializeDifferenceStatistics GmInitializeDifferenceStatistics
#define InitializeLogInfo GmInitializeLogInfo
#define InitializeMagicInfo GmInitializeMagicInfo
#define InitializeMagick GmInitializeMagick
#define InitializeMagickClientPathAndName GmInitializeMagickClientPathAndName
#define InitializeMagickRandomGenerator GmInitializeMagickRandomGenerator
#define InitializeMagickRandomKernel GmInitializeMagickRandomKernel
#define InitializeMagickRegistry GmInitializeMagickRegistry
#define InitializeMagickResources GmInitializeMagickResources
#define InitializeMagickSignalHandlers GmInitializeMagickSignalHandlers
#define InitializePixelIteratorOptions GmInitializePixelIteratorOptions
#define InitializeSemaphore GmInitializeSemaphore
#define InitializeTemporaryFiles GmInitializeTemporaryFiles
#define InitializeTypeInfo GmInitializeTypeInfo
#define InsertImageInList GmInsertImageInList
#define InsertRowHDU GmInsertRowHDU
#define InterlaceTypeToString GmInterlaceTypeToString
#define InterpolateColor GmInterpolateColor
#define InterpolateViewColor GmInterpolateViewColor
#define InvokeDelegate GmInvokeDelegate
#define InvokePostscriptDelegate GmInvokePostscriptDelegate
#define IsAccessible GmIsAccessible
#define IsAccessibleAndNotEmpty GmIsAccessibleAndNotEmpty
#define IsAccessibleNoLogging GmIsAccessibleNoLogging
#define IsEventLogging GmIsEventLogging
#define IsGeometry GmIsGeometry
#define IsGlob GmIsGlob
#define IsGrayImage GmIsGrayImage
#define IsImagesEqual GmIsImagesEqual
#define IsMagickConflict GmIsMagickConflict
#define IsMonochromeImage GmIsMonochromeImage
#define IsOpaqueImage GmIsOpaqueImage
#define IsPaletteImage GmIsPaletteImage
#define IsSubimage GmIsSubimage
#define IsTaintImage GmIsTaintImage
#define IsWriteable GmIsWriteable
#define LZWEncode2Image GmLZWEncode2Image
#define LZWEncodeImage GmLZWEncodeImage
#define LevelImage GmLevelImage
#define LevelImageChannel GmLevelImageChannel
#define LiberateMagickResource GmLiberateMagickResource
#define LiberateMemory GmLiberateMemory
#define LiberateSemaphoreInfo GmLiberateSemaphoreInfo
#define LiberateTemporaryFile GmLiberateTemporaryFile
#define ListColorInfo GmListColorInfo
#define ListDelegateInfo GmListDelegateInfo
#define ListFiles GmListFiles
#define ListMagicInfo GmListMagicInfo
#define ListMagickInfo GmListMagickInfo
#define ListMagickResourceInfo GmListMagickResourceInfo
#define ListModuleMap GmListModuleMap
#define ListTypeInfo GmListTypeInfo
#define LocaleCompare GmLocaleCompare
#define LocaleLower GmLocaleLower
#define LocaleNCompare GmLocaleNCompare
#define LocaleUpper GmLocaleUpper
#define LockSemaphoreInfo GmLockSemaphoreInfo
#define LogMagickEvent GmLogMagickEvent
#define LogMagickEventList GmLogMagickEventList
#define MatteColor GmMatteColor
#define MSBOrderLong GmMSBOrderLong
#define MSBOrderShort GmMSBOrderShort
#define MagickAllocFunctions GmMagickAllocFunctions
#define MagickArraySize GmMagickArraySize
#define MagickBitStreamInitializeRead GmMagickBitStreamInitializeRead
#define MagickBitStreamInitializeWrite GmMagickBitStreamInitializeWrite
#define MagickBitStreamMSBRead GmMagickBitStreamMSBRead
#define MagickBitStreamMSBWrite GmMagickBitStreamMSBWrite
#define MagickCloneMemory GmMagickCloneMemory
#define MagickCommand GmMagickCommand
#define MagickCompositeImageUnderColor GmMagickCompositeImageUnderColor
#define MagickConfirmAccess GmMagickConfirmAccess
#define MagickConstrainColormapIndex GmMagickConstrainColormapIndex
#define MagickCreateDirectoryPath GmMagickCreateDirectoryPath
#define MagickDestroyCommandInfo GmMagickDestroyCommandInfo
#define MagickFindRawImageMinMax GmMagickFindRawImageMinMax
#define MagickFormatString GmMagickFormatString
#define MagickFormatStringList GmMagickFormatStringList
#define MagickFree GmMagickFree
#define MagickFreeAligned GmMagickFreeAligned
#define MagickFmin GmMagickFmin
#define MagickFmax GmMagickFmax
#define MagickGetBitRevTable GmMagickGetBitRevTable
#define MagickGetFileSystemBlockSize GmMagickGetFileSystemBlockSize
#define MagickGetMMUPageSize GmMagickGetMMUPageSize
#define MagickGetQuantumSamplesPerPixel GmMagickGetQuantumSamplesPerPixel
#define MagickInitializeCommandInfo GmMagickInitializeCommandInfo
#define MagickIsTrue GmMagickIsTrue
#define MagickMalloc GmMagickMalloc
#define MagickMallocAligned GmMagickMallocAligned
#define MagickMallocAlignedArray GmMagickMallocAlignedArray
#define MagickMallocArray GmMagickMallocArray
#define MagickMallocCleared GmMagickMallocCleared
#define MagickMapAccessEntry GmMagickMapAccessEntry
#define MagickMapAddEntry GmMagickMapAddEntry
#define MagickMapAllocateIterator GmMagickMapAllocateIterator
#define MagickMapAllocateMap GmMagickMapAllocateMap
#define MagickMapClearMap GmMagickMapClearMap
#define MagickMapCloneMap GmMagickMapCloneMap
#define MagickMapCopyBlob GmMagickMapCopyBlob
#define MagickMapCopyString GmMagickMapCopyString
#define MagickMapDeallocateBlob GmMagickMapDeallocateBlob
#define MagickMapDeallocateIterator GmMagickMapDeallocateIterator
#define MagickMapDeallocateMap GmMagickMapDeallocateMap
#define MagickMapDeallocateString GmMagickMapDeallocateString
#define MagickMapDereferenceIterator GmMagickMapDereferenceIterator
#define MagickMapIterateNext GmMagickMapIterateNext
#define MagickMapIteratePrevious GmMagickMapIteratePrevious
#define MagickMapIterateToBack GmMagickMapIterateToBack
#define MagickMapIterateToFront GmMagickMapIterateToFront
#define MagickMapRemoveEntry GmMagickMapRemoveEntry
#define MagickMonitor GmMagickMonitor
#define MagickMonitorFormatted GmMagickMonitorFormatted
#define MagickRandNewSeed GmMagickRandNewSeed
#define MagickRandReentrant GmMagickRandReentrant
#define MagickRandomInteger GmMagickRandomInteger
#define MagickRandomReal GmMagickRandomReal
#define MagickRealloc GmMagickRealloc
#define MagickReverseBits GmMagickReverseBits
#define MagickSceneFileName GmMagickSceneFileName
#define MagickSetConfirmAccessHandler GmMagickSetConfirmAccessHandler
#define MagickSetFileSystemBlockSize GmMagickSetFileSystemBlockSize
#define MagickSizeStrToInt64 GmMagickSizeStrToInt64
#define MagickSpawnVP GmMagickSpawnVP
#define MagickStripSpacesFromString GmMagickStripSpacesFromString
#define MagickStrlCat GmMagickStrlCat
#define MagickStrlCpy GmMagickStrlCpy
#define MagickStrlCpyTrunc GmMagickStrlCpyTrunc
#define MagickSwabArrayOfDouble GmMagickSwabArrayOfDouble
#define MagickSwabArrayOfFloat GmMagickSwabArrayOfFloat
#define MagickSwabArrayOfUInt16 GmMagickSwabArrayOfUInt16
#define MagickSwabArrayOfUInt32 GmMagickSwabArrayOfUInt32
#define MagickSwabDouble GmMagickSwabDouble
#define MagickSwabFloat GmMagickSwabFloat
#define MagickSwabUInt16 GmMagickSwabUInt16
#define MagickSwabUInt32 GmMagickSwabUInt32
#define MagickToMime GmMagickToMime
#define MagickTsdGetSpecific GmMagickTsdGetSpecific
#define MagickTsdKeyCreate GmMagickTsdKeyCreate
#define MagickTsdKeyCreate2 GmMagickTsdKeyCreate2
#define MagickTsdKeyDelete GmMagickTsdKeyDelete
#define MagickTsdSetSpecific GmMagickTsdSetSpecific
#define MagickWordStreamInitializeRead GmMagickWordStreamInitializeRead
#define MagickWordStreamInitializeWrite GmMagickWordStreamInitializeWrite
#define MagickWordStreamLSBRead GmMagickWordStreamLSBRead
#define MagickWordStreamLSBWrite GmMagickWordStreamLSBWrite
#define MagickWordStreamLSBWriteFlush GmMagickWordStreamLSBWriteFlush
#define MagickXAnimateBackgroundImage GmMagickXAnimateBackgroundImage
#define MagickXAnimateImages GmMagickXAnimateImages
#define MagickXAnnotateImage GmMagickXAnnotateImage
#define MagickXBestFont GmMagickXBestFont
#define MagickXBestIconSize GmMagickXBestIconSize
#define MagickXBestPixel GmMagickXBestPixel
#define MagickXBestVisualInfo GmMagickXBestVisualInfo
#define MagickXCheckRefreshWindows GmMagickXCheckRefreshWindows
#define MagickXClientMessage GmMagickXClientMessage
#define MagickXColorBrowserWidget GmMagickXColorBrowserWidget
#define MagickXCommandWidget GmMagickXCommandWidget
#define MagickXConfigureImageColormap GmMagickXConfigureImageColormap
#define MagickXConfirmWidget GmMagickXConfirmWidget
#define MagickXConstrainWindowPosition GmMagickXConstrainWindowPosition
#define MagickXDelay GmMagickXDelay
#define MagickXDestroyResourceInfo GmMagickXDestroyResourceInfo
#define MagickXDestroyWindowColors GmMagickXDestroyWindowColors
#define MagickXDestroyX11Resources GmMagickXDestroyX11Resources
#define MagickXDestroyXWindowInfo GmMagickXDestroyXWindowInfo
#define MagickXDestroyXWindows GmMagickXDestroyXWindows
#define MagickXDialogWidget GmMagickXDialogWidget
#define MagickXDisplayBackgroundImage GmMagickXDisplayBackgroundImage
#define MagickXDisplayImage GmMagickXDisplayImage
#define MagickXDisplayImageInfo GmMagickXDisplayImageInfo
#define MagickXDrawImage GmMagickXDrawImage
#define MagickXError GmMagickXError
#define MagickXFileBrowserWidget GmMagickXFileBrowserWidget
#define MagickXFontBrowserWidget GmMagickXFontBrowserWidget
#define MagickXFreeResources GmMagickXFreeResources
#define MagickXFreeStandardColormap GmMagickXFreeStandardColormap
#define MagickXGetAnnotateInfo GmMagickXGetAnnotateInfo
#define MagickXGetImportInfo GmMagickXGetImportInfo
#define MagickXGetMapInfo GmMagickXGetMapInfo
#define MagickXGetPixelPacket GmMagickXGetPixelPacket
#define MagickXGetResourceClass GmMagickXGetResourceClass
#define MagickXGetResourceDatabase GmMagickXGetResourceDatabase
#define MagickXGetResourceInfo GmMagickXGetResourceInfo
#define MagickXGetResourceInstance GmMagickXGetResourceInstance
#define MagickXGetScreenDensity GmMagickXGetScreenDensity
#define MagickXGetWindowColor GmMagickXGetWindowColor
#define MagickXGetWindowInfo GmMagickXGetWindowInfo
#define MagickXHighlightEllipse GmMagickXHighlightEllipse
#define MagickXHighlightLine GmMagickXHighlightLine
#define MagickXHighlightRectangle GmMagickXHighlightRectangle
#define MagickXImportImage GmMagickXImportImage
#define MagickXInfoWidget GmMagickXInfoWidget
#define MagickXInitializeWindows GmMagickXInitializeWindows
#define MagickXListBrowserWidget GmMagickXListBrowserWidget
#define MagickXMagickMonitor GmMagickXMagickMonitor
#define MagickXMakeCursor GmMagickXMakeCursor
#define MagickXMakeImage GmMagickXMakeImage
#define MagickXMakeMagnifyImage GmMagickXMakeMagnifyImage
#define MagickXMakeStandardColormap GmMagickXMakeStandardColormap
#define MagickXMakeWindow GmMagickXMakeWindow
#define MagickXMenuWidget GmMagickXMenuWidget
#define MagickXMonitorWidget GmMagickXMonitorWidget
#define MagickXNoticeWidget GmMagickXNoticeWidget
#define MagickXPreferencesWidget GmMagickXPreferencesWidget
#define MagickXQueryColorDatabase GmMagickXQueryColorDatabase
#define MagickXQueryPosition GmMagickXQueryPosition
#define MagickXRefreshWindow GmMagickXRefreshWindow
#define MagickXRemoteCommand GmMagickXRemoteCommand
#define MagickXRetainWindowColors GmMagickXRetainWindowColors
#define MagickXSetCursorState GmMagickXSetCursorState
#define MagickXSetWindows GmMagickXSetWindows
#define MagickXSignalHandler GmMagickXSignalHandler
#define MagickXTextViewWidget GmMagickXTextViewWidget
#define MagickXUserPreferences GmMagickXUserPreferences
#define MagickXWarning GmMagickXWarning
#define MagickXWindowByID GmMagickXWindowByID
#define MagickXWindowByName GmMagickXWindowByName
#define MagickXWindowByProperty GmMagickXWindowByProperty
#define MagnifyImage GmMagnifyImage
#define MapBlob GmMapBlob
#define MapImage GmMapImage
#define MapImages GmMapImages
#define MapModeToString GmMapModeToString
#define MatteFloodfillImage GmMatteFloodfillImage
#define MedianFilterImage GmMedianFilterImage
#define MetricTypeToString GmMetricTypeToString
#define MinifyImage GmMinifyImage
#define ModifyCache GmModifyCache
#define ModifyImage GmModifyImage
#define Modulate GmModulate
#define ModulateImage GmModulateImage
#define MogrifyImage GmMogrifyImage
#define MogrifyImageCommand GmMogrifyImageCommand
#define MogrifyImages GmMogrifyImages
#define MontageImageCommand GmMontageImageCommand
#define MontageImages GmMontageImages
#define MorphImages GmMorphImages
#define MosaicImages GmMosaicImages
#define MotionBlurImage GmMotionBlurImage
#define MultilineCensus GmMultilineCensus
#define NegateImage GmNegateImage
#define NewImageList GmNewImageList
#define NextImageProfile GmNextImageProfile
#define NoiseTypeToString GmNoiseTypeToString
#define NormalizeImage GmNormalizeImage
#define OilPaintImage GmOilPaintImage
#define OpaqueImage GmOpaqueImage
#define OpenBlob GmOpenBlob
#define OpenCacheView GmOpenCacheView
#define OrderedDitherImage GmOrderedDitherImage
#define OrientationTypeToString GmOrientationTypeToString
#define PackbitsEncode2Image GmPackbitsEncode2Image
#define PackbitsEncodeImage GmPackbitsEncodeImage
#define PanicDestroyMagick GmPanicDestroyMagick
#define PersistCache GmPersistCache
#define PingBlob GmPingBlob
#define PingImage GmPingImage
#define PixelIterateDualModify GmPixelIterateDualModify
#define PixelIterateDualNew GmPixelIterateDualNew
#define PixelIterateDualRead GmPixelIterateDualRead
#define PixelIterateMonoModify GmPixelIterateMonoModify
#define PixelIterateMonoRead GmPixelIterateMonoRead
#define PixelIterateTripleModify GmPixelIterateTripleModify
#define PixelIterateTripleNew GmPixelIterateTripleNew
#define PlasmaImage GmPlasmaImage
#define PopImagePixels GmPopImagePixels
#define PrependImageToList GmPrependImageToList
#define ProfileImage GmProfileImage
#define PSDensityGeometry GmPSDensityGeometry
#define PSPageGeometry GmPSPageGeometry
#define PurgeTemporaryFiles GmPurgeTemporaryFiles
#define PurgeTemporaryFilesAsyncSafe GmPurgeTemporaryFilesAsyncSafe
#define PushImagePixels GmPushImagePixels
#define QuantizeImage GmQuantizeImage
#define QuantizeImages GmQuantizeImages
#define QuantumOperatorImage GmQuantumOperatorImage
#define QuantumOperatorImageMultivalue GmQuantumOperatorImageMultivalue
#define QuantumOperatorRegionImage GmQuantumOperatorRegionImage
#define QuantumOperatorToString GmQuantumOperatorToString
#define QuantumSampleTypeToString GmQuantumSampleTypeToString
#define QuantumTypeToString GmQuantumTypeToString
#define QueryColorDatabase GmQueryColorDatabase
#define QueryColorname GmQueryColorname
#define RGBTransformImage GmRGBTransformImage
#define RaiseImage GmRaiseImage
#define RandomChannelThresholdImage GmRandomChannelThresholdImage
#define ReacquireMemory GmReacquireMemory
#define ReadBlob GmReadBlob
#define ReadBlobByte GmReadBlobByte
#define ReadBlobByteFromBuffer GmReadBlobByteFromBuffer
#define ReadBlobLSBDouble GmReadBlobLSBDouble
#define ReadBlobLSBDoubles GmReadBlobLSBDoubles
#define ReadBlobLSBFloat GmReadBlobLSBFloat
#define ReadBlobLSBFloats GmReadBlobLSBFloats
#define ReadBlobLSBLong GmReadBlobLSBLong
#define ReadBlobLSBLongs GmReadBlobLSBLongs
#define ReadBlobLSBShort GmReadBlobLSBShort
#define ReadBlobLSBShortFromBuffer GmReadBlobLSBShortFromBuffer
#define ReadBlobLSBShorts GmReadBlobLSBShorts
#define ReadBlobMSBDouble GmReadBlobMSBDouble
#define ReadBlobMSBDoubles GmReadBlobMSBDoubles
#define ReadBlobMSBFloat GmReadBlobMSBFloat
#define ReadBlobMSBFloats GmReadBlobMSBFloats
#define ReadBlobMSBLong GmReadBlobMSBLong
#define ReadBlobMSBLongs GmReadBlobMSBLongs
#define ReadBlobMSBShort GmReadBlobMSBShort
#define ReadBlobMSBShorts GmReadBlobMSBShorts
#define ReadBlobString GmReadBlobString
#define ReadBlobZC GmReadBlobZC
#define ReadImage GmReadImage
#define ReadInlineImage GmReadInlineImage
#define ReduceNoiseImage GmReduceNoiseImage
#define ReferenceBlob GmReferenceBlob
#define ReferenceCache GmReferenceCache
#define ReferenceImage GmReferenceImage
#define RegisterARTImage GmRegisterARTImage
#define RegisterAVIImage GmRegisterAVIImage
#define RegisterAVSImage GmRegisterAVSImage
#define RegisterBMPImage GmRegisterBMPImage
#define RegisterCALSImage GmRegisterCALSImage
#define RegisterCAPTIONImage GmRegisterCAPTIONImage
#define RegisterCINEONImage GmRegisterCINEONImage
#define RegisterCMYKImage GmRegisterCMYKImage
#define RegisterCUTImage GmRegisterCUTImage
#define RegisterDCMImage GmRegisterDCMImage
#define RegisterDCRAWImage GmRegisterDCRAWImage
#define RegisterDIBImage GmRegisterDIBImage
#define RegisterDPXImage GmRegisterDPXImage
#define RegisterEPTImage GmRegisterEPTImage
#define RegisterFAXImage GmRegisterFAXImage
#define RegisterFITSImage GmRegisterFITSImage
#define RegisterGIFImage GmRegisterGIFImage
#define RegisterGRADIENTImage GmRegisterGRADIENTImage
#define RegisterGRAYImage GmRegisterGRAYImage
#define RegisterHISTOGRAMImage GmRegisterHISTOGRAMImage
#define RegisterHRZImage GmRegisterHRZImage
#define RegisterHTMLImage GmRegisterHTMLImage
#define RegisterICONImage GmRegisterICONImage
#define RegisterIDENTITYImage GmRegisterIDENTITYImage
#define RegisterINFOImage GmRegisterINFOImage
#define RegisterJNXImage GmRegisterJNXImage
#define RegisterJP2Image GmRegisterJP2Image
#define RegisterJPEGImage GmRegisterJPEGImage
#define RegisterHEVCImage GmRegisterHEVCImage
#define RegisterLABELImage GmRegisterLABELImage
#define RegisterLOCALEImage GmRegisterLOCALEImage
#define RegisterLOGOImage GmRegisterLOGOImage
#define RegisterMACImage GmRegisterMACImage
#define RegisterMAPImage GmRegisterMAPImage
#define RegisterMATImage GmRegisterMATImage
#define RegisterMATTEImage GmRegisterMATTEImage
#define RegisterMETAImage GmRegisterMETAImage
#define RegisterMIFFImage GmRegisterMIFFImage
#define RegisterMONOImage GmRegisterMONOImage
#define RegisterMPCImage GmRegisterMPCImage
#define RegisterMPEGImage GmRegisterMPEGImage
#define RegisterMPRImage GmRegisterMPRImage
#define RegisterMSLImage GmRegisterMSLImage
#define RegisterMTVImage GmRegisterMTVImage
#define RegisterMVGImage GmRegisterMVGImage
#define RegisterMagickInfo GmRegisterMagickInfo
#define RegisterNULLImage GmRegisterNULLImage
#define RegisterOTBImage GmRegisterOTBImage
#define RegisterPALMImage GmRegisterPALMImage
#define RegisterPCDImage GmRegisterPCDImage
#define RegisterPCLImage GmRegisterPCLImage
#define RegisterPCXImage GmRegisterPCXImage
#define RegisterPDBImage GmRegisterPDBImage
#define RegisterPDFImage GmRegisterPDFImage
#define RegisterPICTImage GmRegisterPICTImage
#define RegisterPIXImage GmRegisterPIXImage
#define RegisterPLASMAImage GmRegisterPLASMAImage
#define RegisterPNGImage GmRegisterPNGImage
#define RegisterPNMImage GmRegisterPNMImage
#define RegisterPREVIEWImage GmRegisterPREVIEWImage
#define RegisterPS2Image GmRegisterPS2Image
#define RegisterPS3Image GmRegisterPS3Image
#define RegisterPSDImage GmRegisterPSDImage
#define RegisterPSImage GmRegisterPSImage
#define RegisterPWPImage GmRegisterPWPImage
#define RegisterRGBImage GmRegisterRGBImage
#define RegisterRLAImage GmRegisterRLAImage
#define RegisterRLEImage GmRegisterRLEImage
#define RegisterSCTImage GmRegisterSCTImage
#define RegisterSFWImage GmRegisterSFWImage
#define RegisterSGIImage GmRegisterSGIImage
#define RegisterSTEGANOImage GmRegisterSTEGANOImage
#define RegisterSUNImage GmRegisterSUNImage
#define RegisterSVGImage GmRegisterSVGImage
#define RegisterStaticModules GmRegisterStaticModules
#define RegisterTGAImage GmRegisterTGAImage
#define RegisterTIFFImage GmRegisterTIFFImage
#define RegisterTILEImage GmRegisterTILEImage
#define RegisterTIMImage GmRegisterTIMImage
#define RegisterTOPOLImage GmRegisterTOPOLImage
#define RegisterTTFImage GmRegisterTTFImage
#define RegisterTXTImage GmRegisterTXTImage
#define RegisterUILImage GmRegisterUILImage
#define RegisterURLImage GmRegisterURLImage
#define RegisterUYVYImage GmRegisterUYVYImage
#define RegisterVICARImage GmRegisterVICARImage
#define RegisterVIDImage GmRegisterVIDImage
#define RegisterVIFFImage GmRegisterVIFFImage
#define RegisterWBMPImage GmRegisterWBMPImage
#define RegisterWMFImage GmRegisterWMFImage
#define RegisterWPGImage GmRegisterWPGImage
#define RegisterXBMImage GmRegisterXBMImage
#define RegisterXCFImage GmRegisterXCFImage
#define RegisterXCImage GmRegisterXCImage
#define RegisterXImage GmRegisterXImage
#define RegisterXPMImage GmRegisterXPMImage
#define RegisterXWDImage GmRegisterXWDImage
#define RegisterYUVImage GmRegisterYUVImage
#define RemoveDefinitions GmRemoveDefinitions
#define RemoveFirstImageFromList GmRemoveFirstImageFromList
#define RemoveLastImageFromList GmRemoveLastImageFromList
#define ReplaceImageColormap GmReplaceImageColormap
#define ReplaceImageInList GmReplaceImageInList
#define ResetImagePage GmResetImagePage
#define ResetTimer GmResetTimer
#define ResizeFilterToString GmResizeFilterToString
#define ResizeImage GmResizeImage
#define ResolutionTypeToString GmResolutionTypeToString
#define ReverseImageList GmReverseImageList
#define RollImage GmRollImage
#define RotateImage GmRotateImage
#define SampleImage GmSampleImage
#define ScaleImage GmScaleImage
#define SeekBlob GmSeekBlob
#define SegmentImage GmSegmentImage
#define SetBlobClosable GmSetBlobClosable
#define SetBlobTemporary GmSetBlobTemporary
#define SetCacheView GmSetCacheView
#define SetCacheViewPixels GmSetCacheViewPixels
#define SetClientFilename GmSetClientFilename
#define SetClientName GmSetClientName
#define SetClientPath GmSetClientPath
#define SetDelegateInfo GmSetDelegateInfo
#define SetErrorHandler GmSetErrorHandler
#define SetExceptionInfo GmSetExceptionInfo
#define SetFatalErrorHandler GmSetFatalErrorHandler
#define SetGeometry GmSetGeometry
#define SetImage GmSetImage
#define SetImageEx GmSetImageEx
#define SetImageAttribute GmSetImageAttribute
#define SetImageChannelDepth GmSetImageChannelDepth
#define SetImageClipMask GmSetImageClipMask
#define SetImageColor GmSetImageColor
#define SetImageColorRegion GmSetImageColorRegion
#define SetImageDepth GmSetImageDepth
#define SetImageInfo GmSetImageInfo
#define SetImageOpacity GmSetImageOpacity
#define SetImagePixels GmSetImagePixels
#define SetImagePixelsEx GmSetImagePixelsEx
#define SetImageProfile GmSetImageProfile
#define SetImageType GmSetImageType
#define SetImageVirtualPixelMethod GmSetImageVirtualPixelMethod
#define SetLogEventMask GmSetLogEventMask
#define SetLogFormat GmSetLogFormat
#define SetLogMethod GmSetLogMethod
#define SetMagickInfo GmSetMagickInfo
#define SetMagickRegistry GmSetMagickRegistry
#define SetMagickResourceLimit GmSetMagickResourceLimit
#define SetMonitorHandler GmSetMonitorHandler
#define SetWarningHandler GmSetWarningHandler
#define ShadeImage GmShadeImage
#define SharpenImage GmSharpenImage
#define SharpenImageChannel GmSharpenImageChannel
#define ShaveImage GmShaveImage
#define ShearImage GmShearImage
#define SignatureImage GmSignatureImage
#define SolarizeImage GmSolarizeImage
#define SortColormapByIntensity GmSortColormapByIntensity
#define SpliceImageIntoList GmSpliceImageIntoList
#define SplitImageList GmSplitImageList
#define SpreadImage GmSpreadImage
#define SteganoImage GmSteganoImage
#define StereoImage GmStereoImage
#define StorageTypeToString GmStorageTypeToString
#define StretchTypeToString GmStretchTypeToString
#define StringToArgv GmStringToArgv
#define StringToChannelType GmStringToChannelType
#define StringToColorspaceType GmStringToColorspaceType
#define StringToCompositeOperator GmStringToCompositeOperator
#define StringToCompressionType GmStringToCompressionType
#define StringToDouble GmStringToDouble
#define StringToEndianType GmStringToEndianType
#define StringToFilterTypes GmStringToFilterTypes
#define StringToGravityType GmStringToGravityType
#define StringToHighlightStyle GmStringToHighlightStyle
#define StringToImageType GmStringToImageType
#define StringToInterlaceType GmStringToInterlaceType
#define StringToList GmStringToList
#define StringToMetricType GmStringToMetricType
#define StringToNoiseType GmStringToNoiseType
#define StringToPreviewType GmStringToPreviewType
#define StringToQuantumOperator GmStringToQuantumOperator
#define StringToResolutionType GmStringToResolutionType
#define StringToResourceType GmStringToResourceType
#define StringToVirtualPixelMethod GmStringToVirtualPixelMethod
#define Strip GmStrip
#define StripImage GmStripImage
#define StyleTypeToString GmStyleTypeToString
#define SubstituteString GmSubstituteString
#define SwirlImage GmSwirlImage
#define SyncCacheView GmSyncCacheView
#define SyncCacheViewPixels GmSyncCacheViewPixels
#define SyncImage GmSyncImage
#define SyncImagePixels GmSyncImagePixels
#define SyncImagePixelsEx GmSyncImagePixelsEx
#define SyncNextImageInList GmSyncNextImageInList
#define SystemCommand GmSystemCommand
#define TellBlob GmTellBlob
#define TextureImage GmTextureImage
#define ThresholdImage GmThresholdImage
#define ThrowLoggedException GmThrowLoggedException
#define ThumbnailImage GmThumbnailImage
#define TimeImageCommand GmTimeImageCommand
#define Tokenizer GmTokenizer
#define TransformColorspace GmTransformColorspace
#define TransformHSL GmTransformHSL
#define TransformHWB GmTransformHWB
#define TransformImage GmTransformImage
#define TransformRGBImage GmTransformRGBImage
#define TransformSignature GmTransformSignature
#define TranslateText GmTranslateText
#define TranslateTextEx GmTranslateTextEx
#define TransparentImage GmTransparentImage
#define UnlockSemaphoreInfo GmUnlockSemaphoreInfo
#define UnmapBlob GmUnmapBlob
#define UnregisterARTImage GmUnregisterARTImage
#define UnregisterAVIImage GmUnregisterAVIImage
#define UnregisterAVSImage GmUnregisterAVSImage
#define UnregisterBMPImage GmUnregisterBMPImage
#define UnregisterCALSImage GmUnregisterCALSImage
#define UnregisterCAPTIONImage GmUnregisterCAPTIONImage
#define UnregisterCINEONImage GmUnregisterCINEONImage
#define UnregisterCMYKImage GmUnregisterCMYKImage
#define UnregisterCUTImage GmUnregisterCUTImage
#define UnregisterDCMImage GmUnregisterDCMImage
#define UnregisterDCRAWImage GmUnregisterDCRAWImage
#define UnregisterDIBImage GmUnregisterDIBImage
#define UnregisterDPXImage GmUnregisterDPXImage
#define UnregisterEPTImage GmUnregisterEPTImage
#define UnregisterFAXImage GmUnregisterFAXImage
#define UnregisterFITSImage GmUnregisterFITSImage
#define UnregisterGIFImage GmUnregisterGIFImage
#define UnregisterGRADIENTImage GmUnregisterGRADIENTImage
#define UnregisterGRAYImage GmUnregisterGRAYImage
#define UnregisterHISTOGRAMImage GmUnregisterHISTOGRAMImage
#define UnregisterHRZImage GmUnregisterHRZImage
#define UnregisterHTMLImage GmUnregisterHTMLImage
#define UnregisterICONImage GmUnregisterICONImage
#define UnregisterIDENTITYImage GmUnregisterIDENTITYImage
#define UnregisterINFOImage GmUnregisterINFOImage
#define UnregisterJNXImage GmUnregisterJNXImage
#define UnregisterJP2Image GmUnregisterJP2Image
#define UnregisterJPEGImage GmUnregisterJPEGImage
#define UnregisterHEVCImage GmUnregisterHEVCImage
#define UnregisterLABELImage GmUnregisterLABELImage
#define UnregisterLOCALEImage GmUnregisterLOCALEImage
#define UnregisterLOGOImage GmUnregisterLOGOImage
#define UnregisterMACImage GmUnregisterMACImage
#define UnregisterMAPImage GmUnregisterMAPImage
#define UnregisterMATImage GmUnregisterMATImage
#define UnregisterMATTEImage GmUnregisterMATTEImage
#define UnregisterMETAImage GmUnregisterMETAImage
#define UnregisterMIFFImage GmUnregisterMIFFImage
#define UnregisterMONOImage GmUnregisterMONOImage
#define UnregisterMPCImage GmUnregisterMPCImage
#define UnregisterMPEGImage GmUnregisterMPEGImage
#define UnregisterMPRImage GmUnregisterMPRImage
#define UnregisterMSLImage GmUnregisterMSLImage
#define UnregisterMTVImage GmUnregisterMTVImage
#define UnregisterMVGImage GmUnregisterMVGImage
#define UnregisterMagickInfo GmUnregisterMagickInfo
#define UnregisterNULLImage GmUnregisterNULLImage
#define UnregisterOTBImage GmUnregisterOTBImage
#define UnregisterPALMImage GmUnregisterPALMImage
#define UnregisterPCDImage GmUnregisterPCDImage
#define UnregisterPCLImage GmUnregisterPCLImage
#define UnregisterPCXImage GmUnregisterPCXImage
#define UnregisterPDBImage GmUnregisterPDBImage
#define UnregisterPDFImage GmUnregisterPDFImage
#define UnregisterPICTImage GmUnregisterPICTImage
#define UnregisterPIXImage GmUnregisterPIXImage
#define UnregisterPLASMAImage GmUnregisterPLASMAImage
#define UnregisterPNGImage GmUnregisterPNGImage
#define UnregisterPNMImage GmUnregisterPNMImage
#define UnregisterPREVIEWImage GmUnregisterPREVIEWImage
#define UnregisterPS2Image GmUnregisterPS2Image
#define UnregisterPS3Image GmUnregisterPS3Image
#define UnregisterPSDImage GmUnregisterPSDImage
#define UnregisterPSImage GmUnregisterPSImage
#define UnregisterPWPImage GmUnregisterPWPImage
#define UnregisterRGBImage GmUnregisterRGBImage
#define UnregisterRLAImage GmUnregisterRLAImage
#define UnregisterRLEImage GmUnregisterRLEImage
#define UnregisterSCTImage GmUnregisterSCTImage
#define UnregisterSFWImage GmUnregisterSFWImage
#define UnregisterSGIImage GmUnregisterSGIImage
#define UnregisterSTEGANOImage GmUnregisterSTEGANOImage
#define UnregisterSUNImage GmUnregisterSUNImage
#define UnregisterSVGImage GmUnregisterSVGImage
#define UnregisterStaticModules GmUnregisterStaticModules
#define UnregisterTGAImage GmUnregisterTGAImage
#define UnregisterTIFFImage GmUnregisterTIFFImage
#define UnregisterTILEImage GmUnregisterTILEImage
#define UnregisterTIMImage GmUnregisterTIMImage
#define UnregisterTOPOLImage GmUnregisterTOPOLImage
#define UnregisterTTFImage GmUnregisterTTFImage
#define UnregisterTXTImage GmUnregisterTXTImage
#define UnregisterUILImage GmUnregisterUILImage
#define UnregisterURLImage GmUnregisterURLImage
#define UnregisterUYVYImage GmUnregisterUYVYImage
#define UnregisterVICARImage GmUnregisterVICARImage
#define UnregisterVIDImage GmUnregisterVIDImage
#define UnregisterVIFFImage GmUnregisterVIFFImage
#define UnregisterWBMPImage GmUnregisterWBMPImage
#define UnregisterWMFImage GmUnregisterWMFImage
#define UnregisterWPGImage GmUnregisterWPGImage
#define UnregisterXBMImage GmUnregisterXBMImage
#define UnregisterXCFImage GmUnregisterXCFImage
#define UnregisterXCImage GmUnregisterXCImage
#define UnregisterXImage GmUnregisterXImage
#define UnregisterXPMImage GmUnregisterXPMImage
#define UnregisterXWDImage GmUnregisterXWDImage
#define UnregisterYUVImage GmUnregisterYUVImage
#define UnsharpMaskImage GmUnsharpMaskImage
#define UnsharpMaskImageChannel GmUnsharpMaskImageChannel
#define UpdateSignature GmUpdateSignature
#define WaveImage GmWaveImage
#define WhiteThresholdImage GmWhiteThresholdImage
#define WriteBlob GmWriteBlob
#define WriteBlobByte GmWriteBlobByte
#define WriteBlobFile GmWriteBlobFile
#define WriteBlobLSBLong GmWriteBlobLSBLong
#define WriteBlobLSBShort GmWriteBlobLSBShort
#define WriteBlobMSBLong GmWriteBlobMSBLong
#define WriteBlobMSBShort GmWriteBlobMSBShort
#define WriteBlobString GmWriteBlobString
#define WriteBlobStringEOL GmWriteBlobStringEOL
#define WriteBlobStringWithEOL GmWriteBlobStringWithEOL
#define WriteImage GmWriteImage
#define WriteImages GmWriteImages
#define WriteImagesFile GmWriteImagesFile
#define ZoomImage GmZoomImage

#endif /* defined(PREFIX_MAGICK_SYMBOLS) */
#endif /* defined(_MAGICK_SYMBOLS_H) */

/*
 * Local Variables:
 * mode: c
 * c-basic-offset: 2
 * fill-column: 78
 * End:
 */
