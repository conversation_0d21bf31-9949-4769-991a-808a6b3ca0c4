/* magick/magick_config_api.h.  Generated from magick_config_api.h.in by configure.  */
/* Defines required by <magick/api.h> */

/* Define if you have X11 library */
/* #undef HasX11 */

/* Number of bits in a pixel Quantum (8/16/32) */
#define QuantumDepth 8

/* Define to 1 if your processor stores words with the most significant byte
   first (like Motorola and SPARC, unlike Intel and VAX). */
/* #undef WORDS_BIGENDIAN */

/* Prefix Magick library symbols with a common string. */
/* #undef PREFIX_MAGICK_SYMBOLS */

/* Define to `unsigned int' if <sys/types.h> does not define. */
/* #undef size_t */

/* Define to `int' if <sys/types.h> does not define. */
/* #undef ssize_t */
