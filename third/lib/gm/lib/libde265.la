# libde265.la - a libtool library file
# Generated by ltmain.sh (GNU libtool) 2.2.6b
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libde265.so.0'

# Names of this library.
library_names='libde265.so.0.0.10 libde265.so.0 libde265.so'

# The name of the static archive.
old_library='libde265.a'

# Linker flags that can not go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=' -lpthread /usr/local/lib/../lib64/libstdc++.la'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libde265.
current=0
age=0
revision=10

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/huimiao/server-multimedia-common/deps/lib'
