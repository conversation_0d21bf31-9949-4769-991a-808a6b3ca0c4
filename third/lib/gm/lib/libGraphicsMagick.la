# libGraphicsMagick.la - a libtool library file
# Generated by ltmain.sh (GNU libtool) 2.2.6b
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libGraphicsMagick.so.3'

# Names of this library.
library_names='libGraphicsMagick.so.3.15.1 libGraphicsMagick.so.3 libGraphicsMagick.so'

# The name of the static archive.
old_library='libGraphicsMagick.a'

# Linker flags that can not go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -L/usr/lib -L/home/<USER>/huimiao/server-multimedia-common/deps/lib/ -L/home/<USER>/huimiao/server-multimedia-common/deps/lib64/ -L/usr/lib64/ -L/usr/lib/ -lwebp -lwebpdemux -lyuv -lx265 -lxheif /usr/local/lib/../lib64/libstdc++.la -lrt -ldl /home/<USER>/server-multimedia-common/deps/lib/libde265.la -lregionBySaliency /home/<USER>/server-multimedia-common/srcs/../deps/lib/liblcms2.la /home/<USER>/server-multimedia-common/srcs/../deps/lib/libfreetype.la -ljpeg /usr/local/libpng/lib/libpng15.la -lbz2 -lxml2 -lz -lm -lpthread'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libGraphicsMagick.
current=18
age=15
revision=1

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/huimiao/server-multimedia-common/deps/lib'
