#!/bin/bash

# set -e
# set -x

function Show_usage 
{
    echo "Usage
    时间间隔取帧: $0 mode(0) bin_dir input_file output_path max_frames interval_second log_file
    场景切换取帧: $0 mode(2) bin_dir input_file output_path max_frames log_file
    帧数间隔取帧: $0 mode(1) bin_dir input_file output_path max_frames min_frames log_file
    信息提取GIF: $0 mode(3) bin_dir input_file output_path log_file
    文字水印   : $0 mode(4) bin_dir input_file output_path fontfile text fontsize x y color alpha shadow rotate fill log_file
    信息提取   : $0 mode(5) bin_dir input_file output_path log_file
    视频提取音频: $0 mode(6) bin_dir input_file output_path log_file
    视频信息解析: $0 mode(7) bin_dir input_file output_path log_file
    "
}

all_sig="～ ！ @ ＃ ％ … & ＊ （ ） ＋ － ＝ ｛ ｝ ｜ ［ ］ 、 ： “ ； ‘ 《 》 ？ ， 。 ／ "

function symbol2str_ff ()
{
    # echo $1
    # str=`echo $1 | tr -d '\r\n ' `  # del \r\n
    str=${1//"\\"/"\\\\\\\\"}     #  \ --> \\\\\\\\
    str=${str//\'/"\`"}             #  ' --> \`
    for sig in $all_sig; do
        str=${str//$sig/"\\$sig"}
    done
    str=${str//:/"\:"}              #  : --> \:
    str=${str//%/"\\\%"}            #  % --> \\\%
    echo $str
}


function interval_time_mode 
{
    if [[ $interv_sec -eq 0 ]]; then
        echo "Error: interval_second should never be 0" >> $log_file
        exit -1
    fi

    # 除法
    r_freq=`awk -v x=$interv_sec 'BEGIN{printf "%.2f\n",1/x}'`
    end_time=`awk -v x=$interv_sec -v y=$max_frames 'BEGIN{printf "%d\n",x*y-1}'`

    $ffmpeg_bin -loglevel quiet -i $input_file -r $r_freq -t $end_time -f image2 ${output_path}/%02d.jpg

    if [ $? -eq 0 ] ; then 
        echo "Success !!"
        exit 0
    else
        $ffmpeg_bin -i $input_file -r $r_freq -t $end_time -f image2 ${output_path}/%02d.jpg 2>>$log_file 1>>$log_file
        echo "Fail !!" >> $log_file
        exit -1
    fi
}

function interval_frames_mode
{
    $ffmpeg_bin -loglevel quiet -i $input_file  -vcodec copy -acodec copy -y $output_path/temp.mp4
    if [ ! $? -eq 0 ] ; then 
        $ffmpeg_bin -i $input_file  -vcodec copy -acodec copy -y $output_path/temp.mp4  2>>$log_file 1>>$log_file
        echo "Fail !!" >> $log_file
        exit -1
    fi
    # printf("invalid parameters\nusage: %s <video_file> <min_shot_frames> <max_shot_frames> <out_apth>\n", argv[1]);

    $trans_det_bin $output_path/temp.mp4 $min_frames $max_frames $output_path
    if [ $? -eq 0 ] ; then 
        echo "Success !!"
        rm -f $output_path/temp.mp4
        exit 0
    else
        echo "Fail !!" >> $log_file
        rm -f $output_path/temp.mp4
        exit -1
    fi
}

function trans_detection_mode
{
    $ffmpeg_bin -loglevel quiet -i $input_file  -vcodec copy -acodec copy -y $output_path/temp.mp4
    if [ ! $? -eq 0 ] ; then 
        $ffmpeg_bin -i $input_file  -vcodec copy -acodec copy -y $output_path/temp.mp4  2>>$log_file 1>>$log_file
        echo "Fail !!" >> $log_file
        exit -1
    fi
    $trans_det_bin $output_path/temp.mp4 0 $max_frames $output_path
    if [ $? -eq 0 ] ; then 
        echo "Success !!"
        rm -f $output_path/temp.mp4
        exit 0
    else
        echo "Fail !!" >> $log_file
        rm -f $output_path/temp.mp4
        exit -1
    fi
}

function parser_info_and_gif
{
    if [ -e $output_path/info.txt ]; then
        \rm -f $output_path/info.txt;
    fi

    $ffmpeg_bin -loglevel quiet -ss 0 -t 1 -i $input_file -vf scale=480:480:force_original_aspect_ratio=1 -r 12 -print_format $output_path/info.txt -y $output_path/output.gif
    if [ ! $? -eq 0 ] ; then
        if [ -e $output_path/info.txt ]; then
            exit 0
        fi
        $ffmpeg_bin -ss 0 -t 1 -i $input_file -vf scale=480:480:force_original_aspect_ratio=1 -r 12 -print_format $output_path/info.txt -y $output_path/output.gif  2>>$log_file 1>>$log_file
        echo "Fail !!" >> $log_file
        exit -1
    fi
    exit 0
}

function parser_json_info
{
    count_gif_frames=0
    if [ -e $output_path/info.json ]; then
        \rm -f $output_path/info.json;
    fi

    if [ ${input_file:0:4} = 'http' ]; then
        curl -s $input_file -H "Range: bytes=0-64" -o $output_path/temp.dat
	    if [ ! -e $output_path/temp.dat ]; then
	        echo "Curl range fail !!" >> $log_file
            exit -1
	    fi
        start8bytes=`hexdump -C $output_path/temp.dat | head -n1 | awk -F "  " '{print $2}'`
        if [[ $start8bytes == "47 49 46 38 "* ]]; then
            count_gif_frames=1
            curl -s $input_file -o $output_path/temp.dat
            if [ ! -e $output_path/temp.dat ]; then
	            echo "Curl file fail !!" >> $log_file
                exit -1
	        fi
            input_file=$output_path/temp.dat
        fi
    fi

    if [ $count_gif_frames -eq 1 ]; then
        $ffprobe_bin -loglevel error -count_frames -print_format json -show_format -show_streams -i $input_file > $output_path/info.json
    else
        $ffprobe_bin -loglevel error -print_format json -show_format -show_streams -i $input_file > $output_path/info.json
    fi

    if [ -e $output_path/temp.dat ]; then
    	rm -f $output_path/temp.dat
    fi
    if [ ! $? -eq 0 ] ; then
        # $ffprobe_bin -loglevel info -print_format json -show_format -show_streams -i $input_file > $output_path/info.json
        echo "Ffmpeg Fail !!" >> $log_file
        exit -1
    fi
    if [ ! -e $output_path/info.json ]; then
        echo "Json Not Exist Fail !!" >> $log_file
        exit -1
    fi
    if [ ! -s $output_path/info.json ]; then
        echo "Json Empty Fail !!" >> $log_file
        exit -1
    fi

    return 0
}

function str_len ()
{
    str=$1
    local len=0;
    let "len=`echo $str |tr -d '\r\n'| wc -m`"
    echo $len
}

function text_watermark
{

#    文字水印: $0 mode(4) ffmpeg trans_det_bin input_file output_path fontfile text fontsize x y color alpha shadow rotate fill log_file"
    if [ $fontsize -gt 2000 ]; then
        fontsize=2000
    fi

    let line_spacing=fontsize*4
    text=`symbol2str_ff "$text"`

    if [ $fill -eq 1 ]; then
#        time $ffmpeg_bin -loglevel error -i $input_file  -filter_complex    \
#            "color=color=black@0.0:size=100x100,format=rgba[c];\
#            [c][0]scale2ref='iw+ih':'iw+ih'[wm_bg][bg];\
#            [wm_bg]drawtext=fontfile=$fontfile:text='$text':line_spacing=$line_spacing:fill=1:\
#                 fontsize=$fontsize:fontcolor=$fontcolor@$alpha[wm_text]; \
#                 [wm_text]rotate=$rotate:ow=rotw($rotate):oh=roth($rotate):c=white@0[wm_rotate];\
#            [bg][wm_rotate]overlay='(main_w-overlay_w)/2':y='(main_h-overlay_h)/2':alpha=premultiplied:shortest=1[aa]" -map "[aa]"   \
#                  -pix_fmt yuvj420p -q:v 3 -y $output_path >> $log_file

        $ffmpeg_bin -loglevel error -i $input_file  -vf \
                "drawtext=fontfile=$fontfile:text='$text':line_spacing=$line_spacing:fill=1:angle=$rotate:\
                 fontsize=$fontsize:fontcolor=$fontcolor@$alpha" \
                  -pix_fmt yuvj420p -q:v 3 -y $output_path >> $log_file
#   $ffmpeg_bin -loglevel error -i $input_file -i ~/Pictures/transpance.png -filter_complex    \
#            "[1][0]scale2ref='iw+ih':'iw+ih'[wm_bg][bg];\
#            [wm_bg]drawtext=fontfile=$fontfile:text='$text':line_spacing=$line_spacing:fill=1:\
#                 fontsize=$fontsize:fontcolor=$fontcolor@$alpha[wm_text]; \
#                 [wm_text]rotate=$rotate:ow=rotw($rotate):oh=roth($rotate):c=white@0[wm_rotate];\
#            [bg][wm_rotate]overlay='(main_w-overlay_w)/2':y='(main_h-overlay_h)/2':alpha=premultiplied:shortest=1[aa]" -map "[aa]"   \
#                  -pix_fmt yuvj420p -q:v 3 -y $output_path >> $log_file


#        $ffmpeg_bin -loglevel error -i $input_file -i ~/Pictures/transpance.png -filter_complex    \
#            "color=0xff000000:100x100[c];\
#             [c][0]scale2ref='iw+ih':'iw+ih'[ct][mv];\
#             [ct]setsar=1,drawtext=fontfile=$fontfile:text='$text':line_spacing=$line_spacing:fill=1:\
#             fontsize=$fontsize:fontcolor=$fontcolor:alpha=$alpha,split[text][alpha];\
#             [text][alpha]alphamerge,rotate=$rotate:ow=rotw($rotate):oh=roth($rotate):c=white@0[txta]; \
#             [mv][txta]overlay=x='(main_w-overlay_w)/2':y='(main_h-overlay_h)/2':alpha=straight:shortest=1"\
#             -pix_fmt yuvj420p -q:v 3 -y $output_path >> $log_file
    else
        echo "fill=$fill not support" >> $log_file
#        $ffmpeg_bin -loglevel error -i $input_file -i ~/Pictures/transpance.png  -filter_complex    \
#            "[1][0]scale2ref='iw+ih':'iw+ih'[wm_bg][bg];\
#             [wm_bg]drawtext=fontfile=$fontfile:text='$text':line_spacing=$line_spacing:\
#                 fontsize=$fontsize:fontcolor=$fontcolor:alpha=$alpha[wm_text]; \
#             [wm_text]rotate=$rotate:ow=rotw($rotate):oh=roth($rotate):c=white@0[wm_rotate]; \
#             [bg][wm_rotate]overlay=x='min(0,-H*sin($rotate))+$pos_x':y='min(0,W*sin($rotate))+$pos_y':shortest=1[aa]" -map "[aa]"   \
#             -pix_fmt yuvj420p -q:v 3  -y $output_file >> $log_file

    fi

    exit 0
}

function video_extract_audio
{
    $ffmpeg_bin -loglevel error -i $input_file -vn $output_path >> $log_file
}

function video_info_parse
{
    $ffprobe_bin -v quiet -print_format json -show_format -show_streams -i $input_file > $output_path
}

if [ $# -lt 1 ] ; then
    echo "Error: input params "
    Show_usage
    exit -1
fi

mode=$1
bin_dir=$2
ffmpeg_bin=$2/ffmpeg
trans_det_bin=$2/trans_detection.bin
ffprobe_bin=$2/ffprobe
input_file=$3
output_path=$4
output_file=$4

if [[ $mode -eq 0 ]]; then
    max_frames=$5
    interv_sec=$6
    log_file=$7
elif [[ $mode -eq 2 ]]; then
    max_frames=$5
    log_file=$6
elif [[ $mode -eq 1 ]]; then
    max_frames=$5
    min_frames=$6
    log_file=$7
elif [[ $mode -eq 3 ]]; then
    log_file=$5
elif [[ $mode -eq 5 ]]; then
    log_file=$5
elif [[ $mode -eq 6 ]]; then
    log_file=$5
elif [[ $mode -eq 7 ]]; then
    log_file=$5
elif [[ $mode -eq 4 ]]; then
    if [ $# -lt 12 ] ; then
        Show_usage
        exit -1
    fi

    text=$6
    fontfile=$5
    fontsize=$7
    pos_x=$8
    pos_y=${9}
    fontcolor=${10}
    alpha=${11}
    # ffmpeg alpha to rgbA: AA = 255 * alpha * alpha
    alpha=$(awk -v x=$alpha 'BEGIN {printf "%.2f\n",sqrt(x)/10.0}')
    shadow=${12}
    rotate=${13}
    rotate=$(awk -v x=$rotate 'BEGIN {printf "%.2f\n",x*3.1415926/180.0}')
    fill=${14}
    log_file=${15}

else
    echo "Error: mode:$mode error"
    Show_usage
    exit -1
fi

if [ "X_"$log_file = "X_" ] ; then
    log_file=/dev/stderr
fi



if [[ $mode -eq 0 ]]; then
    interval_time_mode
elif [[ $mode -eq 1 ]]; then
    interval_frames_mode
elif [[ $mode -eq 2 ]]; then
    trans_detection_mode
elif [[ $mode -eq 3 ]]; then
    parser_info_and_gif
elif [[ $mode -eq 4 ]]; then
    text_watermark
elif [[ $mode -eq 5 ]]; then
    parser_json_info 0
elif [[ $mode -eq 6 ]]; then
    video_extract_audio
elif [[ $mode -eq 7 ]]; then
    video_info_parse
else
    echo "Error: mode error"
    Show_usage
    exit -1
fi
