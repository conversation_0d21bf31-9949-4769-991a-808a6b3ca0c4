
#!/bin/bash

cd /tmp
mkdir /tmp/test
cd /tmp/test

wget https://mdn.alipayobjects.com/afts_portal/afts/img/A*KvFwSq431NUAAAAAAAAAAAAADg15AQ/original -O /tmp/test/text.jpg
export LD_LIBRARY_PATH=LD_LIBRARY_PATH:/home/<USER>/imgsrv/third/lib/sal:/home/<USER>/imgsrv/third/lib/gm/lib:/home/<USER>/imgsrv/third/lib/ffmpeg:/home/<USER>/imgsrv/third/lib/common:/home/<USER>/imgsrv/third/lib/opencv/lib
export PATH=$PATH:/home/<USER>/imgsrv/third/bin
export MAGICK_CONFIGURE_PATH=/home/<USER>/imgsrv/third/config/gm/

/home/<USER>/imgsrv/third/bin/gm convert -gm_stat_path /home/<USER>/logs/imgsrv/stat/gm_stat.log /tmp/test/text.jpg  +profile "*" -auto-orient -resize "480x480" -quality 90  -define image:relative_qp -define hevc:numberOfThreads=2 /tmp/test/resize.jpg

/home/<USER>/imgsrv/third/bin/gm convert  -font /home/<USER>/imgsrv/third/font/fangzhengheiti.TTF -fill "#FFFFFF" -pointsize 50 -background transparent label:'Text测试预发' /tmp/test/text.jpg
/home/<USER>/imgsrv/third/bin/gm composite /tmp/test/text.jpg /tmp/test/resize.jpg -gravity Center -geometry +10+10 -dissolve 80 /tmp/test/out.jpg