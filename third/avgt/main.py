import subprocess
import sys
import os
import json
from typing import Tuple
import tools.stream_checker
import tools.ssim_checker

def check_command_exists(command: str) -> bool:
    try:
        result = subprocess.run([command, "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def process(params: str) -> str:
    result = {
        "errorCode": 0,
        "errorMsg": "success",
        "result": {}
    }

    ret = check_command_exists('ffmpeg')
    if not ret:
        result["errorCode"] = 1001
        result["errorMsg"] = f"ffmpeg is not installed"
        return json.dumps(result)
    
    try:
        inputs = json.loads(params)
    except ValueError:
        result["errorCode"] = 1003
        result["errorMsg"] = f"params is not json"
        return json.dumps(result)

    must_params = ["guard_tools", "target_video"]    
    for p in must_params:
        if p not in inputs:
            result["errorCode"] = 1004
            result["errorMsg"] = f"params is missing {p}"
            return json.dumps(result)
    
    target_video = inputs["target_video"]
    guard_tools = inputs["guard_tools"]

    if not os.path.exists(target_video):
        result["errorCode"] = 1002
        result["errorMsg"] = f"target video is not exist"
        return json.dumps(result)

    if "stream_checker" in guard_tools: 
        result["result"]["stream_checker"] = tools.stream_checker.try_decode(target_video)
    
    if "ssim_checker" in guard_tools:
        if "source_video" not in inputs:
            result["errorCode"] = 1004
            result["errorMsg"] = f"params is missing source_video"
            return json.dumps(result)
        source_video = inputs["source_video"]
        result["result"]["ssim_checker"] = tools.ssim_checker.get_ssim(target_video, source_video)

    return json.dumps(result)

if __name__ == "__main__":
    args = sys.argv
    res = process(args[1])
    print(res)