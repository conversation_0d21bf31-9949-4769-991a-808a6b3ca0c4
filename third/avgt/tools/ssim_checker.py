import subprocess
from sys import stdout
from typing import Tuple
import json
import re

def get_video_info(path : str) -> str :
    cmd = ["ffprobe", 
            "-v", "quiet", 
            "-print_format", "json", 
            "-show_streams", "-select_streams", "v",
            path]
    try:
        #log = subprocess.run(cmd, capture_output=True, text=True, check=True)
        log = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True,check=True)
        return log.stdout
    except subprocess.CalledProcessError as e:
        return ""

def get_ssim(tar: str, ref: str) -> dict:
    info = get_video_info(tar)

    res = {"ssim": "0", "error_code": 0, "error_msg":"success"}

    if (info == ""):
        res["error_code"] = 2202
        res["error_msg"] = "ffprobe run error"
        return res

    try:
        tmp = json.loads(info)
        width = tmp["streams"][0]["width"]
        height = tmp["streams"][0]["height"]
    except:
        res["error_code"] = 2203
        res["error_msg"] = "ffprobe output is not json"
        return res

    cmd = ["ffmpeg", 
            "-i", tar, "-i", ref, 
            "-filter_complex", f"[0:v]scale={width}x{height}[o0];[1:v]scale={width}x{height}[o1];[o0][o1]ssim", 
            "-f", "null", "-"]

    try:
        #log = subprocess.run(cmd, capture_output=True, text=True, check=True)
        log = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True,check=True)
        for line in log.stderr.split("\n"):
            if "SSIM" in line:
                # print(line)
                match = re.search(r'SSIM Y:(\d+\.\d+)', line)
                ssim_y = float(match.group(1))  # 提取到的 SSIM Y 值
    except:
        res["error_code"] = 2202
        res["error_msg"] = "ffmpeg run error"
        return res
    
    res["ssim"] = ssim_y

    if ssim_y < 0.3:
        res["error_code"] = 2201
        res["error_msg"] = "ssim is not good"

    return res
