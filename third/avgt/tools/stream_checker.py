import subprocess
from typing import <PERSON><PERSON>

def try_decode(input : str) -> dict:
    cmd = ["ffmpeg", 
            "-i", input, 
            "-loglevel", "error", 
            "-f", "null", "-"]

    try :
        #log = subprocess.run(cmd, capture_output=True, text=True, check=True)
        log = subprocess.run(cmd,stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, check=True)
        if "Invalid NAL unit size" in log.stderr:
            return {"error_code":2101, "error_msg":"Invalid NAL unit size"}
        if "Could not find ref" in log.stderr:
            return {"error_code":2102, "error_msg":"Count not find ref frame"}
    except subprocess.CalledProcessError as e:
        return {"error_code":2103, "error_msg":"Decode run failed"}
    return {"error_code":0, "error_msg":"success"}
