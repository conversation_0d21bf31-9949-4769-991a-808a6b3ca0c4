#!/bin/bash
#Author: wen<PERSON> <EMAIL>

proc_name="imgsrv"
proc_path="/home/<USER>/${proc_name}"
conf_file_path="${proc_path}/conf/imgsrv.conf"

if [ $# -lt 1 ]
then
    echo "Usage: $0 log_path_conf [reserver_days]"
    exit 1
fi

log_path_conf=$1

reserve_days=7
if [ $# -gt 1 ]
then
    reserve_days=$2
fi

if [ ! -f "${conf_file_path}" ]
then
    echo "Conf file is missing!"
    exit 1
fi

find /home/<USER>/logs/imgsrv/log/ -type f -name "*.log.*" -mtime +7 -exec rm -f {} \;
find /home/<USER>/logs/imgsrv/self_avif -mindepth 1 -maxdepth 1 -type d -mtime +1 -exec rm -rf {} \;

log_file=$(grep -w "$log_path_conf" ${conf_file_path} | awk -F "\"" '{print $2}')
tmp_log_path=$(dirname ${log_file})
log_path=${tmp_log_path}
if [[ ${tmp_log_path} == /* ]]
then
    log_path=${tmp_log_path}
else
    log_path="${proc_path}/${tmp_log_path}"
fi

if [ ! -d "${log_path}" ]
then
    echo "log path error!"
    exit 1
fi

log_file_prefix=$(basename "${log_file}")


cd ${log_path}
#1.move yesterday's logs to a folder named by date
yesterday=$(date +"%Y%m%d" -d "-1 days")
if [ ! -d "${yesterday}" ]
then
    mkdir "${yesterday}"
fi

logs_to_move=$(ls | grep "${log_file_prefix}.${yesterday}")
for i in ${logs_to_move}
do
    mv "${i}" "${yesterday}"
done

#2.recycle old folder and reserve $reserve_days days log folders
all_log_folder=$(ls -F | grep '/$' | awk -F "/" '{print $1}' | grep "[[:digit:]]" | sort)
folder_num=$(echo "$all_log_folder" | wc -l)
if [ "${folder_num}" -gt "${reserve_days}" ]
then
    rm_num=$(expr "${folder_num}" - "${reserve_days}")
    rm_folder=$(echo "$all_log_folder" | sed -n 1,"${rm_num}"p)
    for i in ${rm_folder}
    do
        rm -rf "${i}"
    done
fi
