#!/bin/bash

imgsrv="imgsrv"
watcher="imgsrv_wd"
img_process="img_proc"

bin_name=${imgsrv}
proc_path="/home/<USER>/${bin_name}"
conf_file_path="./conf/imgsrv.conf"
stop_time=1
reserve_days=1
core_path="/home/<USER>/logs"
corepattern="core.%e.%p.%h.%t"
#MB
tmpfs_size=2048

if [ $# -ne 1 ]; then
    echo "Usage: $0 [start|stop|restart|status|version]"
    exit 0
fi

function mount_tmpfs() {
    if [ ! -f ${conf_file_path} ]; then
        echo "Conf file is missing!"
        exit 1
    fi

    path=$(grep "out_file_path" ${conf_file_path} | grep -v "//" | grep -v "#" | awk -F "\"" '{print $2}')
    if [ ! -d ${path} ]; then
        mkdir -p ${path}
    else
        rst=$(findmnt ${path} | grep tmpfs | wc -l)
        if [ ${rst} -ge 1 ]; then
            return
        fi
    fi
    sudo mount -t tmpfs -o size=${tmpfs_size}M tmpfs ${path}
}

function umount_tmpfs() {
    if [ ! -f ${conf_file_path} ]; then
        echo "Conf file is missing!"
        exit 1
    fi
    path=$(grep "out_file_path" ${conf_file_path} | grep -v "//" | grep -v "#" | awk -F "\"" '{print $2}')
    if [ -d ${path} ]; then
        sudo umount ${path}
    fi
}

function log_recycle() {
    #Move last day's log every day at 1 AM
    log_job_num=$(crontab -l | grep "${bin_name}_log_recycle.sh" | wc -l)
    if [ ${log_job_num} -lt 1 ]; then
        crontab -l | {
            cat
            echo "0 1 * * * ${proc_path}/shell/${bin_name}_log_recycle.sh log_path ${reserve_days} >/dev/null 2>&1"
            echo "0 1 * * * ${proc_path}/shell/${bin_name}_log_recycle.sh ce_log_path ${reserve_days} >/dev/null 2>&1"
            echo "0 1 * * * ${proc_path}/shell/${bin_name}_log_recycle.sh stat_path ${reserve_days} >/dev/null 2>&1"
            echo "0 1 * * * ${proc_path}/shell/imgsrvgm_log_recycle.sh ${reserve_days} >/dev/null 2>&1"
            echo "0 1 * * * rm -f ${proc_path}/img_srv_nohup.log"
        } | crontab -
    fi

#    stat_job_num=$(crontab -l | grep "${bin_name}_stat_recycle.sh" | wc -l)
#    if [ ${stat_job_num} -lt 1 ]; then
#        crontab -l | {
#            cat
#            echo "0 1 * * * ${proc_path}/shell/${bin_name}_stat_recycle.sh ${reserve_days} >/dev/null 2>&1"
#        } | crontab -
#    fi
}

function rm_log_recycle() {
    ret=$(crontab -l | grep "${bin_name}_log_recycle.sh" | grep -v grep | wc -l)
    if [ ${ret} -ge 1 ]; then
        crontab -l | grep -v "${bin_name}_log_recycle.sh" | grep -v "img_srv_nohup.log" | crontab -
    fi
    ret=$(crontab -l | grep "imgsrvgm_log_recycle.sh" | grep -v grep | wc -l)
    if [ ${ret} -ge 1 ]; then
        crontab -l | grep -v "imgsrvgm_log_recycle.sh" | grep -v "img_srv_nohup.log" | crontab -
    fi

#    ret=$(crontab -l | grep "${bin_name}_stat_recycle.sh" | grep -v grep | wc -l)
#    if [ ${ret} -ge 1 ]; then
#        crontab -l | grep -v "${bin_name}_stat_recycle.sh" | crontab -
#    fi
}

function monitor() {
    #Monitor process every 1 min
    monitor_job_num=$(crontab -l | grep "${bin_name}_monitor.sh" | wc -l)
    if [ ${monitor_job_num} -lt 1 ]; then
        crontab -l | {
            cat
            echo "*/1 * * * * ${proc_path}/shell/${bin_name}_monitor.sh >/dev/null 2>&1"
        } | crontab -
    fi
}

function rm_monitor() {
    ret=$(crontab -l | grep "${bin_name}_monitor.sh" | grep -v grep | wc -l)
    if [ ${ret} -ge 1 ]; then
        crontab -l | grep -v "${bin_name}_monitor.sh" | crontab -
    fi
}

function proc_started() {
    if [ $# -ne 1 ]; then
        echo "Wrong param"
        return 1
    fi
    local proc=$1
    local proc_num
    proc_num=$(pgrep -x ${proc} | wc -l)
    if [ ${proc_num} -lt 1 ]; then
        return 1
    fi
    return 0
}

function proc_id() {
    if [ $# -ne 1 ]; then
        echo "Wrong param"
        return 1
    fi
    local proc=$1
    local pid=$(pgrep -x ${proc})
    echo ${pid}
    local proc_num=$(echo ${pid} | awk '{print NF}')
    if [ ${proc_num} -ne 1 ]; then
        return 1
    fi
    return 0
}

function start() {
    proc_started ${watcher}
    if [ $? -eq 0 ]; then
        echo "${watcher} already started!"
        exit 0
    else
        proc_started ${imgsrv}
        if [ $? -eq 0 ]; then
            stop
        fi
    fi
    chown admin:admin -R /home/<USER>/imgsrv
    chown admin:admin -R /home/<USER>/logs
    mount_tmpfs

    sudo bash -c 'echo '''${core_path}/${corepattern}''' > /proc/sys/kernel/core_pattern'
    export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$(pwd)/third/lib/sal:$(pwd)/third/lib/opencv/lib:$(pwd)/third/lib/common
    nohup ./bin/${bin_name} -d ${watcher} -w ${imgsrv} -p ${img_process} -c ${core_path} \
    >>img_srv_nohup.log 2>&1 </dev/null &
    sleep ${stop_time}

    proc_started ${watcher}
    if [ $? -eq 0 ]; then
        echo "Starts successfully!"
    else
        echo "Starts failed!"
        return
    fi
    log_recycle
    monitor
}

function stop() {
    proc_started ${watcher}
    if [ $? -eq 0 ]; then
        local watch_id=$(proc_id ${watcher})
        if [ $? -eq 0 ]; then
            kill -SIGUSR1 ${watch_id}
        else
            killall -KILL ${watcher}
            killall -KILL ${imgsrv}
        fi
    else
        proc_started ${imgsrv}
        if [ $? -eq 0 ]; then
            local pid=$(proc_id ${imgsrv})
            if [ $? -eq 0 ]; then
                kill -SIGQUIT ${pid}
            else
                killall -KILL ${imgsrv}
            fi
        fi
    fi
    killall -KILL ${imgsrv}
    rm_log_recycle
    rm_monitor
    umount_tmpfs
    echo "Shut down successfully!"
}

function status() {
    proc_started ${watcher}
    if [ $? -eq 0 ]; then
        echo "${watcher} : $(proc_id ${watcher}) is running!"
    else
        echo "${watcher} is stopped!"
    fi

    proc_started ${imgsrv}
    if [ $? -eq 0 ]; then
        echo "${imgsrv} : $(proc_id ${imgsrv}) is running!"
    else
        echo "${imgsrv} is stopped!"
    fi
}

case "$1" in
"start")
    start
    exit 0
    ;;

"stop")
    stop
    exit 0
    ;;

"restart")
    stop
    start
    exit 0
    ;;

"status")
    status
    exit 0
    ;;

"version")
    export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$(pwd)/third/lib/sal:$(pwd)/third/lib/opencv/lib:$(pwd)/third/lib/common
    ./bin/${bin_name} -v
    exit 0
    ;;

*)
    echo "Unknown command!"
    ;;

esac
