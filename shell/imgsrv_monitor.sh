#!/bin/bash
#Author: wen<PERSON> <EMAIL>

imgsrv="imgsrv"
watcher="imgsrv_wd"
img_process="img_proc"

bin_name="imgsrv"
proc_path="/home/<USER>/${bin_name}"
file_name="monitor.log"
pid=0
conf_file_path="${proc_path}/conf/imgsrv.conf"
#log_path="${proc_path}/log"


if [ ! -f "${conf_file_path}" ]
then
    echo "Conf file is missing!"
    exit 1
fi
log_file=$(grep "log_path" ${conf_file_path} | grep -v "//" | grep -v "#" | awk -F "\"" '{print $2}')
tmp_log_path=$(dirname "${log_file}")
log_path=${tmp_log_path}
if [[ ${tmp_log_path} == /* ]]
then
    log_path=${tmp_log_path}
else
    log_path="${proc_path}/${tmp_log_path}"
fi

if [ ! -d "${log_path}" ]
then
    echo "log path error!"
    exit 1
fi

#proc_num() {
#    num=$(ps -ef | grep ${proc_name} | grep -v grep | grep -v ".sh" | wc -l)
#    return "${num}"
#}

#proc_num() {
#    if [ ! -f "${conf_file_path}" ]
#    then
#        echo "Conf file is missing!"
#        exit 1
#    fi
#    port=$(grep -w port ${conf_file_path}  | grep -v "//" | awk -F "=" '{print $2}' | awk -F ";" '{print $1}')
#    ret=$(netstat -nlp 2>/dev/null | grep -w "${port}" | wc -l)
#    return "${ret}"
#}

#proc_id() {
#    if [ ! -f "${conf_file_path}" ]
#    then
#        echo "Conf file is missing!"
#        exit 1
#    fi
#    port=$(grep -w port ${conf_file_path}  | grep -v "//" | awk -F "=" '{print $2}' | awk -F ";" '{print $1}')
#    pid=$(netstat -nlp 2>/dev/null | grep -w "${port}" | awk '{print $7}' | awk -F "/" '{print $1}')
#}

function proc_num() {
    if [ $# -ne 1 ]; then
        echo "Wrong param"
        return 1
    fi
    local proc=$1
    local pid=$(pgrep -x ${proc})
    local proc_num=$(echo ${pid} | awk '{print NF}')
    echo ${proc_num}
}

function proc_id() {
    if [ $# -ne 1 ]; then
        echo "Wrong param"
        return 1
    fi
    local proc=$1
    local pid=$(pgrep -x ${proc})
    local proc_num=$(echo ${pid} | awk '{print NF}')
    if [ ${proc_num} -ne 1 ]; then
        return 1
    fi
    echo ${pid}
    return 0
}

function proc_started() {
    if [ $# -ne 1 ]; then
        echo "Wrong param"
        return 1
    fi
    local proc=$1
    local proc_num
    proc_num=$(pgrep -x ${proc} | wc -l)
    if [ ${proc_num} -lt 1 ]; then
        return 1
    fi
    return 0
}

function check_start() {
    proc_started ${watcher}
    if [ $? -ne 0 ]; then
        cd ${proc_path}; ./imgsrv_control.sh start
        sleep 1

        proc_started ${watcher}
        if [ $? -ne 0 ]; then
            echo $(date "+%Y-%m-%d %H:%M:%S"), "Monitor starts failed!" >> "${log_path}/${file_name}"
        else
            echo $(date "+%Y-%m-%d %H:%M:%S"), "Monitor starts successfully, pid: $(proc_id ${imgsrv})" >>\
            "${log_path}/${file_name}"
        fi
    fi
}


conf_img_proc_num=$(grep "process_num" ${conf_file_path} | grep -v "//" | grep -v "#" | awk -F "=" '{print $2}')
critcal_proc_num=$(expr ${conf_img_proc_num} / 2)
p_num=$(proc_num ${img_process})
if [ ${p_num} -lt ${critcal_proc_num} ]; then
    cd ${proc_path};
    ./imgsrv_control.sh stop
    ./imgsrv_control.sh start
    sleep 1
    check_start
fi

check_start





#proc_num
#number=$?
#if [ ${number} -eq 0 ]
#then
#    cd ${proc_path}; ./mm_cache_server_control.sh start
#    sleep 1
#
#    proc_num
#    number=$?
#    if [ ${number} -eq 0 ]
#    then
#        echo $(date "+%Y-%m-%d %H:%M:%S"), "Monitor starts failed!" >> "${log_path}/${file_name}"
#    else
#        proc_id
#        echo $(date "+%Y-%m-%d %H:%M:%S"), "Monitor starts successfully, pid:", "${pid}" >> "${log_path}/${file_name}"
#    fi
#
#fi


#mem_util_threshold=57
#
#util=`tsar --check --mem | awk '{print $8}' | awk -F "=" '{print $2}'`
##if [[ `expr ${util} \> ${mem_util_threshold}` -ne 0 ]]
#rst=`echo "${util} > ${mem_util_threshold}" | bc`
#if [ ${rst} -eq 1 ]
#then
#    cd ${proc_path};
#    ./mm_cache_server_control.sh stop
#    killall -9 ${proc_name}
#    ./mm_cache_server_control.sh start
#
#    proc_num
#    number=$?
#    if [ ${number} -eq 0 ]
#    then
#        echo `date "+%Y-%m-%d %H:%M:%S"`, "Monitor starts failed!" >> "${log_path}/${file_name}"
#    else
#        proc_id
#        echo `date "+%Y-%m-%d %H:%M:%S"`, "Monitor starts successfully, pid:", ${pid} >> "${log_path}/${file_name}"
#    fi
#fi