#!/bin/bash
#Author: wen<PERSON> <EMAIL>

if [ $# -lt 1 ]
then
    echo "Usage: $0 log_path_conf [reserver_days]"
    exit 1
fi

reserve_days=7
if [ $# -gt 1 ]
then
    reserve_days=$2
fi

stat_log_path="/home/<USER>/logs/imgsrv/stat/"
stat_log="/home/<USER>/logs/imgsrv/stat/gm_stat.log"


if [ ! -d "${stat_log_path}" ]
then
    echo "sdk log path error!"
    exit 1
fi


function clean_log
{
    log_path_regex=$1".*"

    log_rm_num=0
    log_num=$(ls -t ${log_path_regex} | wc -l)
    if [ ${log_num} -gt ${reserve_days} ]; then
        log_rm_num=$(expr "${log_num}" - "${reserve_days}")
        logs_to_rm=$(ls -t ${log_path_regex} | tail -"${log_rm_num}")
        for i in ${logs_to_rm}
        do
            rm -rf ${i}
        done
    fi
}

function truncate_log
{
    log_file=$1
    cp "$log_file" "${log_file}."`date +%Y%m%d`
    truncate -s 1024 "$log_file"
}

truncate_log $stat_log
clean_log $stat_log
